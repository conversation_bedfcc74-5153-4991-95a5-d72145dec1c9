TITLE: Build Solana Versioned Transaction for Jupiter Swap (TypeScript)
DESCRIPTION: This function constructs a Solana Versioned Transaction (V0) from Jupiter Aggregator swap instructions. It deserializes various instruction types, including compute budget, setup, swap, and cleanup, then compiles them into a V0 message. The function requires a wallet's public key, a recent blockhash, and address lookup table accounts to build the transaction.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/100-swap-api/7-requote-with-lower-max-accounts.md#_snippet_1

LANGUAGE: typescript
CODE:
```
async function buildSwapTransaction(swapInstructionsResponse) {
    const {
        tokenLedgerInstruction,
        computeBudgetInstructions,
        setupInstructions,
        swapInstruction,
        cleanupInstruction,
        addressLookupTableAccounts,
    } = swapInstructionsResponse;

    const messageV0 = new TransactionMessage({
        payerKey: wallet.publicKey,
        recentBlockhash: (await connection.getLatestBlockhash()).blockhash,
        instructions: [
            deserializeInstruction(tokenLedgerInstruction),
            ...(computeBudgetInstructions?.map(deserializeInstruction).filter(Boolean) || []),
            ...(setupInstructions?.map(deserializeInstruction).filter(Boolean) || []),
            deserializeInstruction(swapInstruction),
            ...(cleanupInstruction ? [deserializeInstruction(cleanupInstruction)].filter(Boolean) : []),
        ].filter(Boolean),
    }).compileToV0Message(addressLookupTableAccounts);

    const transaction = new VersionedTransaction(messageV0);

    return transaction;
}
```

----------------------------------------

TITLE: Prepare and Sign Solana Swap Transaction
DESCRIPTION: This snippet demonstrates how to prepare a Solana swap transaction received from the Jupiter Swap API for sending. It involves deserializing the base64-encoded transaction into a VersionedTransaction object, signing it with a provided wallet, and then re-serializing it into a binary Uint8Array format suitable for network submission. The input is a swapTransaction string in base64 format, and the output is a signed, serialized transaction ready to be sent.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/100-swap-api/3-send-swap-transaction.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
const transactionBase64 = swapResponse.swapTransaction
const transaction = VersionedTransaction.deserialize(Buffer.from(transactionBase64, 'base64'));
console.log(transaction);

transaction.sign([wallet]);

const transactionBinary = transaction.serialize();
console.log(transactionBinary);
```

----------------------------------------

TITLE: Get a Quote for Token Pair (JavaScript)
DESCRIPTION: This snippet demonstrates how to fetch a quote for a specific token pair (SOL to USDC) using the Jupiter Swap API. It sends a GET request with inputMint, outputMint, amount, and slippageBps parameters. The response provides details about the potential swap.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/100-swap-api/1-get-quote.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
const quoteResponse = await (
    await fetch(
        'https://lite-api.jup.ag/swap/v1/quote?inputMint=So11111111111111111111111111111111111111112&outputMint=EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v&amount=********0&slippageBps=50&restrictIntermediateTokens=true'
    )
  ).json();

console.log(JSON.stringify(quoteResponse, null, 2));
```

----------------------------------------

TITLE: Sign Solana Transaction with JavaScript
DESCRIPTION: This snippet demonstrates how to deserialize a base64-encoded transaction, sign it using a wallet, and then serialize it back to base64 format. It uses the Solana `web3.js` v1 library and assumes `createOrderResponse` contains the transaction.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/103-recurring-api/2-execute-order.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
// ... GET /createOrder's response

// Extract the transaction from the order response
const transactionBase64 = createOrderResponse.transaction

// Deserialize the transaction
const transaction = VersionedTransaction.deserialize(Buffer.from(transactionBase64, 'base64'));

// Sign the transaction
transaction.sign([wallet]);

// Serialize the transaction to base64 format
const signedTransaction = Buffer.from(transaction.serialize()).toString('base64');
```

----------------------------------------

TITLE: Constructing a System Program Transfer Instruction - JavaScript
DESCRIPTION: This snippet creates a `SystemProgram.transfer` instruction. This instruction is used to transfer a specified amount of lamports (1000 in this example) from the `wallet.publicKey` to the `referralWalletPublicKey`, intended for a referral fee.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs_versioned_docs/version-old/8-additional-topics/1-composing-with-versioned-transaction.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
// construct the transfer instruction
const transferInstruction = SystemProgram.transfer({
    fromPubkey: wallet.publicKey,
    toPubkey: referralWalletPublicKey,
    lamports: 1000,
  }),
```

----------------------------------------

TITLE: Complete Jupiter Limit Order Creation and Execution Flow - JavaScript
DESCRIPTION: This comprehensive snippet illustrates the entire process of interacting with the Jupiter Limit Order API, from initial setup to transaction confirmation. It includes importing necessary Solana Web3 and Anchor libraries, establishing a connection, generating keypairs, fetching a transaction from the API, deserializing and signing it, adding a compute budget priority fee, and finally sending and confirming the transaction on the Solana network.
SOURCE: https://github.com/jup-ag/docs/blob/main/Moved/1-limit-order-api.md#_snippet_16

LANGUAGE: JavaScript
CODE:
```
import { Connection, Keypair, Transaction, ComputeBudgetProgram } from "@solana/web3.js";
import fetch from "cross-fetch";
import { Wallet } from "@project-serum/anchor";
import bs58 from "bs58";

// This RPC endpoint is only for demonstration purposes so it may not work.
const connection = new Connection(
  "https://neat-hidden-sanctuary.solana-mainnet.discover.quiknode.pro/2af5315d336f9ae920028bbb90a73b724dc1bbed/"
);

// Base key are used to generate a unique order id
const base = Keypair.generate();

// get serialized transaction
const { tx } = await (
  await fetch('https://jup.ag/api/limit/v1/createOrder', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      owner: wallet.publicKey.toString(),
      inAmount: 100000, // 1000000 => 1 USDC if inputToken.address is USDC mint
      outAmount: 100000,
      inputMint: inputMint.toString(),
      outputMint: outputMint.toString(),
      expiredAt: null, // new Date().valueOf() / 1000,
      base: base.publicKey.toString(),
      // referralAccount and name are both optional.
      // Please provide both to get referral fees.
      // More details in the section below.
      // referralAccount: referralPublicKey,
      // referralName: "Referral Name"
    })
  })
).json();

// deserialize the transaction
const transactionBuf = Buffer.from(tx, "base64");
var transaction = Transaction.deserialize(transactionBuf);

// add priority fee
const addPriorityFee = ComputeBudgetProgram.setComputeUnitPrice({
  microLamports: 1, // probably need to be higher for the transaction to be included on chain.
});
transaction.add(addPriorityFee);

// sign the transaction using the required key
// for create order, wallet and base key are required.
transaction.sign([wallet.payer, base]);

// Execute the transaction
const rawTransaction = transaction.serialize();
const txid = await connection.sendRawTransaction(rawTransaction, {
  skipPreflight: true,
  maxRetries: 2,
});
await connection.confirmTransaction(txid);
console.log(`https://solscan.io/tx/${txid}`);
```

----------------------------------------

TITLE: Jupiter Flash Fill Transaction Flow
DESCRIPTION: A Flash Fill transaction is designed to optimize account usage by leveraging Versioned Transactions and Address Lookup Tables (ALTs). It involves a sequence of steps for managing wSOL accounts and performing the token swap, ensuring efficient resource utilization.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/100-swap-api/2-build-swap-transaction.md#_snippet_5

LANGUAGE: APIDOC
CODE:
```
Transaction Steps:
1. Borrow enough SOL for opening the wSOL account from this program.
2. Create the wSOL account for the borrower.
3. Swap X token to wSOL.
4. Close the wSOL account and send it to the borrower.
5. Repay the SOL for opening the wSOL account back to this program.
```

----------------------------------------

TITLE: Implement Solana Transaction Size Retry Logic (TypeScript)
DESCRIPTION: This code implements a retry mechanism to generate a Solana transaction that fits within size limits. It iteratively attempts to build a transaction, reducing the number of `maxAccounts` if the transaction is too large or fails. The loop continues until a valid-sized transaction is created or a maximum number of attempts (`MAX_ACCOUNTS`) is reached.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/100-swap-api/7-requote-with-lower-max-accounts.md#_snippet_3

LANGUAGE: typescript
CODE:
```
let counter = 0;
let transactionTooLarge = true;
let quoteResponse, swapInstructionsResponse, transaction;

while (transactionTooLarge && counter < MAX_ACCOUNTS) {
    try {
        console.log(`Attempting with maxAccounts: ${MAX_ACCOUNTS - counter}`);

        quoteResponse = await getQuote(MAX_ACCOUNTS - counter);
        swapInstructionsResponse = await getSwapInstructions(quoteResponse);
        transaction = await buildSwapTransaction(swapInstructionsResponse);
        transactionTooLarge = await checkTransactionSize(transaction);

        if (transactionTooLarge) {
            console.log(`Transaction too large (with ${MAX_ACCOUNTS - counter} maxAccounts), retrying with fewer accounts...`);
            counter++;
        } else {
            console.log(`Transaction size OK with ${MAX_ACCOUNTS - counter} maxAccounts`);
        }

    } catch (error) {
        console.error('Error in attempt:', error);
        counter += 2; // Incrementing by 1 account each time will be time consuming, you can use a higher counter
        transactionTooLarge = true;
    }
}

if (transactionTooLarge) {
    console.error('Failed to create transaction within size limits after all attempts');
} else {
    console.log('Success! Transaction is ready for signing and sending');

    // After, you can add your transaction signing and sending logic
}
```

----------------------------------------

TITLE: Build Serialized Swap Transaction with Jupiter Swap API (JavaScript)
DESCRIPTION: This JavaScript code demonstrates how to make a POST request to the Jupiter Swap API to obtain a serialized swap transaction. It takes a "quoteResponse" and "userPublicKey" as mandatory inputs, and optionally includes parameters like "dynamicComputeUnitLimit", "dynamicSlippage", and "prioritizationFeeLamports" to optimize for transaction landing. The snippet returns a JSON object containing the "swapTransaction" string and other relevant transaction details.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/100-swap-api/2-build-swap-transaction.md#_snippet_0

LANGUAGE: jsx
CODE:
```
const swapResponse = await (
await fetch('https://lite-api.jup.ag/swap/v1/swap', {
    method: 'POST',
    headers: {
    'Content-Type': 'application/json',
    },
    body: JSON.stringify({
    quoteResponse,
    userPublicKey: wallet.publicKey,

    // ADDITIONAL PARAMETERS TO OPTIMIZE FOR TRANSACTION LANDING
    // See next guide to optimize for transaction landing
    dynamicComputeUnitLimit: true,
    dynamicSlippage: true,
    prioritizationFeeLamports: {
          priorityLevelWithMaxLamports: {
            maxLamports: 1000000,
            priorityLevel: "veryHigh"
          }
        }
    })
})
).json();

console.log(swapResponse);
```

----------------------------------------

TITLE: JavaScript: Create Time-based Recurring Order via Jupiter API
DESCRIPTION: This JavaScript example shows how to programmatically create a time-based recurring order using the Jupiter Recurring API. It constructs a POST request body with `user`, `inputMint`, `outputMint`, and `time` parameters, then fetches the order transaction. This requires a `wallet.publicKey` and a network connection to the Jupiter API.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/103-recurring-api/1-create-order.md#_snippet_3

LANGUAGE: jsx
CODE:
```
const createOrderResponse = await (
    await fetch('https://lite-api.jup.ag/recurring/v1/createOrder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            user: wallet.publicKey,
            inputMint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            outputMint: "So11111111111111111111111111111111111111112",
            params: {
                time: {
                    inAmount: 104000000, // Raw amount of input token to deposit now (before decimals)
                    numberOfOrders: 2, // Total number of orders to execute
                    interval: 86400, // Time between each order in unix seconds
                    minPrice: null, // Minimum price or null
                    maxPrice: null, // Maximum price or null
                    startAt: null, // Unix timestamp of start time or null - null starts immediately
                },
            },
        }),
    })
).json();
```

----------------------------------------

TITLE: Configure Solana Transaction Priority Fees
DESCRIPTION: This section details the settings for transaction priority fees on Jupiter, influencing execution queue placement. Options include various broadcasting methods (RPC, Jito, Mixed), priority levels (Fast, Turbo, Ultra), and fee modes (Max Cap, Exact Fee). Use with caution, especially during congestion, and remember to re-adjust fees.
SOURCE: https://github.com/jup-ag/docs/blob/main/Moved/1-swap.md#_snippet_3

LANGUAGE: APIDOC
CODE:
```
TransactionPriorityFees:
  TransactionBroadcastingSelector: string
    description: Selects how transactions are broadcasted.
    options: "RPC with priority fee" | "Jito Validators via bundle" | "Mixed (both)"
  PriorityLevel: string
    description: Specifies the transaction's priority.
    options: "Fast" | "Turbo" | "Ultra"
  FeeMode: string
    description: Defines how the fee is applied.
    options: "Max Cap" | "Exact Fee"
```

----------------------------------------

TITLE: Fetching Swap Transaction with Dynamic CU Estimation (JavaScript)
DESCRIPTION: This JavaScript snippet shows how to request a swap transaction from the Jupiter Aggregator API with dynamic compute unit (CU) estimation enabled. By setting `dynamicComputeUnitLimit` to `true`, the transaction will utilize an optimized CU limit based on simulation, rather than the standard maximum, improving efficiency.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs_versioned_docs/version-old/2-apis/11-landing-transactions.md#_snippet_7

LANGUAGE: JavaScript
CODE:
```
const { swapTransaction } = await (
  await fetch('https://quote-api.jup.ag/v6/swap', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      quoteResponse,
      userPublicKey: wallet.publicKey.toString(),
      dynamicComputeUnitLimit: true
    })
  })
).json();
```

----------------------------------------

TITLE: Send Solana Raw Transaction to Network with JavaScript
DESCRIPTION: This snippet demonstrates how to manually send a signed Solana transaction to the network using `connection.sendRawTransaction`. It includes steps for deserializing, signing, serializing, fetching a recent blockhash, and confirming the transaction. Error handling is also included.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/103-recurring-api/2-execute-order.md#_snippet_4

LANGUAGE: JavaScript
CODE:
```
const transactionBase64 = createOrderResponse.transaction
const transaction = VersionedTransaction.deserialize(Buffer.from(transactionBase64, 'base64'));

transaction.sign([wallet]);

const transactionBinary = transaction.serialize();

const blockhashInfo = await connection.getLatestBlockhashAndContext({ commitment: "finalized" });

const signature = await connection.sendRawTransaction(transactionBinary, {
    maxRetries: 1,
    skipPreflight: true
});

const confirmation = await connection.confirmTransaction({
signature,
blockhash: blockhashInfo.value.blockhash,
lastValidBlockHeight: blockhashInfo.value.lastValidBlockHeight,
}, "finalized");

if (confirmation.value.err) {
    throw new Error(`Transaction failed: ${JSON.stringify(confirmation.value.err)}\n\nhttps://solscan.io/tx/${signature}`);
} else console.log(`Transaction successful: https://solscan.io/tx/${signature}`);
```

----------------------------------------

TITLE: Executing Solana Transactions with Confirmation (JavaScript)
DESCRIPTION: This code snippet shows how to serialize a signed VersionedTransaction and send it to the Solana network using 'connection.sendRawTransaction'. It includes options for skipping preflight checks and retries. After sending, it waits for transaction confirmation using the latest block hash and the transaction ID, then logs the Solscan URL.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs_versioned_docs/version-old/2-apis/1-swap-api.md#_snippet_6

LANGUAGE: JavaScript
CODE:
```
// get the latest block hash
const latestBlockHash = await connection.getLatestBlockhash();

// Execute the transaction
const rawTransaction = transaction.serialize()
const txid = await connection.sendRawTransaction(rawTransaction, {
  skipPreflight: true,
  maxRetries: 2
});
await connection.confirmTransaction({
 blockhash: latestBlockHash.blockhash,
 lastValidBlockHeight: latestBlockHash.lastValidBlockHeight,
 signature: txid
});
console.log(`https://solscan.io/tx/${txid}`);
```

----------------------------------------

TITLE: Manually Send Solana Transaction with JavaScript
DESCRIPTION: This snippet demonstrates how to manually send a signed Solana transaction to the network using the Solana `web3.js` library. It covers deserializing the transaction, signing it, serializing it, fetching a recent blockhash, sending the raw transaction, and confirming its final status on the blockchain.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/102-trigger-api/2-execute-order.md#_snippet_3

LANGUAGE: jsx
CODE:
```
const transactionBase64 = createOrderResponse.transaction
const transaction = VersionedTransaction.deserialize(Buffer.from(transactionBase64, 'base64'));

transaction.sign([wallet]);

const transactionBinary = transaction.serialize();

const blockhashInfo = await connection.getLatestBlockhashAndContext({ commitment: "finalized" });

const signature = await connection.sendRawTransaction(transactionBinary, {
    maxRetries: 1,
    skipPreflight: true
});

const confirmation = await connection.confirmTransaction({
signature,
blockhash: blockhashInfo.value.blockhash,
lastValidBlockHeight: blockhashInfo.value.lastValidBlockHeight,
}, "finalized");

if (confirmation.value.err) {
    throw new Error(`Transaction failed: ${JSON.stringify(confirmation.value.err)}\n\nhttps://solscan.io/tx/${signature}`);
} else console.log(`Transaction successful: https://solscan.io/tx/${signature}`);
```

----------------------------------------

TITLE: Initializing RPC Connection with Solana web3.js in JavaScript
DESCRIPTION: This snippet initializes a connection to the Solana blockchain's mainnet-beta RPC endpoint using the `Connection` class from `@solana/web3.js`. It establishes a basic connection for interacting with the network, though for production, using a dedicated third-party RPC provider is recommended for reliability and performance.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/1-environment-setup.md#_snippet_0

LANGUAGE: jsx
CODE:
```
const connection = new Connection('https://api.mainnet-beta.solana.com');
```

----------------------------------------

TITLE: Sending and Confirming Solana Raw Transaction (JavaScript)
DESCRIPTION: This code sends the prepared raw Solana transaction to the network using `connection.sendRawTransaction`. It configures `maxRetries` and `preflightCommitment` for robustness. After sending, it waits for transaction confirmation and logs the outcome, including a Solscan link for successful or failed transactions.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/100-swap-api/5-payments-through-swap.md#_snippet_8

LANGUAGE: jsx
CODE:
```
const signature = await connection.sendRawTransaction(transactionBinary, {
    maxRetries: 10,
    preflightCommitment: "finalized",
});

const confirmation = await connection.confirmTransaction({ signature }, "finalized");

if (confirmation.value.err) {
    throw new Error(`Transaction failed: ${JSON.stringify(confirmation.value.err)}\nhttps://solscan.io/${signature}/`);
} else console.log(`Transaction successful: https://solscan.io/tx/${signature}/`);
```

----------------------------------------

TITLE: Execute Jupiter Recurring Order via API with JavaScript
DESCRIPTION: This code makes a POST request to the Jupiter Recurring API's `/execute` endpoint. It sends the signed transaction and the `requestId` obtained from the `createOrder` response. This allows Jupiter to handle the transaction execution on your behalf.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/103-recurring-api/2-execute-order.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
const executeResponse = await (
    await fetch('https://lite-api.jup.ag/recurring/v1/execute', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            signedTransaction: signedTransaction,
            requestId: createOrderResponse.requestId,
        }),
    })
).json();
```

----------------------------------------

TITLE: Jupiter Swap API: Get Quote 200 OK Response Schema
DESCRIPTION: Describes the successful response structure for the Jupiter Swap API's /quote endpoint. This schema outlines the details of the calculated swap quote, including input/output amounts, swap mode, slippage, price impact, and the detailed route plan.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/api/swap-api/quote.api.mdx#_snippet_2

LANGUAGE: APIDOC
CODE:
```
{
  "type": "object",
  "required": [
    "inputMint",
    "outputMint",
    "inAmount",
    "outAmount",
    "otherAmountThreshold",
    "swapMode",
    "slippageBps",
    "priceImpactPct",
    "routePlan"
  ],
  "properties": {
    "inputMint": {
      "type": "string"
    },
    "inAmount": {
      "type": "string"
    },
    "outputMint": {
      "type": "string"
    },
    "outAmount": {
      "type": "string",
      "description": "- Calculated output amount from routing engine\n- The value includes platform fees and DEX fees, excluding slippage\n"
    },
    "otherAmountThreshold": {
      "type": "string",
      "description": "- Calculated minimum output amount after accounting for `slippageBps` on the `outAmount` value\n- Not used by `/swap` endpoint to build transaction\n"
    },
    "swapMode": {
      "required": true,
      "type": "string",
      "enum": [
        "ExactIn",
        "ExactOut"
      ],
      "title": "SwapMode"
    },
    "slippageBps": {
      "type": "integer",
      "format": "int32"
    },
    "platformFee": {
      "type": "object",
      "properties": {
        "amount": {
          "type": "string"
        },
        "feeBps": {
          "type": "integer",
          "format": "int32"
        }
      },
      "title": "PlatformFee"
    },
    "priceImpactPct": {
      "type": "string"
    },
    "routePlan": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "swapInfo": {
            "type": "object",
            "required": [
              "ammKey",
              "inputMint",
              "outputMint",
              "inAmount",
              "outAmount",
              "feeAmount",
              "feeMint"
            ],
            "properties": {
              "ammKey": {
                "type": "string"
              },
              "label": {
                "type": "string"
              },
              "inputMint": {
                "type": "string"
              },
              "outputMint": {
                "type": "string"
              },
              "inAmount": {
                "type": "string"
              },
              "outAmount": {
                "type": "string"
              },
              "feeAmount": {
                "type": "string"
              },
              "feeMint": {
                "type": "string"
              }
            },
            "title": "SwapInfo"
          },
          "percent": {
            "type": "integer",
            "format": "int32"
          }
        },
        "required": [
          "swapInfo",
          "percent"
        ],
        "title": "RoutePlanStep"
      }
    },
    "contextSlot": {
      "type": "number"
    },
    "timeTaken": {
      "type": "number"
    }
  },
  "title": "QuoteResponse"
}
```

----------------------------------------

TITLE: Initializing Jupiter Terminal with Package Import (TypeScript)
DESCRIPTION: This React component initializes the Jupiter Terminal by dynamically importing the `@jup-ag/terminal` package within a `useEffect` hook. It then calls the `init` function from the imported module to render the terminal as a widget within a designated HTML element, also importing its CSS.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/300-tool-kits/terminal/nextjs-app-example.md#_snippet_7

LANGUAGE: typescript
CODE:
```
"use client";

import React, { useEffect } from "react";
import "@jup-ag/terminal/css";

export default function TerminalComponent() {
  useEffect(() => {
    import("@jup-ag/terminal").then((mod) => {
      const { init } = mod;
      init({
        displayMode: "widget",
        integratedTargetId: "jupiter-terminal",
      });
    });
  }, []);

  return (
    <div>
      <h1>Jupiter Terminal Demo</h1>
      <div id="jupiter-terminal" />
    </div>
  );
}
```

----------------------------------------

TITLE: Embedding Jupiter Terminal Script in Next.js App Router
DESCRIPTION: This code snippet demonstrates how to embed the Jupiter Terminal script (`main-v4.js`) into a Next.js 13+ application using the App Router. The `next/script` component is used within the `RootLayout` to load the script `beforeInteractive`, ensuring it's available early for the terminal's initialization and functionality.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/300-tool-kits/terminal/nextjs-app-example.md#_snippet_3

LANGUAGE: typescript
CODE:
```
import Script from "next/script";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <Script
          src="https://terminal.jup.ag/main-v4.js"
          strategy="beforeInteractive"
          data-preload
          defer
        />
      </head>
      <body>{children}</body>
    </html>
  );
}
```

----------------------------------------

TITLE: Wrapping Application with UnifiedWalletProvider - JSX
DESCRIPTION: This JSX snippet demonstrates how to wrap your React application with the `<UnifiedWalletProvider />` component. This provider is essential for making wallet functionalities available throughout your app, configuring initial settings like auto-connection, environment, metadata, and notification callbacks.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/300-tool-kits/wallet-kit/README.md#_snippet_1

LANGUAGE: jsx
CODE:
```
const ExampleBaseOnly = () => {
  return (
    <UnifiedWalletProvider
      wallets={[]}
      config={{
        autoConnect: false,
        env: 'mainnet-beta',
        metadata: {
          name: 'UnifiedWallet',
          description: 'UnifiedWallet',
          url: 'https://jup.ag',
          iconUrls: ['https://jup.ag/favicon.ico']
        },
        notificationCallback: WalletNotification,
        walletlistExplanation: {
          href: 'https://station.jup.ag/docs/old/additional-topics/wallet-list'
        }
      }}
    >
      <UnifiedWalletButton />
    </UnifiedWalletProvider>
  );
};

export default ExampleBaseOnly;
```

----------------------------------------

TITLE: Executing Swap with Referral Fee Account (Lite API) - JavaScript
DESCRIPTION: This snippet demonstrates how to execute a swap transaction using Jupiter's Lite Swap API, including passing a 'feeAccount' for referral fees. It requires a 'quoteResponse' and the user's public key.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/100-swap-api/4-add-fees-to-swap.md#_snippet_4

LANGUAGE: jsx
CODE:
```
const swapResponse = await (
    await fetch('https://lite-api.jup.ag/swap/v1/swap', {
        method: 'POST',
        headers: {
        'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            quoteResponse,
            userPublicKey: wallet.publicKey.toBase58(), // Pass in actual referred user in production
            feeAccount: feeAccount,
        })
    })
).json();
```

----------------------------------------

TITLE: Sending Solana Transaction to Blockchain - JavaScript
DESCRIPTION: This asynchronous function is responsible for deserializing the base64-encoded swap transaction, setting the recent blockhash and fee payer, and signing it with the provided user keypair. It then performs a simulation to validate the transaction's success before finally sending it to the Solana blockchain. The function includes important considerations for commitment levels and retries to optimize for speed and reliability in a trading context.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs_versioned_docs/version-old/2-apis/2-payments-api.md#_snippet_7

LANGUAGE: JavaScript
CODE:
```
// Step 3: Send the transaction to the Solana blockchain
async function sendTransaction(swapTransaction, swapUserKeypair, lastValidBlockHeight) {
  const transaction = VersionedTransaction.deserialize(Buffer.from(swapTransaction, 'base64'));

  // Get the recent blockhash
  // Using 'finalized' commitment to ensure the blockhash is final and secure
  // You may experiment with 'processed' or 'confirmed' for fetching blockhash to increase speed
  // Reference: https://solana.com/docs/oldrpc/http/getlatestblockhash
  const bhInfo = await connection.getLatestBlockhashAndContext({ commitment: "finalized" });
  transaction.recentBlockhash = bhInfo.value.blockhash;
  transaction.feePayer = swapUserKeypair.publicKey;

  // Sign the transaction with the swap user's keypair
  transaction.sign([swapUserKeypair]);

  // Simulate the transaction to ensure it will succeed
  // Using 'finalized' commitment for the simulation to match the security level of the actual send
  // You may experiment with 'confirmed' or 'processed' to simulate faster, but keep in mind the risks
  // Reference: https://solana.com/docs/oldcore/transactions#commitment
  const simulation = await connection.simulateTransaction(transaction, { commitment: "finalized" });
  if (simulation.value.err) {
    throw new Error(`Simulation failed: ${simulation.value.err.toString()}`);
  }

  // Send the transaction
  try {
    const signature = await connection.sendTransaction(transaction, {
      // NOTE: Adjusting maxRetries to a lower value for trading, as 20 retries can be too much
      // Experiment with different maxRetries values based on your tolerance for slippage and speed

```

----------------------------------------

TITLE: JavaScript: Create Price-based Recurring Order via Jupiter API
DESCRIPTION: This JavaScript example demonstrates creating a price-based recurring order through the Jupiter Recurring API. It prepares a POST request with `user`, `inputMint`, `outputMint`, and `price` parameters, then sends it to the API to receive the order transaction. This snippet assumes `wallet.publicKey` is available and requires network access to the Jupiter API.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/103-recurring-api/1-create-order.md#_snippet_4

LANGUAGE: jsx
CODE:
```
const createOrderResponse = await (
    await fetch('https://lite-api.jup.ag/recurring/v1/createOrder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            user: wallet.publicKey,
            inputMint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
            outputMint: "So11111111111111111111111111111111111111112",
            params: {
                price: {
                    depositAmount: 1********, // Raw amount of input token to deposit now (before decimals)
                    incrementUsdcValue: ********, // Raw amount of USDC to increment per cycle (before decimals)
                    interval: 86400, // Time between each cycle in unix seconds
                    startAt: null, // Unix timestamp of start time or null - null starts immediately
                },
            },
        }),
    })
).json();
```

----------------------------------------

TITLE: Sign Solana Transaction with Web3.js
DESCRIPTION: This JavaScript snippet demonstrates how to deserialize a base64-encoded transaction received from the Jupiter Ultra API, sign it using a Solana `Keypair`, and then serialize it back to base64 format. This signed transaction is required for execution.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/101-ultra-api/2-execute-order.md#_snippet_3

LANGUAGE: JavaScript
CODE:
```
// ... GET /order's response

// Extract the transaction from the order response
const transactionBase64 = orderResponse.transaction

// Deserialize the transaction
const transaction = VersionedTransaction.deserialize(Buffer.from(transactionBase64, 'base64'));

// Sign the transaction
transaction.sign([wallet]);

// Serialize the transaction to base64 format
const signedTransaction = Buffer.from(transaction.serialize()).toString('base64');
```

----------------------------------------

TITLE: Cancel a Single Jupiter Trigger Order using JavaScript
DESCRIPTION: This code snippet demonstrates how to initiate a cancellation for a single Jupiter Trigger order. It sends a POST request to the `/cancelOrder` endpoint, specifying the `maker` address and the `order` account to be cancelled. The response will contain a transaction that needs to be signed and broadcasted.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/102-trigger-api/3-cancel-order.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
const cancelOrderResponse = await (
    await fetch('https://lite-api.jup.ag/trigger/v1/cancelOrder', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            maker: "jdocuPgEAjMfihABsPgKEvYtsmMzjUHeq9LX4Hvs7f3",
            computeUnitPrice: "auto",
            order: "********************************************"
        })
    })
).json();
```

----------------------------------------

TITLE: Initiate Swap Transaction with Jupiter Ultra API /order
DESCRIPTION: Describes the initial step for a swap using Jupiter Ultra API, where the /order endpoint is used to request a swap transaction, which then needs to be signed by the user.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/101-ultra-api/README.md#_snippet_14

LANGUAGE: APIDOC
CODE:
```
Endpoint: /ultra/v1/order
Description: Request for a swap transaction then sign it.
```

----------------------------------------

TITLE: Get Token Information for a Specific Mint Address using Jupiter Token API
DESCRIPTION: This snippet demonstrates how to fetch detailed information for a specific token mint address. It uses the `lite-api.jup.ag` endpoint to retrieve data such as token name, symbol, decimals, logo, tags, and volume. The response includes helpful details like `freeze_authority` and `permanent_delegate` to aid in informed decision-making.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/501-token-api/README.md#_snippet_0

LANGUAGE: jsx
CODE:
```
const tokenInfoResponse = await (
    await fetch('https://lite-api.jup.ag/tokens/v1/token/JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN')
).json();

console.log(tokenInfoResponse);
```

LANGUAGE: json
CODE:
```
{
    "address": "JUPyiwrYJFskUPiHa7hkeR8VUtAeFoSYbKedZNsDvCN",
    "name": "Jupiter",
    "symbol": "JUP",
    "decimals": 6,
    "logoURI": "https://static.jup.ag/jup/icon.png",
    "tags": [ "verified", "strict", "community", "birdeye-trending" ],
    "daily_volume": 79535977.0513354,
    "created_at": "2024-04-26T10:56:58.893768Z",
    "freeze_authority": null,
    "mint_authority": null,
    "permanent_delegate": null,
    "minted_at": "2024-01-25T08:54:23Z",
    "extensions": { "coingeckoId": "jupiter-exchange-solana" }
}
```

----------------------------------------

TITLE: Perform and Manage Jupiter Aggregator Swaps in JavaScript
DESCRIPTION: This snippet demonstrates the end-to-end process of performing a swap using the Jupiter Aggregator, with a focus on managing transaction size. It includes functions to fetch quotes, retrieve swap instructions, and construct a versioned transaction, highlighting the use of `maxAccounts` for optimization.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/100-swap-api/7-requote-with-lower-max-accounts.md#_snippet_0

LANGUAGE: JavaScript
CODE:
```
import {
    AddressLookupTableAccount,
    Connection,
    Keypair,
    PublicKey,
    TransactionInstruction,
    TransactionMessage,
    VersionedTransaction,
} from '@solana/web3.js';

// Set up dev environment
import fs from 'fs';
const privateKeyArray = JSON.parse(fs.readFileSync('/Path/to/key', 'utf8').trim());
const wallet = Keypair.fromSecretKey(new Uint8Array(privateKeyArray));
const connection = new Connection('your-own-rpc');

// Recommended
const MAX_ACCOUNTS = 64

async function getQuote(maxAccounts) {
    const params = new URLSearchParams({
        inputMint: 'insert-mint',
        outputMint: 'insert-mint',
        amount: '1000000',
        slippageBps: '100',
        maxAccounts: maxAccounts.toString()
    });

    const url = `https://lite-api.jup.ag/swap/v1/quote?${params}`;
    const response = await fetch(url);

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const quoteResponse = await response.json();

    if (quoteResponse.error) {
        throw new Error(`Jupiter API error: ${quoteResponse.error}`);
    }

    return quoteResponse;
};

async function getSwapInstructions(quoteResponse) {
    const response = await fetch('https://lite-api.jup.ag/swap/v1/swap-instructions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            quoteResponse: quoteResponse,
            userPublicKey: wallet.publicKey.toString(),
            prioritizationFeeLamports: {
                priorityLevelWithMaxLamports: {
                    maxLamports: ********,
                    priorityLevel: "veryHigh"
                }
            },
            dynamicComputeUnitLimit: true,
        }, null, 2)
    });

    if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const swapInstructionsResponse = await response.json();

    if (swapInstructionsResponse.error) {
        throw new Error(`Jupiter API error: ${swapInstructionsResponse.error}`);
    }

    return swapInstructionsResponse;
};

async function buildSwapTransaction(swapInstructionsResponse) {
    const {
        computeBudgetInstructions,
        setupInstructions,
        swapInstruction,
        cleanupInstruction,
        addressLookupTableAddresses,
    } = swapInstructionsResponse;

    const deserializeInstruction = (instruction) => {
        if (!instruction) return null;
        return new TransactionInstruction({
            programId: new PublicKey(instruction.programId),
            keys: instruction.accounts.map((key) => ({
                pubkey: new PublicKey(key.pubkey),
                isSigner: key.isSigner,
                isWritable: key.isWritable,
            })),
            data: Buffer.from(instruction.data, "base64"),
        });
    };

    const getAddressLookupTableAccounts = async (
        keys
    ) => {
        const addressLookupTableAccountInfos =
            await connection.getMultipleAccountsInfo(
                keys.map((key) => new PublicKey(key))
            );

        return addressLookupTableAccountInfos.reduce((acc, accountInfo, index) => {
            const addressLookupTableAddress = keys[index];
            if (accountInfo) {
                const addressLookupTableAccount = new AddressLookupTableAccount({
                    key: new PublicKey(addressLookupTableAddress),
                    state: AddressLookupTableAccount.deserialize(accountInfo.data),
                });
                acc.push(addressLookupTableAccount);
            }

            return acc;
        }, []);
    };

    const addressLookupTableAccounts = [];
    addressLookupTableAccounts.push(
        ...(await getAddressLookupTableAccounts(addressLookupTableAddresses))
    );

    const blockhash = (await connection.getLatestBlockhash()).blockhash;

    // Create transaction message with all instructions
    const messageV0 = new TransactionMessage({
        payerKey: wallet.publicKey,
        recentBlockhash: blockhash,
        instructions: [
```

----------------------------------------

TITLE: Jupiter Swap API: Get Quote
DESCRIPTION: Initiate a swap by requesting a quote from the Jupiter Swap API. This step provides the optimal route plan, along with parameters like integrator fees and slippage, necessary for building the subsequent swap transaction.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/100-swap-api/README.md#_snippet_0

LANGUAGE: APIDOC
CODE:
```
Method: GET /quote
Description: Requests a quote for a token swap.
Returns: Route plan, integrator fee, slippage, and other swap parameters.
Related Guide: /docs/swap-api/get-quote
```

----------------------------------------

TITLE: Initializing Jupiter Terminal in React with useEffect - TypeScript
DESCRIPTION: This TypeScript React component demonstrates how to initialize the Jupiter Terminal using the `window.Jupiter.init()` method within a `useEffect` hook. The terminal is configured to display as a 'widget' and integrate into a specific DOM element identified by `jupiter-terminal`, ensuring it's set up once the component mounts.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/300-tool-kits/terminal/react-app-example.md#_snippet_4

LANGUAGE: typescript
CODE:
```
import React, { useEffect } from 'react';
import './App.css';
import './types/terminal.d';

export default function App() {
  useEffect(() => {
    // Initialize terminal
    window.Jupiter.init({
      displayMode: "widget",
      integratedTargetId: "jupiter-terminal",
    });
  }, []);

  return (
    <div className="App">
      <h1>Jupiter Terminal Demo</h1>
      <div id="jupiter-terminal" />
    </div>
  );
}
```

----------------------------------------

TITLE: Initializing Jupiter Terminal for Fixed SOL Swap (JavaScript)
DESCRIPTION: This example demonstrates initializing Jupiter Terminal for a fixed SOL swap. It sets the `displayMode` to 'integrated' and targets a specific HTML element. The `formProps` are configured to pre-select SOL as the input and USDC as the output, and to fix the input mint to SOL, preventing users from changing the input token. This is useful for scenarios requiring a specific input token.
SOURCE: https://github.com/jup-ag/docs/blob/main/docs/300-tool-kits/terminal/customization.md#_snippet_7

LANGUAGE: JavaScript
CODE:
```
window.Jupiter.init({
  displayMode: "integrated",
  integratedTargetId: "swap-container",
  formProps: {
    initialInputMint: "So11111111111111111111111111111111111111112", // SOL
    initialOutputMint: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", // USDC
    fixedMint: "So11111111111111111111111111111111111111112"
  }
});
```
