TITLE: Install PM2 globally
DESCRIPTION: Instructions for installing PM2 globally using either npm or Bun package managers. Note: A symlink might be needed for Bun if it's the sole Node.js runtime.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
$ npm install pm2 -g
```

LANGUAGE: bash
CODE:
```
$ bun install pm2 -g
```

----------------------------------------

TITLE: Start an application with PM2
DESCRIPTION: Demonstrates how to start any application (Node.js, Bun, Python, Ruby, binaries in $PATH) with PM2, ensuring it is daemonized, monitored, and kept alive forever.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
$ pm2 start app.js
```

----------------------------------------

TITLE: PM2 Startup Scripts: Generate and Manage
DESCRIPTION: Generate, save, and remove PM2 startup scripts to ensure processes persist across server restarts. Compatible with systemd, upstart, launchd, and rc.d init systems.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_12

LANGUAGE: bash
CODE:
```
# Generate Startup Script
$ pm2 startup

# Freeze your process list across server restart
$ pm2 save

# Remove Startup Script
$ pm2 unstartup
```

----------------------------------------

TITLE: Start Node.js application in PM2 Cluster Mode
DESCRIPTION: Command to start a Node.js application in PM2's Cluster mode, leveraging multiple CPU cores for load balancing and increased performance and reliability. The number of processes can be specified or set to 'max' for all available CPUs.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_6

LANGUAGE: bash
CODE:
```
$ pm2 start api.js -i <processes>
```

----------------------------------------

TITLE: Perform zero-downtime reload for PM2 applications
DESCRIPTION: Command to hot reload all applications managed by PM2 without any downtime, allowing for seamless updates.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_7

LANGUAGE: bash
CODE:
```
$ pm2 reload all
```

----------------------------------------

TITLE: Control PM2 Application States
DESCRIPTION: Commands for starting applications with custom names or arguments, enabling watch mode for auto-restart on file changes, starting bash scripts or JSON configurations, resetting counters, and stopping, restarting, or deleting applications by name or ID.
SOURCE: https://github.com/unitech/pm2/blob/master/pres/TMP.md#_snippet_4

LANGUAGE: bash
CODE:
```
pm2 start app.js --name=\"api\" # Start application and name it \"api\"
pm2 start app.js -- -a 34     # Start app and pass option \"-a 34\" as argument
pm2 start app.js --watch      # Restart application on file change
pm2 start script.sh           # Start bash script
pm2 start app.json            # Start all applications declared in app.json
pm2 reset [app-name]          # Reset all counters
pm2 stop all                  # Stop all apps
pm2 stop 0                    # Stop process with id 0
pm2 restart all               # Restart all apps
pm2 delete all                # Kill and delete all apps
pm2 delete 0                  # Delete app with id 0
```

----------------------------------------

TITLE: Manage PM2 applications (stop, restart, delete)
DESCRIPTION: Commands to stop, restart, or delete applications managed by PM2. Actions can be applied to specific apps by name, namespace, ID, or to all applications.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_3

LANGUAGE: bash
CODE:
```
$ pm2 stop     <app_name|namespace|id|'all'|json_conf>
$ pm2 restart  <app_name|namespace|id|'all'|json_conf>
$ pm2 delete   <app_name|namespace|id|'all'|json_conf>
```

----------------------------------------

TITLE: Run Node.js application in Docker with pm2-runtime
DESCRIPTION: Example Dockerfile commands demonstrating how to use `pm2-runtime` as a drop-in replacement for `node` to run Node.js applications in a hardened production environment within a container.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_8

LANGUAGE: bash
CODE:
```
RUN npm install pm2 -g
CMD [ "pm2-runtime", "npm", "--", "start" ]
```

----------------------------------------

TITLE: Configure PM2 for System Startup
DESCRIPTION: Commands to detect the init system, generate and configure PM2 to start on boot, save the current process list, restore previously saved processes, and disable/remove the startup system.
SOURCE: https://github.com/unitech/pm2/blob/master/pres/TMP.md#_snippet_5

LANGUAGE: bash
CODE:
```
pm2 startup                   # Detect init system, generate and configure pm2 boot on startup
pm2 save                      # Save current process list
pm2 resurrect                 # Restore previously saved processes
pm2 unstartup                 # Disable and remove startup system
```

----------------------------------------

TITLE: Manage Node.js Applications in PM2 Cluster Mode
DESCRIPTION: Commands to start Node.js applications in cluster mode for load balancing, perform zero-downtime reloads, and scale the number of process instances.
SOURCE: https://github.com/unitech/pm2/blob/master/pres/TMP.md#_snippet_1

LANGUAGE: bash
CODE:
```
pm2 start app.js -i 4         # Start 4 instances of application in cluster mode
                                # it will load balance network queries to each app
pm2 reload all                # Zero Second Downtime Reload
pm2 scale [app-name] 10       # Scale Cluster app to 10 process
```

----------------------------------------

TITLE: Start HTTP Application in PM2 Cluster Mode
DESCRIPTION: Demonstrates how to start an HTTP application using PM2 in cluster mode, either via an ecosystem configuration file or directly with the application script and instance count.
SOURCE: https://github.com/unitech/pm2/blob/master/examples/cluster-http/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
$ pm2 start ecosystem.config.js
# OR
$ pm2 start http.js -i max
```

----------------------------------------

TITLE: Get detailed information for a PM2 application
DESCRIPTION: Command to retrieve comprehensive details about a specific application managed by PM2, identified by its ID or name.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_4

LANGUAGE: bash
CODE:
```
$ pm2 describe <id|app_name>
```

----------------------------------------

TITLE: Install and Start Applications with PM2
DESCRIPTION: Commands for installing PM2 globally and starting various types of applications (Node.js, Python, npm scripts) in daemon mode with auto-restart capabilities.
SOURCE: https://github.com/unitech/pm2/blob/master/pres/TMP.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install pm2 -g            # Install PM2
pm2 start app.js              # Start, Daemonize and auto-restart application (Node)
pm2 start app.py              # Start, Daemonize and auto-restart application (Python)
pm2 start npm -- start        # Start, Daemonize and auto-restart Node application
```

----------------------------------------

TITLE: PM2 Log Management: Enable Log Rotation
DESCRIPTION: Install the pm2-logrotate module to enable automatic log rotation, preventing log files from consuming excessive disk space.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_11

LANGUAGE: bash
CODE:
```
$ pm2 install pm2-logrotate
```

----------------------------------------

TITLE: Monitor PM2 Processes
DESCRIPTION: Commands for listing all PM2-managed processes, sorting them, displaying real-time CPU and memory usage, and showing detailed information for a specific application.
SOURCE: https://github.com/unitech/pm2/blob/master/pres/TMP.md#_snippet_2

LANGUAGE: bash
CODE:
```
pm2 list                      # List all processes started with PM2
pm2 list --sort=<field>       # Sort all processes started with PM2
pm2 monit                     # Display memory and cpu usage of each app
pm2 show [app-name]           # Show all information about application
```

----------------------------------------

TITLE: Deploy Applications with PM2
DESCRIPTION: Commands for setting up remote servers for deployment, updating deployed applications, and reverting to previous deployment versions using a PM2 application JSON configuration.
SOURCE: https://github.com/unitech/pm2/blob/master/pres/TMP.md#_snippet_7

LANGUAGE: bash
CODE:
```
pm2 deploy app.json prod setup    # Setup \"prod\" remote server
pm2 deploy app.json prod          # Update \"prod\" remote server
pm2 deploy app.json prod revert 2 # Revert \"prod\" remote server by 2
```

----------------------------------------

TITLE: Start HTTP/TCP Application in Cluster Mode via PM2 CLI
DESCRIPTION: This command initiates an HTTP or TCP application, `api.js`, using PM2 in cluster mode. The `-i max` option instructs PM2 to launch as many instances of the application as there are CPU cores available, maximizing system resource utilization and enhancing application resilience.
SOURCE: https://github.com/unitech/pm2/blob/master/lib/templates/sample-apps/http-server/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
$ pm2 start api.js -i max
```

----------------------------------------

TITLE: Start HTTP Application in Cluster Mode with PM2
DESCRIPTION: Use PM2 to launch an HTTP application in cluster mode. This can be done by referencing an `ecosystem.config.js` file for predefined settings or by directly specifying the application file (`http.js`) with the `-i max` flag to utilize all available CPU cores.
SOURCE: https://github.com/unitech/pm2/blob/master/test/programmatic/fixtures/tar-module/multi-app-module/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
$ pm2 start ecosystem.config.js
# OR
$ pm2 start http.js -i max
```

----------------------------------------

TITLE: Manage Application Logs with PM2
DESCRIPTION: Commands for displaying logs from all or specific applications, viewing logs in JSON format, flushing all logs, and reloading log files.
SOURCE: https://github.com/unitech/pm2/blob/master/pres/TMP.md#_snippet_3

LANGUAGE: bash
CODE:
```
pm2 logs                      # Display logs of all apps
pm2 logs [app-name]           # Display logs for a specific app
pm2 logs --json               # Logs in JSON format
pm2 flush
pm2 reloadLogs
```

----------------------------------------

TITLE: Starting and Deleting PM2 Processes with Different Config Files
DESCRIPTION: These commands demonstrate how to start applications using PM2 with JavaScript, JSON, and YAML configuration files, followed by deleting all running PM2 processes after each start operation for cleanup.
SOURCE: https://github.com/unitech/pm2/blob/master/examples/ecosystem-file/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
$ pm2 start process.config.js
$ pm2 delete all
$ pm2 start process.json
$ pm2 delete all
$ pm2 start process.yml
```

----------------------------------------

TITLE: Waiting for Application 'ready' Event with PM2
DESCRIPTION: The `--wait-ready` option for PM2 will pause the process startup until the application explicitly sends a 'ready' event using `process.send('ready')`. This ensures that PM2 only considers the application fully started when it's ready to receive requests.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_25

LANGUAGE: Node.js
CODE:
```
process.send('ready')
```

----------------------------------------

TITLE: Execute PM2 API Script with Node.js
DESCRIPTION: This command executes a Node.js script (`api.js`) that interacts with the PM2 API. The script is designed to perform actions such as deleting all existing applications, starting a new application (`http.js`), and then restarting the `http` application. This demonstrates programmatic control over PM2 processes.
SOURCE: https://github.com/unitech/pm2/blob/master/examples/api-pm2/README.md#_snippet_0

LANGUAGE: shell
CODE:
```
$ node api.js
```

----------------------------------------

TITLE: Managing PM2 Startup Scripts
DESCRIPTION: PM2 provides commands to automatically detect and set up or remove init scripts for various system init systems like systemd, upstart, and launchd. This ensures PM2 processes start automatically on system boot.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_21

LANGUAGE: Shell
CODE:
```
pm2 startup
```

LANGUAGE: Shell
CODE:
```
pm2 unstartup
```

----------------------------------------

TITLE: Monitoring PM2 Processes with imonit
DESCRIPTION: The `pm2 imonit` command provides a previous terminal interface for monitoring PM2 processes. This allows users to view process status, resource usage, and logs in a curses-like dashboard.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_16

LANGUAGE: Shell
CODE:
```
pm2 imonit
```

----------------------------------------

TITLE: List all running PM2 applications
DESCRIPTION: Command to display a list of all applications currently managed by PM2, showing their status and other details.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_2

LANGUAGE: bash
CODE:
```
$ pm2 list
```

----------------------------------------

TITLE: Generating a Simple PM2 Ecosystem File
DESCRIPTION: The `pm2 ecosystem simple` command generates a basic ecosystem configuration file. This file can be used to define and manage multiple PM2 applications, simplifying deployment and configuration.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_18

LANGUAGE: Shell
CODE:
```
pm2 ecosystem simple
```

----------------------------------------

TITLE: Exposing a Folder over HTTP with PM2
DESCRIPTION: PM2 can serve static files or expose a folder over HTTP using the `pm2 serve` command. This feature is useful for quickly setting up a web server for static content or development purposes.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_17

LANGUAGE: Shell
CODE:
```
pm2 serve <path> <port>
```

----------------------------------------

TITLE: Update PM2 to Latest Version
DESCRIPTION: Update PM2 to its latest version using npm. This command saves the current process list, updates PM2, and then restores all processes seamlessly.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_13

LANGUAGE: bash
CODE:
```
# Install latest PM2 version
$ npm install pm2@latest -g
# Save process list, exit old PM2 & restore all processes
$ pm2 update
```

----------------------------------------

TITLE: PM2 CPU and Memory Profiling Commands
DESCRIPTION: Adds new commands to profile CPU and memory usage of PM2 applications, allowing for performance analysis and bottleneck identification over a specified timeout duration.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_6

LANGUAGE: APIDOC
CODE:
```
pm2 profile:cpu [timeout]
  timeout: Optional. Duration in milliseconds to capture CPU profile. If not specified, a default timeout is used.
pm2 profile:mem [timeout]
  timeout: Optional. Duration in milliseconds to capture memory profile. If not specified, a default timeout is used.
```

----------------------------------------

TITLE: Start PM2 TCP Application in Cluster Mode
DESCRIPTION: This snippet demonstrates how to launch a TCP application using PM2 in cluster mode. It provides two methods: using an `ecosystem.config.js` file for configuration or directly specifying the JavaScript file with the '-i max' flag to utilize all available CPU cores.
SOURCE: https://github.com/unitech/pm2/blob/master/examples/cluster-tcp/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
$ pm2 start ecosystem.config.js
# OR
$ pm2 start tcp.js -i max
```

----------------------------------------

TITLE: PM2 Keymetrics Setup and Application Management
DESCRIPTION: This snippet demonstrates how to register your PM2 instance with Keymetrics, start applications defined in a configuration file, and open the Keymetrics dashboard for monitoring.
SOURCE: https://github.com/unitech/pm2/blob/master/examples/test-all-keymetrics-features/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
# Make sure you've created keymetrics account
$ pm2 register
# Start all applications
$ pm2 start process.config.js
# Open Dashboard
$ pm2 open
```

----------------------------------------

TITLE: PM2 JavaScript API Usage
DESCRIPTION: Illustrates how to programmatically control PM2 daemon and managed applications using its JavaScript API. Examples include initializing a custom PM2 instance, starting and stopping applications, and subscribing to PM2 events via the bus system.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_30

LANGUAGE: javascript
CODE:
```
var PM2 = require('pm2');

// Or instanciate a custom PM2 instance

var pm2 = new PM2.custom({
  pm2_home :    // Default is the legacy $USER/.pm2. Now you can override this value
  cwd      :    // Move to CWD,
  daemon_mode : // Should the process stay attached to this application,
  independant : // Create new random instance available for current session
  secret_key  : // Keymetrics secret key
  public_key  : // Keymetrics public key
  machine_name: // Keymetrics instance name
});

// Start an app
pm2.start('myapp.js');

// Start an app with options
pm2.start({
  script   : 'api.js',
  instances: 4
}, function(err, processes) {
});

// Stop all apps
pm2.stop('all');

// Bus system to detect events
pm2.launchBus((err, bus) => {
  bus.on('log:out', (message) => {
    console.log(message);
  });

  bus.on('log:err', (message) => {
    console.log(message);
  });
});

// Connect to different keymetrics bucket
pm2.interact(opts, cb)

// PM2 auto closes connection if no processing is done but manually:

pm2.disconnect(cb) // Close connection with current pm2 instance
pm2.destroy(cb)    // Close and delete all pm2 related files of this session
```

----------------------------------------

TITLE: PM2 CLI: Application Lifecycle Commands
DESCRIPTION: Commands for starting, restarting, reloading, and deploying applications with PM2, including changes to restart behavior and deployment file checks.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_35

LANGUAGE: CLI
CODE:
```
pm2 start <app_name|app_id>
```

LANGUAGE: CLI
CODE:
```
pm2 start <json>
```

LANGUAGE: CLI
CODE:
```
pm2 start all
```

LANGUAGE: CLI
CODE:
```
pm2 reload <json_file>
```

LANGUAGE: CLI
CODE:
```
pm2 gracefulReload <json_file>
```

LANGUAGE: CLI
CODE:
```
pm2 deploy
```

----------------------------------------

TITLE: PM2 Command Line Interface (CLI) Examples
DESCRIPTION: Illustrates various PM2 CLI commands and options introduced or refined across different releases, covering process renaming, linking, unlinking, detailed status display, and configuration flags.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_31

LANGUAGE: bash
CODE:
```
pm2 restart app --name "new-name"
```

LANGUAGE: bash
CODE:
```
pm2 link
```

LANGUAGE: bash
CODE:
```
pm2 unlink
```

LANGUAGE: bash
CODE:
```
pm2 show
```

LANGUAGE: bash
CODE:
```
pm2 <command> --only <app-name>
```

LANGUAGE: bash
CODE:
```
pm2 id <app_name>
```

LANGUAGE: bash
CODE:
```
pm2 show <app_name>
```

LANGUAGE: bash
CODE:
```
pm2 restart --kill-timeout <number>
```

LANGUAGE: bash
CODE:
```
pm2 restart process.json --env <X>
```

LANGUAGE: bash
CODE:
```
pm2 <command> --no-automation
```

LANGUAGE: bash
CODE:
```
pm2 startup --hp
```

----------------------------------------

TITLE: PM2 CLI: Logging and Monitoring Commands
DESCRIPTION: Enhanced commands for viewing and managing application logs, including real-time updates, filtering, and flushing logs, as well as probing application status.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_36

LANGUAGE: CLI
CODE:
```
pm2 logs ['all'|'PM2'|app_name|app_id] [--err|--out] [--lines <n>] [--raw] [--timestamp [format]]
```

LANGUAGE: CLI
CODE:
```
pm2 logs PM2
```

LANGUAGE: CLI
CODE:
```
pm2 flush
```

LANGUAGE: CLI
CODE:
```
pm2 iprobe [app_name|app_id|'ALL']
```

----------------------------------------

TITLE: PM2 Logging Commands
DESCRIPTION: Commands related to viewing and managing application logs with PM2, including options for raw output and merged log streams, and displaying the last lines of log files.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_40

LANGUAGE: Shell
CODE:
```
pm2 logs --raw
```

LANGUAGE: Shell
CODE:
```
pm2 logs --raw
# Shows 20 last lines of each log file
```

LANGUAGE: Shell
CODE:
```
pm2 logs
# Now shows merged logs
```

----------------------------------------

TITLE: Monitor PM2 Metrics from CLI
DESCRIPTION: These commands provide ways to monitor custom metrics exposed by PM2 processes directly from the command line interface, either through a real-time dashboard or by showing detailed information for a specific process.
SOURCE: https://github.com/unitech/pm2/blob/master/examples/expose-custom-metrics/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
$ pm2 monit
```

LANGUAGE: bash
CODE:
```
$ pm2 show 0
```

----------------------------------------

TITLE: Monitor PM2 application logs and metrics
DESCRIPTION: Command to open the PM2 terminal-based monitoring interface, providing real-time logs, custom metrics, and application information.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_5

LANGUAGE: bash
CODE:
```
$ pm2 monit
```

----------------------------------------

TITLE: Displaying Key System Metrics with `pm2 ls`
DESCRIPTION: This snippet shows the condensed output of the `pm2 ls` command, which now includes vital server metrics like CPU, memory, network, and disk usage alongside process information.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_0

LANGUAGE: bash
CODE:
```
┌─────┬─────────────────┬─────────────┬─────────┬─────────┬──────────┬────────┬──────┬───────────┬──────────┬──────────┬──────────┬──────────┐
│ id  │ name            │ namespace   │ version │ mode    │ pid      │ uptime │ ↺    │ status    │ cpu      │ mem      │ user     │ watching │
├─────┼─────────────────┼─────────────┼─────────┼─────────┼──────────┼────────┼──────┼───────────┼──────────┼──────────┼──────────┼──────────┤
│ 4   │ app             │ default     │ 1.0.0   │ fork    │ 164618   │ 2s     │ 1670 │ online    │ 0%       │ 41.8mb   │ unitech  │ disabled │
└─────┴─────────────────┴─────────────┴─────────┴─────────┴──────────┴────────┴──────┴───────────┴──────────┴──────────┴──────────┴──────────┘
host metrics | cpu: 1.6% 42.9º | mem free: 52.0% | wlp0s20f3: ⇓ 0mb/s ⇑ 0mb/s | disk: ⇓ 0.199mb/s ⇑ 0mb/s /dev/nvme0n1p3 88.25% |
```

----------------------------------------

TITLE: PM2 Application Description and Log Flushing Commands
DESCRIPTION: Adds the ability to display cron configurations with `pm2 desc <id>` and introduces `pm2 flush <app>` to clear logs for a specific application, improving management and debugging.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_8

LANGUAGE: APIDOC
CODE:
```
pm2 desc <id>
  <id>: The ID of the application to describe.
  Displays cron configuration.
pm2 flush <app>
  <app>: The name or ID of the application whose logs to flush.
```

----------------------------------------

TITLE: PM2 Built-in Node.js Application Metrics
DESCRIPTION: Introduces new built-in metrics for Node.js applications, providing detailed insights into heap usage, active requests/handles, event loop latency, and HTTP performance for better monitoring.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_5

LANGUAGE: APIDOC
CODE:
```
Metrics:
  - Heap Size
  - Heap Usage
  - Used Heap Size
  - Active Requests
  - Active handles
  - Event loop latency
  - Event loop latency p95
  - HTTP queries per minutes
  - HTTP Mean Latency
  - HTTP P95 Latency
```

----------------------------------------

TITLE: PM2 Report and Deploy Command Enhancements
DESCRIPTION: Enhances the `pm2 report` command and introduces several fixes and improvements for `pm2 deploy`, including better environment variable handling and multi-command execution in post-deploy hooks.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_7

LANGUAGE: APIDOC
CODE:
```
pm2 report
  Enhances the diagnostic report.
pm2 deploy <env> exec [commands...]
  <env>: The deployment environment.
  [commands...]: Multiple commands to execute in the post-deploy hook.
```

----------------------------------------

TITLE: Manage PM2 Modules
DESCRIPTION: Commands for generating new PM2 modules, installing and uninstalling existing modules (e.g., log rotation), and publishing modules to npm.
SOURCE: https://github.com/unitech/pm2/blob/master/pres/TMP.md#_snippet_8

LANGUAGE: bash
CODE:
```
pm2 module:generate [name]    # Generate sample module with name [name]
pm2 install pm2-logrotate     # Install module (here a log rotation system)
pm2 uninstall pm2-logrotate   # Uninstall module
pm2 publish                   # Increment version, git push and npm publish
```

----------------------------------------

TITLE: PM2 Log Management: Basic Commands
DESCRIPTION: View, flush, and reload application logs managed by PM2. Supports standard, raw, JSON, and formatted output for flexible log analysis.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_10

LANGUAGE: bash
CODE:
```
$ pm2 logs
$ pm2 logs APP-NAME       # Display APP-NAME logs
$ pm2 logs --json         # JSON output
$ pm2 logs --format       # Formated output

$ pm2 flush               # Flush all logs
$ pm2 reloadLogs          # Reload all logs
```

----------------------------------------

TITLE: Generating JavaScript Configuration File with PM2 Ecosystem
DESCRIPTION: The `pm2 ecosystem` command now generates a JavaScript configuration file by default. This provides more flexibility for defining application settings compared to static JSON files.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_29

LANGUAGE: Shell
CODE:
```
pm2 ecosystem
```

----------------------------------------

TITLE: List PM2 Managed Applications Status
DESCRIPTION: This command displays a comprehensive table of all applications currently managed by PM2. The output includes key details such as application name, ID, process mode, PID, current status (e.g., 'online'), number of restarts, uptime, CPU and memory usage, user, and watching status. The example output specifically shows the 'http' app online with one restart, confirming the effects of the previous API operations.
SOURCE: https://github.com/unitech/pm2/blob/master/examples/api-pm2/README.md#_snippet_1

LANGUAGE: shell
CODE:
```
$ pm2 list
┌──────────┬────┬──────┬──────┬────────┬─────────┬────────┬─────┬───────────┬─────────┬──────────┐
│ App name │ id │ mode │ pid  │ status │ restart │ uptime │ cpu │ mem       │ user    │ watching │
├──────────┼────┼──────┼──────┼────────┼─────────┼────────┼─────┼───────────┼─────────┼──────────┤
│ http     │ 0  │ fork │ 7668 │ online │ 1       │ 2s     │ 0%  │ 34.2 MB   │ unitech │ disabled │
└──────────┴────┴──────┴──────┴────────┴─────────┴────────┴─────┬───────────┬─────────┬──────────┘
 Use `pm2 show <id|name>` to get more details about an app
```

----------------------------------------

TITLE: Attaching to PM2 Application Logs
DESCRIPTION: The `pm2 start --attach` command allows users to start a PM2 application and immediately attach to its logs, providing real-time output directly in the terminal. This is useful for debugging and monitoring during development.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_15

LANGUAGE: Shell
CODE:
```
pm2 start app.js --attach
```

----------------------------------------

TITLE: Enable PM2 host/server monitoring speedbar
DESCRIPTION: Commands to enable PM2's host monitoring feature, which provides real-time vitals of the server/host.
SOURCE: https://github.com/unitech/pm2/blob/master/README.md#_snippet_9

LANGUAGE: bash
CODE:
```
$ pm2 set pm2:sysmonit true
$ pm2 update
```

----------------------------------------

TITLE: PM2 Programmatic API Methods
DESCRIPTION: Documents key methods available in the PM2 programmatic API for interacting with processes, including sending data to a specific process ID and restarting applications programmatically.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_32

LANGUAGE: javascript
CODE:
```
pm2.sendDataToProcessId(type|data|id)
  type: string - The type of data being sent.
  data: any - The data payload.
  id: number - The ID of the target process.
```

LANGUAGE: javascript
CODE:
```
pm2.restart(json_data, function(err, data))
  json_data: object - Configuration for the application to restart.
  callback: function(err, data) - Callback function.
    err: Error - An error object if the operation fails.
    data: array - An array of process objects on success.
```

----------------------------------------

TITLE: Update PM2 and Generate Configuration
DESCRIPTION: Commands to update PM2 while preserving running processes and to generate a sample JavaScript configuration file for application management.
SOURCE: https://github.com/unitech/pm2/blob/master/pres/TMP.md#_snippet_6

LANGUAGE: bash
CODE:
```
pm2 update                    # Save processes, kill PM2 and restore processes
pm2 init                      # Generate a sample js configuration file
```

----------------------------------------

TITLE: Start PM2 Application with Source Map Auto-Resolution
DESCRIPTION: Use the `pm2 start` command to launch an application. PM2 automatically detects and uses source maps for debugging, providing resolved stack traces in case of errors without additional configuration.
SOURCE: https://github.com/unitech/pm2/blob/master/examples/sourcemap-auto-resolve/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
$ pm2 start process.config.js
```

----------------------------------------

TITLE: PM2 Module and Configuration Management
DESCRIPTION: Commands for configuring PM2 modules and updating installed modules, providing a new CLI API for fine-grained control over module options.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_45

LANGUAGE: Shell
CODE:
```
pm2 conf module.option [value]
# New CLI API for configuring modules
```

LANGUAGE: Shell
CODE:
```
pm2 install <module_name>
# Updates the module if already installed
```

----------------------------------------

TITLE: PM2 Show Application Metrics and Environment Variables
DESCRIPTION: Improves `pm2 show <app>` to display metric units and divergent environment variables, providing more detailed insights into running applications and their configurations.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_4

LANGUAGE: APIDOC
CODE:
```
pm2 show <app>
  <app>: The name or ID of the application.
  Displays metric units and divergent environment variables.
```

----------------------------------------

TITLE: PM2 Logs and Ecosystem File Enhancements
DESCRIPTION: Allows `pm2 logs` to be called without a specific application and enhances the `pm2 init` template for generating ecosystem files with additional fields, improving usability and configuration.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_13

LANGUAGE: APIDOC
CODE:
```
pm2 logs
  Can now be called without an application ID/name to view all logs.
pm2 init
  Generates an enhanced ecosystem file template with extra fields.
```

----------------------------------------

TITLE: PM2 Environment Variable Display Command
DESCRIPTION: Adds a new command `pm2 env <pm_id>` to display the environment variables an application is running with, aiding in debugging and verification of application settings.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_10

LANGUAGE: APIDOC
CODE:
```
pm2 env <pm_id>
  <pm_id>: The ID of the application.
  Displays the environment variables of the running application.
```

----------------------------------------

TITLE: PM2 CLI: Module Management Commands
DESCRIPTION: Commands for managing PM2 modules, including updating, publishing, generating sample modules, and installing modules from GitHub repositories or via an alias.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_33

LANGUAGE: CLI
CODE:
```
pm2 module:update <module_name>
```

LANGUAGE: CLI
CODE:
```
pm2 module:publish
```

LANGUAGE: CLI
CODE:
```
pm2 module:generate [module name]
```

LANGUAGE: CLI
CODE:
```
pm2 install username/repository
```

LANGUAGE: CLI
CODE:
```
pm2 i
```

----------------------------------------

TITLE: PM2 Application Management and Scaling
DESCRIPTION: Commands for starting applications in development mode, scaling instances up or down, and managing application lifecycle (stop/restart). Includes examples for using negative instance counts.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_41

LANGUAGE: Shell
CODE:
```
pm2-dev run app.js
# Start an app in dev mode (--no-daemon --watch and stream logs of all launched apps)
```

LANGUAGE: Shell
CODE:
```
pm2 scale <app_name> <number>
# Scale up/down an application
```

LANGUAGE: Shell
CODE:
```
pm2 start app.js -i -3
# In an 8 CPU environment, this starts 5 instances (8 - 3)
```

LANGUAGE: Shell
CODE:
```
pm2 stop <app_name>
pm2 restart <app_name>
```

LANGUAGE: Shell
CODE:
```
pm2 list
# Safe parameters for 'pm2 list' to prevent cli-table failures
```

----------------------------------------

TITLE: PM2 CLI: Configuration and Settings
DESCRIPTION: Commands to manage PM2's configuration, allowing for batch setting of multiple key-value pairs, setting passwords, displaying current configurations, and handling key escaping.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_34

LANGUAGE: CLI
CODE:
```
pm2 multiset "k1 v1 k2 v2 k3 v3"
```

LANGUAGE: CLI
CODE:
```
pm2 set pm2:passwd xxxx
```

LANGUAGE: CLI
CODE:
```
pm2 get|pm2 conf
```

LANGUAGE: CLI
CODE:
```
pm2 conf system
```

----------------------------------------

TITLE: Enabling and Disabling PM2 System Monitoring
DESCRIPTION: These commands demonstrate how to use `pm2 set` to toggle the local system monitoring feature on or off. This allows users to control whether PM2 collects and displays host-level metrics.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_2

LANGUAGE: bash
CODE:
```
# Disable system monitoring
pm2 set pm2:sysmonit false
# Enable system monitoring
pm2 set pm2:sysmonit true
```

----------------------------------------

TITLE: PM2 Integration for TypeScript Applications
DESCRIPTION: Commands to install PM2's TypeScript module and launch a TypeScript application. This enables PM2 to directly execute .ts files.
SOURCE: https://github.com/unitech/pm2/blob/master/examples/using-pm2-and-transpilers/README.md#_snippet_1

LANGUAGE: shell
CODE:
```
$ pm2 install typescript
$ pm2 start http.ts
```

----------------------------------------

TITLE: PM2 Integration for CoffeeScript Applications
DESCRIPTION: Commands to install PM2's CoffeeScript module and launch a CoffeeScript application. This enables PM2 to directly execute .coffee files.
SOURCE: https://github.com/unitech/pm2/blob/master/examples/using-pm2-and-transpilers/README.md#_snippet_0

LANGUAGE: shell
CODE:
```
$ pm2 install coffee-script
$ pm2 start echo.coffee
```

----------------------------------------

TITLE: PM2 Custom Metrics: Incrementing an io.counter
DESCRIPTION: This JavaScript example demonstrates how to initialize a custom counter metric using `@pm2/io` and increment it periodically. The `currentReq` counter tracks 'Current Processing' and is incremented every second, providing real-time visibility into ongoing operations.
SOURCE: https://github.com/unitech/pm2/blob/master/lib/templates/sample-apps/pm2-plus-metrics-actions/README.md#_snippet_0

LANGUAGE: javascript
CODE:
```
const io = require('@pm2/io')

const currentReq = io.counter({
  name: 'CM: Current Processing',
  type: 'counter'
})

setInterval(() => {
  currentReq.inc()
}, 1000)
```

----------------------------------------

TITLE: Installing PM2 on Debian-based Systems via APT
DESCRIPTION: PM2 can be installed on Debian-based systems (like Ubuntu) using its official APT repository. This method ensures PM2 is installed and updated like other system packages.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_22

LANGUAGE: Shell
CODE:
```
wget -O - http://apt.pm2.io/ubuntu/apt.pm2.io.gpg.key | sudo apt-key add -
echo "deb http://apt.pm2.io/ubuntu xenial main" | sudo tee /etc/apt/sources.list.d/pm2.list
sudo apt-get update
sudo apt-get install pm2
```

----------------------------------------

TITLE: PM2 Module Management System Commands
DESCRIPTION: Introduces a new set of commands for managing PM2 modules, allowing users to package, publish, and install modules from local folders or tarballs, enhancing extensibility.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_12

LANGUAGE: APIDOC
CODE:
```
pm2 package <folder>
  <folder>: Path to the module folder to package.
pm2 publish <folder>
  <folder>: Path to the module folder to publish.
pm2 install <tarball>
  <tarball>: Path to the module tarball to install.
```

----------------------------------------

TITLE: PM2 Serve SPA Autoredirect Configuration
DESCRIPTION: Enhances `pm2 serve` to automatically redirect requests to `index.html` for Single Page Applications (SPAs) when the `--spa` flag is used, simplifying SPA deployment.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_3

LANGUAGE: APIDOC
CODE:
```
pm2 serve --spa
  --spa: Enables automatic redirection of requests to index.html for SPAs.
```

----------------------------------------

TITLE: Manage PM2 Module Lifecycle: Install, Log, Uninstall, Restart
DESCRIPTION: This section provides essential `pm2` commands for managing a module's lifecycle after generation. It covers installing the module, viewing its logs, uninstalling it, and forcing a restart for development or deployment.
SOURCE: https://github.com/unitech/pm2/blob/master/examples/module-test/README.md#_snippet_1

LANGUAGE: bash
CODE:
```
Start module in development mode:
$ cd module-test/
$ pm2 install .

Module Log:
$ pm2 logs module-test

Uninstall module:
$ pm2 uninstall module-test

Force restart:
$ pm2 restart module-test
```

----------------------------------------

TITLE: Generate a New PM2 Module via CLI
DESCRIPTION: This snippet shows the command-line interaction and output when generating a new PM2 module using `pm2 module:generate`. It includes the cloning process and initial package installation, guiding the user through the module creation.
SOURCE: https://github.com/unitech/pm2/blob/master/examples/module-test/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
>>> pm2 module:generate
[PM2] Spawning PM2 daemon with pm2_home=/home/<USER>/.pm2
[PM2] PM2 Successfully daemonized
[PM2][Module] Module name: module-test
[PM2][Module] Getting sample app
Cloning into 'module-test'...

npm notice created a lockfile as package-lock.json. You should commit this file.
added 4 packages in 0.939s

[PM2][Module] Module sample created in folder:  /home/<USER>/keymetrics/pm2/examples/module-test
```

----------------------------------------

TITLE: PM2 Configuration Flags and JSON Options
DESCRIPTION: Command-line flags and JSON configuration options to control PM2's behavior, such as daemonization, JavaScript engine features, automatic restarts, and vizion integration.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_42

LANGUAGE: Shell
CODE:
```
--no-daemon
# Command now displays logs of all processes (useful for Docker)
```

LANGUAGE: Shell
CODE:
```
--next-gen-js
# Integrates BabelJS into PM2
```

LANGUAGE: Shell
CODE:
```
--harmony
# Makes modules compatible with ES6 by default
```

LANGUAGE: Shell
CODE:
```
--no-autorestart
# Starts an app without the automatic restart feature
```

LANGUAGE: JSON
CODE:
```
{
  "autorestart": false
}
```

LANGUAGE: Shell
CODE:
```
--no-vizion
# Starts an app completely without vizion features
```

LANGUAGE: JSON
CODE:
```
{
  "vizion": false
}
```

LANGUAGE: JSON
CODE:
```
// exec() timeout configurable via .json
{
  "exec_timeout": 5000
}
```

----------------------------------------

TITLE: PM2 Exponential Backoff Restart Delay Configuration
DESCRIPTION: Introduces an exponential backoff restart delay mechanism for applications, configurable via `--exp-backoff-restart-delay`, to prevent rapid restarts in case of persistent errors and improve stability.
SOURCE: https://github.com/unitech/pm2/blob/master/CHANGELOG.md#_snippet_11

LANGUAGE: APIDOC
CODE:
```
--exp-backoff-restart-delay <ms>
  <ms>: Delay in milliseconds. The delay resets on successful application start.
```
