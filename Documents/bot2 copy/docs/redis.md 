This document serves as both a quick start guide to Redis and a detailed resource for building it from source.

New to Redis? Start with What is Redis and Getting Started
Ready to build from source? Jump to Build Redis from Source
Want to contribute? See the Code contributions section and CONTRIBUTING.md
Looking for detailed documentation? Navigate to redis.io/docs
Table of contents
What is Redis?
Key use cases
Why choose Redis?
What is Redis Open Source?
Getting started
Redis starter projects
Using Redis with client libraries
Using Redis with redis-cli
Using Redis with Redis Insight
Redis data types, processing engines, and capabilities
Community
Build Redis from source
Build and run Redis with all data structures - Ubuntu 20.04 (Focal)
Build and run Redis with all data structures - Ubuntu 22.04 (Jammy) / 24.04 (Noble)
Build and run Redis with all data structures - Debian 11 (Bullseye) / 12 (Bookworm)
Build and run Redis with all data structures - AlmaLinux 8.10 / Rocky Linux 8.10
Build and run Redis with all data structures - AlmaLinux 9.5 / Rocky Linux 9.5
Build and run Redis with all data structures - macOS 13 (Ventura) and macOS 14 (Sonoma)
Build and run Redis with all data structures - macOS 15 (Sequoia)
Building Redis - flags and general notes
Fixing build problems with dependencies or cached build options
Fixing problems building 32 bit binaries
Allocator
Monotonic clock
Verbose build
Running Redis with TLS
Code contributions
Redis Trademarks
What is Redis?
For developers, who are building real-time data-driven applications, Redis is the preferred, fastest, and most feature-rich cache, data structure server, and document and vector query engine.

Key use cases
Redis excels in various applications, including:

Caching: Supports multiple eviction policies, key expiration, and hash-field expiration.
Distributed Session Store: Offers flexible session data modeling (string, JSON, hash).
Data Structure Server: Provides low-level data structures (strings, lists, sets, hashes, sorted sets, JSON, etc.) with high-level semantics (counters, queues, leaderboards, rate limiters) and supports transactions & scripting.
NoSQL Data Store: Key-value, document, and time series data storage.
Search and Query Engine: Indexing for hash/JSON documents, supporting vector search, full-text search, geospatial queries, ranking, and aggregations via Redis Query Engine.
Event Store & Message Broker: Implements queues (lists), priority queues (sorted sets), event deduplication (sets), streams, and pub/sub with probabilistic stream processing capabilities.
Vector Store for GenAI: Integrates with AI applications (e.g. LangGraph, mem0) for short-term memory, long-term memory, LLM response caching (semantic caching), and retrieval augmented generation (RAG).
Real-Time Analytics: Powers personalization, recommendations, fraud detection, and risk assessment.
Why choose Redis?
Redis is a popular choice for developers worldwide due to its combination of speed, flexibility, and rich feature set. Here's why people choose Redis for:

Performance: Because Redis keeps data primarily in memory and uses efficient data structures, it achieves extremely low latency (often sub-millisecond) for both read and write operations. This makes it ideal for applications demanding real-time responsiveness.
Flexibility: Redis isn't just a key-value store, it provides native support for a wide range of data structures and capabilities listed in What is Redis?
Extensibility: Redis is not limited to the built-in data structures, it has a modules API that makes it possible to extend Redis functionality and rapidly implement new Redis commands
Simplicity: Redis has a simple, text-based protocol and well-documented command set
Ubiquity: Redis is battle tested in production workloads at a massive scale. There is a good chance you indirectly interact with Redis several times daily
Versatility: Redis is the de facto standard for use cases such as:
Caching: quickly access frequently used data without needing to query your primary database
Session management: read and write user session data without hurting user experience or slowing down every API call
Querying, sorting, and analytics: perform deduplication, full text search, and secondary indexing on in-memory data as fast as possible
Messaging and interservice communication: job queues, message brokering, pub/sub, and streams for communicating between services
Vector operations: Long-term and short-term LLM memory, RAG content retrieval, semantic caching, semantic routing, and vector similarity search
In summary, Redis provides a powerful, fast, and flexible toolkit for solving a wide variety of data management challenges. If you want to know more, here is a list of starting points:

Introduction to Redis data types
The full list of Redis commands
Redis for AI
Redis documentation
What is Redis Open Source?
Redis Community Edition (Redis CE) was renamed Redis Open Source with the v8.0 release.

Redis Ltd. also offers Redis Software, a self-managed software with additional compliance, reliability, and resiliency for enterprise scaling, and Redis Cloud, a fully managed service integrated with Google Cloud, Azure, and AWS for production-ready apps.

Read more about the differences between Redis Open Source and Redis here.

Getting started
If you want to get up and running with Redis quickly without needing to build from source, use one of the following methods:

Redis Cloud
Official Redis Docker images (Alpine/Debian)
docker run -d -p 6379:6379 redis:latest
Redis binary distributions
Snap
Homebrew
RPM
Debian
Redis quick start guides
If you prefer to build Redis from source - see instructions below.

Redis starter projects
To get started as quickly as possible in your language of choice, use one of the following starter projects:

Python (redis-py)
C#/.NET (NRedisStack/StackExchange.Redis)
Go (go-redis)
JavaScript (node-redis)
Java/Spring (Jedis)
Using Redis with client libraries
To connect your application to Redis, you will need a client library. Redis has documented client libraries in most popular languages, with community-supported client libraries in additional languages.

Python (redis-py)
Python (RedisVL)
C#/.NET (NRedisStack/StackExchange.Redis)
JavaScript (node-redis)
Java (Jedis)
Java (Lettuce)
Go (go-redis)
PHP (Predis)
C (hiredis)
Full list of client libraries
Using Redis with redis-cli
redis-cli is Redis' command line interface. It is available as part of all the binary distributions and when you build Redis from source.

You can start a redis-server instance, and then, in another terminal try the following:

cd src
./redis-cli
redis> ping
PONG
redis> set foo bar
OK
redis> get foo
"bar"
redis> incr mycounter
(integer) 1
redis> incr mycounter
(integer) 2
redis>
Using Redis with Redis Insight
For a more visual and user-friendly experience, use Redis Insight - a tool that lets you explore data, design, develop, and optimize your applications while also serving as a platform for Redis education and onboarding. Redis Insight integrates Redis Copilot, a natural language AI assistant that improves the experience when working with data and commands.

Redis Insight documentation
Redis Insight GitHub repository
Redis data types, processing engines, and capabilities
Redis provides a variety of data types, processing engines, and capabilities to support a wide range of use cases:

Important: Features marked with an asterisk (*) require Redis to be compiled with the BUILD_WITH_MODULES=yes flag when building Redis from source

String: Sequences of bytes, including text, serialized objects, and binary arrays used for caching, counters, and bitwise operations.
JSON: Nested JSON documents that are indexed and searchable using JSONPath expressions and with Redis Query Engine
Hash: Field-value maps used to represent basic objects and store groupings of key-value pairs with support for hash field expiration (TTL)
Redis Query Engine: Use Redis as a document database, a vector database, a secondary index, and a search engine. Define indexes for hash and JSON documents and then use a rich query language for vector search, full-text search, geospatial queries, and aggregations.
List: Linked lists of string values used as stacks, queues, and for queue management.
Set: Unordered collection of unique strings used for tracking unique items, relations, and common set operations (intersections, unions, differences).
Sorted set: Collection of unique strings ordered by an associated score used for leaderboards and rate limiters.
Vector set (beta): Collection of vector embeddings used for semantic similarity search, semantic caching, semantic routing, and Retrieval Augmented Generation (RAG).
Geospatial indexes: Coordinates used for finding nearby points within a given radius or bounding box.
Bitmap: A set of bit-oriented operations defined on the string type used for efficient set representations and object permissions.
Bitfield: Binary-encoded strings that let you set, increment, and get integer values of arbitrary bit length used for limited-range counters, numeric values, and multi-level object permissions such as role-based access control (RBAC)
Hyperloglog: A probabilistic data structure for approximating the cardinality of a set used for analytics such as counting unique visits, form fills, etc.
*Bloom filter: A probabilistic data structure to check if a given value is present in a set. Used for fraud detection, ad placement, and unique column (i.e. username/email/slug) checks.
*Cuckoo filter: A probabilistic data structure for checking if a given value is present in a set while also allowing limited counting and deletions used in targeted advertising and coupon code validation.
*t-digest: A probabilistic data structure used for estimating the percentile of a large dataset without having to store and order all the data points. Used for hardware/software monitoring, online gaming, network traffic monitoring, and predictive maintenance.
*Top-k: A probabilistic data structure for finding the most frequent values in a data stream used for trend discovery.
*Count-min sketch: A probabilistic data structure for estimating how many times a given value appears in a data stream used for sales volume calculations.
Time series: Data points indexed in time order used for monitoring sensor data, asset tracking, and predictive analytics
Pub/sub: A lightweight messaging capability. Publishers send messages to a channel, and subscribers receive messages from that channel.
Stream: An append-only log with random access capabilities and complex consumption strategies such as consumer groups. Used for event sourcing, sensor monitoring, and notifications.
Transaction: Allows the execution of a group of commands in a single step. A request sent by another client will never be served in the middle of the execution of a transaction. This guarantees that the commands are executed as a single isolated operation.
Programmability: Upload and execute Lua scripts on the server. Scripts can employ programmatic control structures and use most of the commands while executing to access the database. Because scripts are executed on the server, reading and writing data from scripts is very efficient.
Community
Redis Community Resources

Build Redis from source
This section refers to building Redis from source. If you want to get up and running with Redis quickly without needing to build from source see the Getting started section.

Build and run Redis with all data structures - Ubuntu 20.04 (Focal)
Tested with the following Docker images:

ubuntu:20.04
Install required dependencies

Update your package lists and install the necessary development tools and libraries:

apt-get update
apt-get install -y sudo
sudo apt-get install -y --no-install-recommends ca-certificates wget dpkg-dev gcc g++ libc6-dev libssl-dev make git python3 python3-pip python3-venv python3-dev unzip rsync clang automake autoconf gcc-10 g++-10 libtool
Use GCC 10 as the default compiler

Update the system's default compiler to GCC 10:

sudo update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-10 100 --slave /usr/bin/g++ g++ /usr/bin/g++-10
Install CMake

Install CMake using pip3 and link it for system-wide access:

pip3 install cmake==3.31.6
sudo ln -sf /usr/local/bin/cmake /usr/bin/cmake
cmake --version
Note: CMake version 3.31.6 is the latest supported version. Newer versions cannot be used.

Download the Redis source

Download a specific version of the Redis source code archive from GitHub.

Replace <version> with the Redis version, for example: 8.0.0.

cd /usr/src
wget -O redis-<version>.tar.gz https://github.com/redis/redis/archive/refs/tags/<version>.tar.gz
Extract the source archive

Create a directory for the source code and extract the contents into it:

cd /usr/src
tar xvf redis-<version>.tar.gz
rm redis-<version>.tar.gz
Build Redis

Set the necessary environment variables and compile Redis:

cd /usr/src/redis-<version>
export BUILD_TLS=yes BUILD_WITH_MODULES=yes INSTALL_RUST_TOOLCHAIN=yes DISABLE_WERRORS=yes
make -j "$(nproc)" all
Run Redis

cd /usr/src/redis-<version>
./src/redis-server redis-full.conf
Build and run Redis with all data structures - Ubuntu 22.04 (Jammy) / 24.04 (Noble)
Tested with the following Docker image:

ubuntu:22.04
ubuntu:24.04
Install required dependencies

Update your package lists and install the necessary development tools and libraries:

apt-get update
apt-get install -y sudo
sudo apt-get install -y --no-install-recommends ca-certificates wget dpkg-dev gcc g++ libc6-dev libssl-dev make git cmake python3 python3-pip python3-venv python3-dev unzip rsync clang automake autoconf libtool
Download the Redis source

Download a specific version of the Redis source code archive from GitHub.

Replace <version> with the Redis version, for example: 8.0.0.

cd /usr/src
wget -O redis-<version>.tar.gz https://github.com/redis/redis/archive/refs/tags/<version>.tar.gz
Extract the source archive

Create a directory for the source code and extract the contents into it:

cd /usr/src
tar xvf redis-<version>.tar.gz
rm redis-<version>.tar.gz
Build Redis

Set the necessary environment variables and build Redis:

cd /usr/src/redis-<version>
export BUILD_TLS=yes BUILD_WITH_MODULES=yes INSTALL_RUST_TOOLCHAIN=yes DISABLE_WERRORS=yes
make -j "$(nproc)" all
Run Redis

cd /usr/src/redis-<version>
./src/redis-server redis-full.conf
Build and run Redis with all data structures - Debian 11 (Bullseye) / 12 (Bookworm)
Tested with the following Docker images:

debian:bullseye
debian:bullseye-slim
debian:bookworm
debian:bookworm-slim
Install required dependencies

Update your package lists and install the necessary development tools and libraries:

apt-get update
apt-get install -y sudo
sudo apt-get install -y --no-install-recommends ca-certificates wget dpkg-dev gcc g++ libc6-dev libssl-dev make git cmake python3 python3-pip python3-venv python3-dev unzip rsync clang automake autoconf libtool
Download the Redis source

Download a specific version of the Redis source code archive from GitHub.

Replace <version> with the Redis version, for example: 8.0.0.

cd /usr/src
wget -O redis-<version>.tar.gz https://github.com/redis/redis/archive/refs/tags/<version>.tar.gz
Extract the source archive

Create a directory for the source code and extract the contents into it:

cd /usr/src
tar xvf redis-<version>.tar.gz
rm redis-<version>.tar.gz
Build Redis

Set the necessary environment variables and build Redis:

cd /usr/src/redis-<version>
export BUILD_TLS=yes BUILD_WITH_MODULES=yes INSTALL_RUST_TOOLCHAIN=yes DISABLE_WERRORS=yes
make -j "$(nproc)" all
Run Redis

cd /usr/src/redis-<version>
./src/redis-server redis-full.conf
Build and run Redis with all data structures - AlmaLinux 8.10 / Rocky Linux 8.10
Tested with the following Docker images:

almalinux:8.10
almalinux:8.10-minimal
rockylinux/rockylinux:8.10
rockylinux/rockylinux:8.10-minimal
Prepare the system

For 8.10-minimal, install sudo and dnf as follows:

microdnf install dnf sudo -y
For 8.10 (regular), install sudo as follows:

dnf install sudo -y
Clean the package metadata, enable required repositories, and install development tools:

sudo dnf clean all
sudo tee /etc/yum.repos.d/goreleaser.repo > /dev/null <<EOF
[goreleaser]
name=GoReleaser
baseurl=https://repo.goreleaser.com/yum/
enabled=1
gpgcheck=0
EOF
sudo dnf update -y
sudo dnf groupinstall "Development Tools" -y
sudo dnf config-manager --set-enabled powertools
sudo dnf install -y epel-release
Install required dependencies

Update your package lists and install the necessary development tools and libraries:

sudo dnf install -y --nobest --skip-broken pkg-config wget gcc-toolset-13-gcc gcc-toolset-13-gcc-c++ git make openssl openssl-devel python3.11 python3.11-pip python3.11-devel unzip rsync clang curl libtool automake autoconf jq systemd-devel
Create a Python virtual environment:

python3.11 -m venv /opt/venv
Enable the GCC toolset:

sudo cp /opt/rh/gcc-toolset-13/enable /etc/profile.d/gcc-toolset-13.sh
echo "source /etc/profile.d/gcc-toolset-13.sh" | sudo tee -a /etc/bashrc
Install CMake

Install CMake 3.25.1 manually:

CMAKE_VERSION=3.25.1
ARCH=$(uname -m)
if [ "$ARCH" = "x86_64" ]; then
  CMAKE_FILE=cmake-${CMAKE_VERSION}-linux-x86_64.sh
else
  CMAKE_FILE=cmake-${CMAKE_VERSION}-linux-aarch64.sh
fi
wget https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/${CMAKE_FILE}
chmod +x ${CMAKE_FILE}
./${CMAKE_FILE} --skip-license --prefix=/usr/local --exclude-subdir
rm ${CMAKE_FILE}
cmake --version
Download the Redis source

Download a specific version of the Redis source code archive from GitHub.

Replace <version> with the Redis version, for example: 8.0.0.

cd /usr/src
wget -O redis-<version>.tar.gz https://github.com/redis/redis/archive/refs/tags/<version>.tar.gz
Extract the source archive

Create a directory for the source code and extract the contents into it:

cd /usr/src
tar xvf redis-<version>.tar.gz
rm redis-<version>.tar.gz
Build Redis

Enable the GCC toolset, set the necessary environment variables, and build Redis:

source /etc/profile.d/gcc-toolset-13.sh
cd /usr/src/redis-<version>
export BUILD_TLS=yes BUILD_WITH_MODULES=yes INSTALL_RUST_TOOLCHAIN=yes DISABLE_WERRORS=yes
make -j "$(nproc)" all
Run Redis

cd /usr/src/redis-<version>
./src/redis-server redis-full.conf
Build and run Redis with all data structures - AlmaLinux 9.5 / Rocky Linux 9.5
Tested with the following Docker images:

almalinux:9.5
almalinux:9.5-minimal
rockylinux/rockylinux:9.5
rockylinux/rockylinux:9.5-minimal
Prepare the system

For 9.5-minimal, install sudo and dnf as follows:

microdnf install dnf sudo -y
For 9.5 (regular), install sudo as follows:

dnf install sudo -y
Clean the package metadata, enable required repositories, and install development tools:

sudo tee /etc/yum.repos.d/goreleaser.repo > /dev/null <<EOF
[goreleaser]
name=GoReleaser
baseurl=https://repo.goreleaser.com/yum/
enabled=1
gpgcheck=0
EOF
sudo dnf clean all
sudo dnf makecache
sudo dnf update -y
Install required dependencies

Update your package lists and install the necessary development tools and libraries:

sudo dnf install -y --nobest --skip-broken pkg-config xz wget which gcc-toolset-13-gcc gcc-toolset-13-gcc-c++ git make openssl openssl-devel python3 python3-pip python3-devel unzip rsync clang curl libtool automake autoconf jq systemd-devel
Create a Python virtual environment:

python3 -m venv /opt/venv
Enable the GCC toolset:

sudo cp /opt/rh/gcc-toolset-13/enable /etc/profile.d/gcc-toolset-13.sh
echo "source /etc/profile.d/gcc-toolset-13.sh" | sudo tee -a /etc/bashrc
Install CMake

Install CMake 3.25.1 manually:

CMAKE_VERSION=3.25.1
ARCH=$(uname -m)
if [ "$ARCH" = "x86_64" ]; then
  CMAKE_FILE=cmake-${CMAKE_VERSION}-linux-x86_64.sh
else
  CMAKE_FILE=cmake-${CMAKE_VERSION}-linux-aarch64.sh
fi
wget https://github.com/Kitware/CMake/releases/download/v${CMAKE_VERSION}/${CMAKE_FILE}
chmod +x ${CMAKE_FILE}
./${CMAKE_FILE} --skip-license --prefix=/usr/local --exclude-subdir
rm ${CMAKE_FILE}
cmake --version
Download the Redis source

Download a specific version of the Redis source code archive from GitHub.

Replace <version> with the Redis version, for example: 8.0.0.

cd /usr/src
wget -O redis-<version>.tar.gz https://github.com/redis/redis/archive/refs/tags/<version>.tar.gz
Extract the source archive

Create a directory for the source code and extract the contents into it:

cd /usr/src
tar xvf redis-<version>.tar.gz
rm redis-<version>.tar.gz
Build Redis

Enable the GCC toolset, set the necessary environment variables, and build Redis:

source /etc/profile.d/gcc-toolset-13.sh
cd /usr/src/redis-<version>
export BUILD_TLS=yes BUILD_WITH_MODULES=yes INSTALL_RUST_TOOLCHAIN=yes DISABLE_WERRORS=yes
make -j "$(nproc)" all
Run Redis

cd /usr/src/redis-<version>
./src/redis-server redis-full.conf
Build and run Redis with all data structures - macOS 13 (Ventura) and macOS 14 (Sonoma)
Install Homebrew

If Homebrew is not already installed, follow the installation instructions on the Homebrew home page.

Install required packages

export HOMEBREW_NO_AUTO_UPDATE=1
brew update
brew install coreutils
brew install make
brew install openssl
brew install llvm@18
brew install cmake
brew install gnu-sed
brew install automake
brew install libtool
brew install wget
Install Rust

Rust is required to build the JSON package.

RUST_INSTALLER=rust-1.80.1-$(if [ "$(uname -m)" = "arm64" ]; then echo "aarch64"; else echo "x86_64"; fi)-apple-darwin
wget --quiet -O ${RUST_INSTALLER}.tar.xz https://static.rust-lang.org/dist/${RUST_INSTALLER}.tar.xz
tar -xf ${RUST_INSTALLER}.tar.xz
(cd ${RUST_INSTALLER} && sudo ./install.sh)
Download the Redis source

Download a specific version of the Redis source code archive from GitHub.

Replace <version> with the Redis version, for example: 8.0.0.

cd ~/src
wget -O redis-<version>.tar.gz https://github.com/redis/redis/archive/refs/tags/<version>.tar.gz
Extract the source archive

Create a directory for the source code and extract the contents into it:

cd ~/src
tar xvf redis-<version>.tar.gz
rm redis-<version>.tar.gz
Build Redis

cd ~/src/redis-<version>
export HOMEBREW_PREFIX="$(brew --prefix)"
export BUILD_WITH_MODULES=yes
export BUILD_TLS=yes
export DISABLE_WERRORS=yes
PATH="$HOMEBREW_PREFIX/opt/libtool/libexec/gnubin:$HOMEBREW_PREFIX/opt/llvm@18/bin:$HOMEBREW_PREFIX/opt/make/libexec/gnubin:$HOMEBREW_PREFIX/opt/gnu-sed/libexec/gnubin:$HOMEBREW_PREFIX/opt/coreutils/libexec/gnubin:$PATH"
export LDFLAGS="-L$HOMEBREW_PREFIX/opt/llvm@18/lib"
export CPPFLAGS="-I$HOMEBREW_PREFIX/opt/llvm@18/include"
mkdir -p build_dir/etc
make -C redis-8.0 -j "$(nproc)" all OS=macos
make -C redis-8.0 install PREFIX=$(pwd)/build_dir OS=macos
Run Redis

export LC_ALL=en_US.UTF-8
export LANG=en_US.UTF-8
build_dir/bin/redis-server redis-full.conf
Build and run Redis with all data structures - macOS 15 (Sequoia)
Support and instructions will be provided at a later date.

Building Redis - flags and general notes
Redis can be compiled and used on Linux, OSX, OpenBSD, NetBSD, FreeBSD. We support big endian and little endian architectures, and both 32 bit and 64-bit systems.

It may compile on Solaris derived systems (for instance SmartOS) but our support for this platform is best effort and Redis is not guaranteed to work as well as on Linux, OSX, and *BSD.

To build Redis with all the data structures (including JSON, time series, Bloom filter, cuckoo filter, count-min sketch, top-k, and t-digest) and with Redis Query Engine, make sure first that all the prerequisites are installed (see build instructions above, per operating system). You need to use the following flag in the make command:

make BUILD_WITH_MODULES=yes
To build Redis with just the core data structures, use:

make
To build with TLS support, you need OpenSSL development libraries (e.g. libssl-dev on Debian/Ubuntu) and the following flag in the make command:

make BUILD_TLS=yes
To build with systemd support, you need systemd development libraries (such as libsystemd-dev on Debian/Ubuntu or systemd-devel on CentOS), and the following flag:

make USE_SYSTEMD=yes
To append a suffix to Redis program names, add the following flag:

make PROG_SUFFIX="-alt"
You can build a 32 bit Redis binary using:

make 32bit
After building Redis, it is a good idea to test it using:

make test
If TLS is built, running the tests with TLS enabled (you will need tcl-tls installed):

./utils/gen-test-certs.sh
./runtest --tls
Fixing build problems with dependencies or cached build options
Redis has some dependencies which are included in the deps directory. make does not automatically rebuild dependencies even if something in the source code of dependencies changes.

When you update the source code with git pull or when code inside the dependencies tree is modified in any other way, make sure to use the following command in order to really clean everything and rebuild from scratch:

make distclean
This will clean: jemalloc, lua, hiredis, linenoise and other dependencies.

Also, if you force certain build options like 32bit target, no C compiler optimizations (for debugging purposes), and other similar build time options, those options are cached indefinitely until you issue a make distclean command.

Fixing problems building 32 bit binaries
If after building Redis with a 32 bit target you need to rebuild it with a 64 bit target, or the other way around, you need to perform a make distclean in the root directory of the Redis distribution.

In case of build errors when trying to build a 32 bit binary of Redis, try the following steps:

Install the package libc6-dev-i386 (also try g++-multilib).
Try using the following command line instead of make 32bit: make CFLAGS="-m32 -march=native" LDFLAGS="-m32"
Allocator
Selecting a non-default memory allocator when building Redis is done by setting the MALLOC environment variable. Redis is compiled and linked against libc malloc by default, except for jemalloc being the default on Linux systems. This default was picked because jemalloc has proven to have fewer fragmentation problems than libc malloc.

To force compiling against libc malloc, use:

make MALLOC=libc
To compile against jemalloc on Mac OS X systems, use:

make MALLOC=jemalloc
Monotonic clock
By default, Redis will build using the POSIX clock_gettime function as the monotonic clock source. On most modern systems, the internal processor clock can be used to improve performance. Cautions can be found here: http://oliveryang.net/2015/09/pitfalls-of-TSC-usage/

To build with support for the processor's internal instruction clock, use:

make CFLAGS="-DUSE_PROCESSOR_CLOCK"
Verbose build
Redis will build with a user-friendly colorized output by default. If you want to see a more verbose output, use the following:

make V=1
Running Redis with TLS
Please consult the TLS.md file for more information on how to use Redis with TLS.

