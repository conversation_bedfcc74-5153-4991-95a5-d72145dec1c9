TITLE: Using Socket.IO Cluster Engine with Node.js Cluster and Redis
DESCRIPTION: This Node.js example combines Node.js clustering with Redis pub/sub for communication. The primary process sets up the cluster and the Redis clients, using `setupPrimaryWithRedis`. Workers then use the `NodeClusterEngine`, which leverages the Redis setup for inter-process communication.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-cluster-engine/README.md#_snippet_3

LANGUAGE: JavaScript
CODE:
```
import cluster from "node:cluster";
import process from "node:process";
import { availableParallelism } from "node:os";
import { createClient } from "redis";
import { setupPrimaryWithRedis, NodeClusterEngine } from "@socket.io/cluster-engine";
import { createServer } from "node:http";
import { Server } from "socket.io";

if (cluster.isPrimary) {
  console.log(`Primary ${process.pid} is running`);

  const numCPUs = availableParallelism();

  // fork workers
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }

  const pubClient = createClient();
  const subClient = pubClient.duplicate();

  await Promise.all([
    pubClient.connect(),
    subClient.connect(),
  ]);

  // setup connection between and within the clusters
  setupPrimaryWithRedis(pubClient, subClient);

  // needed for packets containing Buffer objects (you can ignore it if you only send plaintext objects)
  cluster.setupPrimary({
    serialization: "advanced",
  });

  cluster.on("exit", (worker, code, signal) => {
    console.log(`worker ${worker.process.pid} died`);
  });
} else {
  const httpServer = createServer((req, res) => {
    res.writeHead(404).end();
  });

  const engine = new NodeClusterEngine();

  engine.attach(httpServer, {
    path: "/socket.io/"
  });

  const io = new Server();

  io.bind(engine);

  // workers will share the same port
  httpServer.listen(3000);

  console.log(`Worker ${process.pid} started`);
}
```

----------------------------------------

TITLE: Installing Socket.IO (Bash)
DESCRIPTION: These commands demonstrate how to install the Socket.IO library using the npm or yarn package managers. Installation is required to use Socket.IO in a Node.js project.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/Readme.md#_snippet_1

LANGUAGE: bash
CODE:
```
// with npm
npm install socket.io

// with yarn
yarn add socket.io
```

----------------------------------------

TITLE: Server Authenticating Handshake with Passport JWT (JavaScript)
DESCRIPTION: Server-side Socket.IO middleware that intercepts incoming engine requests. It checks if the request is the initial handshake and, if so, applies Passport-JWT authentication to validate the JWT provided by the client. Subsequent requests bypass this authentication step.
SOURCE: https://github.com/socketio/socket.io/blob/main/examples/passport-jwt-example/README.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
io.engine.use((req, res, next) => {
  const isHandshake = req._query.sid === undefined;
  if (isHandshake) {
    passport.authenticate("jwt", { session: false })(req, res, next);
  } else {
    next();
  }
});
```

----------------------------------------

TITLE: Handling Socket.IO Events (JavaScript)
DESCRIPTION: This snippet shows the basic structure for handling connections and events on the Socket.IO server. It demonstrates listening for new client connections, emitting events to a specific socket or broadcasting to all connected sockets, and listening for specific events sent from a client.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/Readme.md#_snippet_0

LANGUAGE: javascript
CODE:
```
io.on('connection', socket => {
  socket.emit('request', /* … */); // emit an event to the socket
  io.emit('broadcast', /* … */); // emit an event to all connected sockets
  socket.on('reply', () => { /* … */ }); // listen to the event
});
```

----------------------------------------

TITLE: Importing Socket.IO Client via CDN ESM Bundle in HTML
DESCRIPTION: This HTML snippet shows how to import the Socket.IO client library directly in a web browser using an ESM bundle provided via a Content Delivery Network (CDN). It then demonstrates connecting to the default socket endpoint and emitting a 'hello' event.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-client/CHANGELOG.md#_snippet_12

LANGUAGE: HTML
CODE:
```
<script type="module">
  import { io } from "https://cdn.socket.io/4.3.0/socket.io.esm.min.js";

  const socket = io();

  socket.emit("hello", "world");
</script>
```

----------------------------------------

TITLE: Implementing Acknowledgements - Socket.IO JavaScript
DESCRIPTION: This example illustrates how to implement packet acknowledgements in Socket.IO. On one side, a message is sent with a callback function that executes upon receiving the acknowledgement. On the other side, the event handler receives the message payload and a callback function to send the acknowledgement back.
SOURCE: https://github.com/socketio/socket.io/blob/main/docs/socket.io-protocol/v4.md#_snippet_1

LANGUAGE: javascript
CODE:
```
// on one side
socket.emit("hello", 1, () => { console.log("received"); });
// on the other side
socket.on("hello", (a, cb) => { cb(); });
```

----------------------------------------

TITLE: Configuring CORS with cors option (Node.js)
DESCRIPTION: Demonstrates the new method (4.0.0-alpha.0 and later) for configuring CORS using the dedicated cors option object in the Engine.IO server. It shows setting origin, methods, allowed headers, and credentials via structured options, replacing the handlePreflightRequest function.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/engine.io/CHANGELOG.md#_snippet_7

LANGUAGE: javascript
CODE:
```
new Server({
  cors: {
    origin: "https://example.com",
    methods: ["GET"],
    allowedHeaders: ["Authorization"],
    credentials: true
  }
})
```

----------------------------------------

TITLE: Integrating Socket.IO with Express (JavaScript)
DESCRIPTION: This snippet illustrates how to integrate Socket.IO with an Express application. It emphasizes creating a standard HTTP server using `http.createServer()` with the Express app as the request handler and then attaching Socket.IO to this HTTP server.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/Readme.md#_snippet_5

LANGUAGE: javascript
CODE:
```
const app = require('express')();
const server = require('http').createServer(app);
const io = require('socket.io')(server);
io.on('connection', () => { /* … */ });
server.listen(3000);
```

----------------------------------------

TITLE: Declaring Socket.IO Namespace (Server JS)
DESCRIPTION: Demonstrates how to declare a custom namespace on the server side using the Socket.IO Node.js API. It shows how to define the namespace object and attach a connection handler to listen for client connections to that specific namespace.
SOURCE: https://github.com/socketio/socket.io/blob/main/docs/socket.io-protocol/v5-current.md#_snippet_0

LANGUAGE: js
CODE:
```
// declare the namespace
const namespace = io.of("/admin");
// handle the connection to the namespace
namespace.on("connection", (socket) => {
  // ...
});
```

----------------------------------------

TITLE: Client Connecting with JWT Headers (JavaScript)
DESCRIPTION: Client-side JavaScript code showing how to initialize a Socket.IO connection and include a JWT in the HTTP headers using the `extraHeaders` option during instantiation. This token will be used for authentication on the server.
SOURCE: https://github.com/socketio/socket.io/blob/main/examples/passport-jwt-example/README.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
const socket = io({
  extraHeaders: {
    authorization: `bearer token`
  }
});
```

----------------------------------------

TITLE: Configuring Retry Mechanism for Socket.IO Client JS
DESCRIPTION: This snippet demonstrates how to configure the packet retry mechanism using the `retries` and `ackTimeout` options during client initialization. It shows how to set the maximum number of retries and the default timeout for waiting for an acknowledgement, followed by examples of event emissions with implicit, explicit, and custom timeout acknowledgements.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-client/CHANGELOG.md#_snippet_8

LANGUAGE: js
CODE:
```
const socket = io({
  retries: 3,
  ackTimeout: 10000
});
```

LANGUAGE: js
CODE:
```
// implicit ack
socket.emit("my-event");
```

LANGUAGE: js
CODE:
```
// explicit ack
socket.emit("my-event", (err, val) => { /* ... */ });
```

LANGUAGE: js
CODE:
```
// custom timeout (in that case the ackTimeout is optional)
socket.timeout(5000).emit("my-event", (err, val) => { /* ... */ });
```

----------------------------------------

TITLE: Integrating Socket.IO with Koa (JavaScript)
DESCRIPTION: This example shows how to integrate Socket.IO with a Koa application. It involves creating an HTTP server using the `http` module and passing the Koa application's `callback()` method to it, then attaching Socket.IO to this server.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/Readme.md#_snippet_6

LANGUAGE: javascript
CODE:
```
const app = require('koa')();
const server = require('http').createServer(app.callback());
const io = require('socket.io')(server);
io.on('connection', () => { /* … */ });
server.listen(3000);
```

----------------------------------------

TITLE: Using Socket Local Flag for Server-Local Emitting Socket.IO JS
DESCRIPTION: Demonstrates the use of the socket.local flag in Socket.IO to emit events only to clients connected to the specific server instance where the emission is triggered, bypassing the adapter's broadcasting mechanism for other nodes in a multi-server setup.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/CHANGELOG.md#_snippet_12

LANGUAGE: js
CODE:
```
socket.local.to('room101').emit(/* */);
```

----------------------------------------

TITLE: Integrating Socket.IO with Node.js HTTP Server (JavaScript)
DESCRIPTION: This example shows how to attach Socket.IO to a standard Node.js HTTP server. It requires creating an HTTP server instance first, then passing it to the Socket.IO constructor, and finally starting the HTTP server to listen on a port.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/Readme.md#_snippet_2

LANGUAGE: javascript
CODE:
```
const server = require('http').createServer();
const io = require('socket.io')(server);
io.on('connection', client => {
  client.on('event', data => { /* … */ });
  client.on('disconnect', () => { /* … */ });
});
server.listen(3000);
```

----------------------------------------

TITLE: Using Promise-based Acknowledgements with Socket.IO
DESCRIPTION: Illustrates the use of `emitWithAck()` and `serverSideEmitWithAck()` methods, which provide a Promise-based approach for handling acknowledgements from clients or other servers. Includes examples for emitting with and without timeouts, and how to catch errors if acknowledgements are not received within the specified delay.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/CHANGELOG.md#_snippet_1

LANGUAGE: javascript
CODE:
```
try {
  const responses = await io.timeout(1000).emitWithAck("some-event");
  console.log(responses); // one response per client
} catch (e) {
  // some clients did not acknowledge the event in the given delay
}

io.on("connection", async (socket) => {
    // without timeout
  const response = await socket.emitWithAck("hello", "world");

  // with a specific timeout
  try {
    const response = await socket.timeout(1000).emitWithAck("hello", "world");
  } catch (err) {
    // the client did not acknowledge the event in the given delay
  }
});
```

LANGUAGE: javascript
CODE:
```
try {
  const responses = await io.timeout(1000).serverSideEmitWithAck("some-event");
  console.log(responses); // one response per server (except itself)
} catch (e) {
  // some servers did not acknowledge the event in the given delay
}
```

----------------------------------------

TITLE: Implementing Socket.IO Packet Acknowledgement - JavaScript
DESCRIPTION: Illustrates how to send an event with an acknowledgement callback on one side and how the receiving side invokes the callback to send the acknowledgement back. This enables confirmation that a packet was received and processed.
SOURCE: https://github.com/socketio/socket.io/blob/main/docs/socket.io-protocol/v3.md#_snippet_1

LANGUAGE: javascript
CODE:
```
// on one side
socket.emit("hello", 1, () => { console.log("received"); });
// on the other side
socket.on("hello", (a, cb) => { cb(); });
```

----------------------------------------

TITLE: Using Promise-Based Acknowledgements Socket.IO Client JS
DESCRIPTION: This snippet illustrates the new promise-based syntax for handling event acknowledgements, providing a more modern asynchronous pattern. It shows how to emit an event and `await` the server's acknowledgement, demonstrating usage both without a timeout and with a specific timeout value using the `.timeout()` method and including error handling for timeout failures.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-client/CHANGELOG.md#_snippet_6

LANGUAGE: js
CODE:
```
// without timeout
const response = await socket.emitWithAck("hello", "world");
```

LANGUAGE: js
CODE:
```
// with a specific timeout
try {
  const response = await socket.timeout(1000).emitWithAck("hello", "world");
} catch (err) {
  // the server did not acknowledge the event in the given delay
}
```

----------------------------------------

TITLE: Using Socket.IO with ES Modules (JavaScript)
DESCRIPTION: This example shows how to import and instantiate the Socket.IO Server class using modern ES module syntax (`import`). It then attaches the server to an existing HTTP server instance.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/Readme.md#_snippet_4

LANGUAGE: javascript
CODE:
```
import { Server } from "socket.io";
const io = new Server(server);
io.listen(3000);
```

----------------------------------------

TITLE: Connecting to Socket.IO Namespaces (Client JS)
DESCRIPTION: Illustrates how a Socket.IO client connects to the main namespace ('/') and a custom namespace ('/admin'). It shows that multiple namespaces can share the same underlying WebSocket connection and how to attach a 'connect' event handler to a specific namespace connection.
SOURCE: https://github.com/socketio/socket.io/blob/main/docs/socket.io-protocol/v5-current.md#_snippet_1

LANGUAGE: js
CODE:
```
// reach the main namespace
const socket1 = io();
// reach the "/admin" namespace (with the same underlying WebSocket connection)
const socket2 = io("/admin");
// handle the connection to the namespace
socket2.on("connect", () => {
  // ...
});
```

----------------------------------------

TITLE: Defining and Connecting to Socket.IO Namespaces - JavaScript
DESCRIPTION: Demonstrates defining a custom namespace ('/admin') on the server side and handling new connections within that namespace. Also shows how a client connects to both the default namespace and a specific namespace.
SOURCE: https://github.com/socketio/socket.io/blob/main/docs/socket.io-protocol/v3.md#_snippet_0

LANGUAGE: javascript
CODE:
```
// server-side
const nsp = io.of("/admin");
nsp.on("connect", socket => {});

// client-side
const socket1 = io(); // default namespace
const socket2 = io("/admin");
socket2.on("connect", () => {});
```

----------------------------------------

TITLE: Enabling Connection State Recovery in Socket.IO
DESCRIPTION: Shows how to enable the connection state recovery feature on the Socket.IO server. This feature allows clients to restore their state (id, rooms, data, missed packets) after a temporary disconnection by configuring the `connectionStateRecovery` option with parameters like maximum disconnection duration.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/CHANGELOG.md#_snippet_2

LANGUAGE: javascript
CODE:
```
import { Server } from "socket.io";

const io = new Server({
  connectionStateRecovery: {
    // default values
    maxDisconnectionDuration: 2 * 60 * 1000,
    skipMiddlewares: true,
  },
});

io.on("connection", (socket) => {
  console.log(socket.recovered); // whether the state was recovered or not
});
```

----------------------------------------

TITLE: Handling Disconnect Event with Reason and Description
DESCRIPTION: Shows how to access additional details about a socket disconnection by listening to the `disconnect` event. The event handler receives the `reason` for disconnection and a `description`, providing more context than previous versions.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/CHANGELOG.md#_snippet_4

LANGUAGE: javascript
CODE:
```
io.on("connection", (socket) => {
  socket.on("disconnect", (reason, description) => {
    console.log(description);
  });
});
```

----------------------------------------

TITLE: Configuring Server CORS and Request Validation Socket.IO JS
DESCRIPTION: Shows how to configure Cross-Origin Resource Sharing (CORS) and validate incoming requests in Socket.IO v3 after the removal of the origins option. It provides examples using the standard cors option and the allowRequest function for custom logic.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/CHANGELOG.md#_snippet_10

LANGUAGE: js
CODE:
```
new Server(3000, {
  origins: ["https://example.com"]
});
```

LANGUAGE: js
CODE:
```
new Server(3000, {
  cors: {
    origin: "https://example.com",
    methods: ["GET", "POST"],
    allowedHeaders: ["content-type"]
  }
});
```

LANGUAGE: js
CODE:
```
new Server(3000, {
  allowRequest: (req, callback) => {
    callback(null, req.headers.referer.startsWith("https://example.com"));
  }
});
```

----------------------------------------

TITLE: Enabling Socket.IO Debug Logging (Bash)
DESCRIPTION: This command shows how to enable detailed debug output for all Socket.IO components by setting the `DEBUG` environment variable to `socket.io*` before running your Node.js application. This is helpful for diagnosing connection issues and understanding library behavior.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/Readme.md#_snippet_8

LANGUAGE: bash
CODE:
```
DEBUG=socket.io* node myapp
```

----------------------------------------

TITLE: Handling Connect Errors Socket.IO JavaScript Client
DESCRIPTION: Shows the change in event name for connection errors in Socket.IO client version 3.0.0. The event name was changed from "error" to "connect_error" to distinguish it from general error events. This snippet demonstrates the correct way to listen for connection errors in the new version.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-client/CHANGELOG.md#_snippet_13

LANGUAGE: javascript
CODE:
```
// before
socket.on("error", () => {});

// after
socket.on("connect_error", () => {});
```

----------------------------------------

TITLE: Using Namespaces - Socket.IO JavaScript
DESCRIPTION: This snippet demonstrates how to work with custom namespaces in Socket.IO. It shows the server-side code to create a namespace using `io.of('/admin')` and listen for connections, and the client-side code to connect to the default namespace and the specific '/admin' namespace.
SOURCE: https://github.com/socketio/socket.io/blob/main/docs/socket.io-protocol/v4.md#_snippet_0

LANGUAGE: javascript
CODE:
```
// server-side
const nsp = io.of("/admin");
nsp.on("connect", socket => {});

// client-side
const socket1 = io(); // default namespace
const socket2 = io("/admin");
socket2.on("connect", () => {});
```

----------------------------------------

TITLE: Running Socket.IO Server Standalone (JavaScript)
DESCRIPTION: This snippet demonstrates creating and running a Socket.IO server instance directly, allowing it to listen on a specified port without explicitly creating a separate HTTP server instance beforehand.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/Readme.md#_snippet_3

LANGUAGE: javascript
CODE:
```
const io = require('socket.io')();
io.on('connection', client => { ... });
io.listen(3000);
```

----------------------------------------

TITLE: Integrating Express-like Middlewares at Engine.IO Level
DESCRIPTION: Demonstrates how to use the `io.engine.use()` method to integrate standard Express-compatible middlewares (like `express-session` or `helmet`) into the Engine.IO layer. These middlewares are executed during the HTTP request/response cycle, unlike Socket.IO middlewares which are for namespace authorization.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/CHANGELOG.md#_snippet_3

LANGUAGE: javascript
CODE:
```
io.engine.use((req, res, next) => {
  // do something

  next();
});
```

LANGUAGE: javascript
CODE:
```
import session from "express-session";

io.engine.use(session({
  secret: "keyboard cat",
  resave: false,
  saveUninitialized: true,
  cookie: { secure: true }
}));
```

LANGUAGE: javascript
CODE:
```
import helmet from "helmet";

io.engine.use(helmet());
```

----------------------------------------

TITLE: Listening for Manager Events Socket.IO JavaScript Client
DESCRIPTION: Demonstrates how to listen for Manager-level events (like "reconnect") after a breaking change in Socket.IO client version 3.0.0. Previously, these events were forwarded directly to the Socket instance, but now they must be accessed via the `socket.io` (Manager) instance.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-client/CHANGELOG.md#_snippet_14

LANGUAGE: javascript
CODE:
```
socket.io.on("reconnect", () => {
  // ...
});
```

----------------------------------------

TITLE: Including Socket.IO Client Script - HTML
DESCRIPTION: This HTML snippet demonstrates how to include the Socket.IO client-side library in a web page. The script is typically served from the application server running Socket.IO, making the `io` object available globally for establishing connections.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/engine.io/README.md#_snippet_8

LANGUAGE: html
CODE:
```
<script src="/socket.io/socket.io.js">

```

----------------------------------------

TITLE: Integrating Socket.IO with Fastify (JavaScript)
DESCRIPTION: This snippet demonstrates integrating Socket.IO with a Fastify application using the `fastify-socket.io` plugin. After registering the plugin, the Socket.IO instance is accessible via the `app.io` decorator within the `ready()` promise.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/Readme.md#_snippet_7

LANGUAGE: javascript
CODE:
```
const app = require('fastify')();
app.register(require('fastify-socket.io'));
app.ready().then(() => {
    app.io.on('connection', () => { /* … */ });
})
app.listen(3000);
```

----------------------------------------

TITLE: Using Socket.IO Cluster Engine with Redis Pub/Sub
DESCRIPTION: This Node.js example demonstrates configuring Socket.IO with the RedisEngine. It involves setting up Redis clients for publishing and subscribing and then attaching the engine to an HTTP server and binding it to the Socket.IO server instance.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-cluster-engine/README.md#_snippet_2

LANGUAGE: JavaScript
CODE:
```
import { createServer } from "node:http";
import { createClient } from "redis";
import { RedisEngine } from "@socket.io/cluster-engine";
import { Server } from "socket.io";

const httpServer = createServer((req, res) => {
  res.writeHead(404).end();
});

const pubClient = createClient();
const subClient = pubClient.duplicate();

await Promise.all([
  pubClient.connect(),
  subClient.connect(),
]);

const engine = new RedisEngine(pubClient, subClient);

engine.attach(httpServer, {
  path: "/socket.io/"
});

const io = new Server();

io.bind(engine);

httpServer.listen(3000);
```

----------------------------------------

TITLE: Broadcasting with Timeout and Acknowledgements Socket.IO JavaScript
DESCRIPTION: This example shows how to use `io.timeout()` to broadcast an event to all connected sockets and wait for acknowledgements from each socket within the specified duration (1000ms). The callback receives an error object and an array of responses from the sockets.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/CHANGELOG.md#_snippet_8

LANGUAGE: javascript
CODE:
```
io.timeout(1000).emit("some-event", (err, responses) => {
  // ...
});
```

----------------------------------------

TITLE: Instantiating Engine.IO Server Node.js
DESCRIPTION: These examples show various ways to create and attach an Engine.IO `Server` instance using the top-level `require('engine.io')` function. It covers creating first and then attaching, calling the module as a function, immediate attachment, and attachment with custom options. Requires an existing `http.Server` instance.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/engine.io/README.md#_snippet_4

LANGUAGE: JavaScript
CODE:
```
const httpServer; // previously created with `http.createServer();` from node.js api.

// create a server first, and then attach
const eioServer = require('engine.io').Server();
eioServer.attach(httpServer);

// or call the module as a function to get `Server`
const eioServer = require('engine.io')();
eioServer.attach(httpServer);

// immediately attach
const eioServer = require('engine.io')(httpServer);

// with custom options
const eioServer = require('engine.io')(httpServer, {
  maxHttpBufferSize: 1e3
});
```

----------------------------------------

TITLE: Checking Connection Recovery Status Socket.IO Client JS
DESCRIPTION: This code shows how to access the new `recovered` boolean attribute on the socket object within the `connect` event listener. This attribute indicates whether the client successfully recovered its session and received missed packets after a temporary disconnection, provided the feature is enabled on the server.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-client/CHANGELOG.md#_snippet_7

LANGUAGE: js
CODE:
```
socket.on("connect", () => {
  console.log(socket.recovered); // whether the recovery was successful
});
```

----------------------------------------

TITLE: Initializing Socket for Tree-shaking - JavaScript
DESCRIPTION: This snippet shows how to use `SocketWithoutUpgrade` and specify a limited set of transports (`[WebSocket]`) to enable tree-shaking. This approach excludes the code for unused transports (like HTTP long-polling and WebTransport in this example) from the final bundle, reducing the client's size.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/engine.io-client/CHANGELOG.md#_snippet_1

LANGUAGE: javascript
CODE:
```
import { SocketWithoutUpgrade, WebSocket } from "engine.io-client";

const socket = new SocketWithoutUpgrade({
  transports: [WebSocket]
});
```

----------------------------------------

TITLE: Handling Socket.IO Disconnect Event with Details JS
DESCRIPTION: This code shows how the `disconnect` event listener now provides `reason` and `details` parameters for enhanced debugging. It demonstrates accessing these parameters to understand the cause of disconnection, including transport errors and associated context like HTTP status codes.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-client/CHANGELOG.md#_snippet_9

LANGUAGE: js
CODE:
```
socket.on("disconnect", (reason, details) => {
  console.log(reason); // "transport error"

  // in that case, details is an error object
  console.log(details.message); "xhr post error"
  console.log(details.description); // 413 (the HTTP status of the response)

  // details.context refers to the XMLHttpRequest object
  console.log(details.context.status); // 413
  console.log(details.context.responseText); // ""
});
```

----------------------------------------

TITLE: Applying Middleware to Socket.IO Handshake (JavaScript)
DESCRIPTION: Defines a helper function `onlyForHandshake` to conditionally apply standard Express middleware only during the Socket.IO connection handshake. It then uses this function to apply Passport session middleware and an authentication check to secure the connection engine.
SOURCE: https://github.com/socketio/socket.io/blob/main/examples/passport-example/README.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
function onlyForHandshake(middleware) {
  return (req, res, next) => {
    const isHandshake = req._query.sid === undefined;
    if (isHandshake) {
      middleware(req, res, next);
    } else {
      next();
    }
  };
}

io.engine.use(onlyForHandshake(sessionMiddleware));
io.engine.use(onlyForHandshake(passport.session()));
io.engine.use(
  onlyForHandshake((req, res, next) => {
    if (req.user) {
      next();
    } else {
      res.writeHead(401);
      res.end();
    }
  }),
);
```

----------------------------------------

TITLE: Handling Namespace Connections and Middleware Socket.IO JS
DESCRIPTION: Explains the change in Socket.IO v3 regarding implicit connections to the default namespace. It shows that io.on('connection') and io.use() on the root server instance are no longer triggered for namespace connections, requiring middleware registration directly on the namespace instance.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/CHANGELOG.md#_snippet_11

LANGUAGE: js
CODE:
```
// client-side
const socket = io("/admin");

// server-side
io.on("connection", socket => {
  // not triggered anymore
})

io.use((socket, next) => {
  // not triggered anymore
});
```

LANGUAGE: js
CODE:
```
io.of("/admin").use((socket, next) => {
  // triggered
});
```

----------------------------------------

TITLE: Setting Timeout for Event Acknowledgment in JavaScript
DESCRIPTION: This JavaScript snippet demonstrates how to set a timeout for an event emission using the `socket.timeout()` method. If the server does not acknowledge the event within the specified delay (5000ms), the callback function will be invoked with an error.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-client/CHANGELOG.md#_snippet_11

LANGUAGE: JavaScript
CODE:
```
socket.timeout(5000).emit("my-event", (err) => {
  if (err) {
    // the server did not acknowledge the event in the given delay
  }
});
```

----------------------------------------

TITLE: Attaching Engine.IO Server with Options Node.js
DESCRIPTION: This example shows how to attach an Engine.IO server to an existing `http.Server` instance using the `engine.attach` method with an options object. It demonstrates specifying a different WebSocket engine, like 'eiows', which requires installing it as a dependency. Requires 'engine.io', 'http', and the specified `wsEngine` dependency.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/engine.io/README.md#_snippet_6

LANGUAGE: JavaScript
CODE:
```
const engine = require('engine.io');
const httpServer = require('http').createServer().listen(3000);
const server = engine.attach(httpServer, {
  wsEngine: require('eiows').Server // requires having eiows as dependency
});

server.on('connection', /* ... */);
```

----------------------------------------

TITLE: Attaching Engine.IO Server to existing http.Server Node.js
DESCRIPTION: This code illustrates how to attach an Engine.IO server to an existing Node.js `http.Server` instance that is already listening. It shows how to handle new connections and register listeners for 'message' and 'close' events on the connected socket. Requires 'engine.io' and the built-in 'http' module.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/engine.io/README.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
const engine = require('engine.io');
const http = require('http').createServer().listen(3000);
const server = engine.attach(http);

server.on('connection', socket => {
  socket.on('message', data => { });
  socket.on('close', () => { });
});
```

----------------------------------------

TITLE: Listening Engine.IO Server with Options Node.js
DESCRIPTION: This snippet demonstrates using the `engine.listen` method with an options object. It creates an HTTP server listening on port 3000 and attaches Engine.IO to it, configuring parameters like `pingTimeout` and `pingInterval`. Requires the 'engine.io' module.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/engine.io/README.md#_snippet_5

LANGUAGE: JavaScript
CODE:
```
const engine = require('engine.io');
const server = engine.listen(3000, {
  pingTimeout: 2000,
  pingInterval: 10000
});

server.on('connection', /* ... */);
```

----------------------------------------

TITLE: Using Socket.IO Cluster Engine with Node.js Cluster
DESCRIPTION: This Node.js example demonstrates how to integrate the NodeClusterEngine with the built-in Node.js cluster module. It sets up a primary process to manage workers and inter-worker communication, while each worker runs a Socket.IO server instance attached to the cluster engine.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-cluster-engine/README.md#_snippet_1

LANGUAGE: JavaScript
CODE:
```
import cluster from "node:cluster";
import process from "node:process";
import { availableParallelism } from "node:os";
import { setupPrimary, NodeClusterEngine } from "@socket.io/cluster-engine";
import { createServer } from "node:http";
import { Server } from "socket.io";

if (cluster.isPrimary) {
  console.log(`Primary ${process.pid} is running`);

  const numCPUs = availableParallelism();

  // fork workers
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }

  // setup connection within the cluster
  setupPrimary();

  // needed for packets containing Buffer objects (you can ignore it if you only send plaintext objects)
  cluster.setupPrimary({
    serialization: "advanced",
  });

  cluster.on("exit", (worker, code, signal) => {
    console.log(`worker ${worker.process.pid} died`);
  });
} else {
  const httpServer = createServer((req, res) => {
    res.writeHead(404).end();
  });

  const engine = new NodeClusterEngine();

  engine.attach(httpServer, {
    path: "/socket.io/"
  });

  const io = new Server();

  io.bind(engine);

  // workers will share the same port
  httpServer.listen(3000);

  console.log(`Worker ${process.pid} started`);
}
```

----------------------------------------

TITLE: Configuring Custom Transports in Socket.IO Client (JavaScript)
DESCRIPTION: Demonstrates how to explicitly specify the transport mechanisms the Socket.IO client should attempt to use. The `transports` option accepts an array of transport constructors imported from `engine.io-client`. This allows developers to prioritize or limit the transports based on the client or server environment.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-client/CHANGELOG.md#_snippet_0

LANGUAGE: javascript
CODE:
```
import { io } from "socket.io-client";
import { XHR, WebSocket } from "engine.io-client";

const socket = io({
  transports: [XHR, WebSocket]
});
```

----------------------------------------

TITLE: Providing WebTransport Polyfill for Node.js Socket.IO Client (JavaScript)
DESCRIPTION: Shows how to make a WebTransport implementation available for the Socket.IO client in a Node.js environment. It involves importing a WebTransport polyfill package (`@fails-components/webtransport`) and assigning it to the global `WebTransport` property, allowing the client to utilize the WebTransport transport option if supported.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-client/CHANGELOG.md#_snippet_2

LANGUAGE: javascript
CODE:
```
import { WebTransport } from "@fails-components/webtransport";

global.WebTransport = WebTransport;
```

----------------------------------------

TITLE: Enabling Debug Logging for Engine.IO Bash
DESCRIPTION: This command enables verbose debugging output for all components within the Engine.IO library using the `debug` module. Set the `DEBUG` environment variable to `engine*` before running your Node.js application to see detailed logs related to Engine.IO operations.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/engine.io/README.md#_snippet_7

LANGUAGE: bash
CODE:
```
DEBUG=engine* node myapp
```

----------------------------------------

TITLE: Handling Engine.IO Client Socket Close Event - JavaScript
DESCRIPTION: This snippet shows how to handle the 'close' event emitted by an Engine.IO client socket. The event callback receives a `reason` string and a `details` object, which provides additional context for debugging transport errors, such as HTTP status codes or message descriptions.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/engine.io-client/CHANGELOG.md#_snippet_6

LANGUAGE: javascript
CODE:
```
socket.on("close", (reason, details) => {
  console.log(reason); // "transport error"

  // in that case, details is an error object
  console.log(details.message); "xhr post error"
  console.log(details.description); // 413 (the HTTP status of the response)

  // details.context refers to the XMLHttpRequest object
  console.log(details.context.status); // 413
  console.log(details.context.responseText); // ""
});
```

----------------------------------------

TITLE: Connecting Engine.IO Client Browser JavaScript
DESCRIPTION: This HTML script demonstrates connecting to an Engine.IO server from a browser using the client library (`engine.io.js`). It shows how to create a new `eio.Socket` instance and handle 'open', 'message', and 'close' events. The client library must be included via a `<script>` tag.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/engine.io/README.md#_snippet_3

LANGUAGE: HTML
CODE:
```
<script src="/path/to/engine.io.js"></script>
<script>
  const socket = new eio.Socket('ws://localhost/');
  socket.on('open', () => {
    socket.on('message', data => {});
    socket.on('close', () => {});
  });
</script>
```

----------------------------------------

TITLE: Initializing Socket with Custom Transports - JavaScript
DESCRIPTION: This snippet demonstrates how to initialize an Engine.IO client `Socket` instance using a custom array of transport implementations. By providing an array like `[XHR, WebSocket]`, the client will only attempt to use these specified transports in order, overriding the default transport list. This requires importing the desired transport classes.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/engine.io-client/CHANGELOG.md#_snippet_0

LANGUAGE: javascript
CODE:
```
import { Socket, XHR, WebSocket } from "engine.io-client";

const socket = new Socket({
  transports: [XHR, WebSocket]
});
```

----------------------------------------

TITLE: Implementing Dynamic Namespaces Socket.IO JS
DESCRIPTION: Illustrates how to create and connect to dynamic namespaces in Socket.IO using regular expressions on the server-side. This allows handling connections to multiple namespaces with similar naming patterns using a single listener.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io/CHANGELOG.md#_snippet_14

LANGUAGE: js
CODE:
```
io.of(/^\/dynamic-\d+$/).on('connect', (socket) => {
  // socket.nsp.name = '/dynamic-101'
});
```

LANGUAGE: js
CODE:
```
const client = require('socket.io-client')('/dynamic-101');
```

----------------------------------------

TITLE: Initializing Engine.IO Client in Node.js
DESCRIPTION: This snippet demonstrates the basic usage of the Engine.IO client in a Node.js environment. It requires the module and creates a new Socket instance, setting up listeners for standard events like 'open', 'message', and 'close'.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/engine.io-client/README.md#_snippet_2

LANGUAGE: javascript
CODE:
```
const { Socket } = require('engine.io-client');
const socket = new Socket('ws://localhost');
socket.on('open', () => {
  socket.on('message', (data) => {});
  socket.on('close', () => {});
});
```

----------------------------------------

TITLE: Starting Docker Compose Stack - Shell
DESCRIPTION: This command starts all services defined in the `docker-compose.yml` file in detached mode (`-d`). It is used to launch the Socket.IO chat application, including the HAProxy load balancer, Redis backend, and multiple Socket.IO nodes. Requires Docker Compose installation.
SOURCE: https://github.com/socketio/socket.io/blob/main/examples/cluster-haproxy/README.md#_snippet_0

LANGUAGE: Shell
CODE:
```
$ docker-compose up -d
```

----------------------------------------

TITLE: Starting Socket.IO Chat Demo with Docker Compose (Shell)
DESCRIPTION: This command starts the Socket.IO chat demo using Docker Compose. It builds, creates, and starts all services defined in the docker-compose.yml file in detached mode.
SOURCE: https://github.com/socketio/socket.io/blob/main/examples/cluster-traefik/README.md#_snippet_0

LANGUAGE: shell
CODE:
```
$ docker-compose up -d
```

----------------------------------------

TITLE: Running the Express/Socket.IO Example (Shell)
DESCRIPTION: These commands are used to set up and start the example application. The first command installs necessary Node.js dependencies listed in the package.json file. The second command starts the server, typically listening on port 3000.
SOURCE: https://github.com/socketio/socket.io/blob/main/examples/express-session-example/README.md#_snippet_0

LANGUAGE: Shell
CODE:
```
$ npm install
$ npm start
```

----------------------------------------

TITLE: Enabling Credentials for Node.js Socket.IO Client (JavaScript)
DESCRIPTION: Configures the Socket.IO client running in Node.js to send credentials, specifically cookies, with HTTP requests used by transports like HTTP long-polling. Setting `withCredentials` to `true` is necessary for integrating the client with server setups relying on cookie-based sticky sessions for session persistence.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-client/CHANGELOG.md#_snippet_3

LANGUAGE: javascript
CODE:
```
import { io } from "socket.io-client";

const socket = io("https://example.com", {
  withCredentials: true
});
```

----------------------------------------

TITLE: Enabling Try All Transports Option in Socket.IO Client (JavaScript)
DESCRIPTION: Configures the Socket.IO client to automatically cycle through all configured transport options if the initial connection attempt fails. Setting `tryAllTransports` to `true` ensures resilience in environments where certain transports might be blocked or unavailable.
SOURCE: https://github.com/socketio/socket.io/blob/main/packages/socket.io-client/CHANGELOG.md#_snippet_1

LANGUAGE: javascript
CODE:
```
import { io } from "socket.io-client";

const socket = io({
  tryAllTransports: true
});
```
