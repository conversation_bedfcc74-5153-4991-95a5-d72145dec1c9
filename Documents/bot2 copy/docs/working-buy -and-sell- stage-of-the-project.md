18 files changed
+1678
-0
lines changed
Search within code

‎.github/copilot-instructions.md
+130
Lines changed: 130 additions & 0 deletions


Original file line number	Diff line number	Diff line change
@@ -0,0 +1,130 @@
---
description: Project-wide Copilot agent coding instructions for JS, TS, HTML, CSS, Tailwind, and Python
applyTo: "**"
alwaysApply: true
---
# Copilot Instructions
These are the main, always-on AI coding guidelines for this repository.
All contributors and AI agents must follow these rules to ensure code quality, maintainability, and security.
- Write clear, concise, and well-documented code.
- Follow the style guide and best practices for each language.
- Prioritize security and privacy in all code.
- Ensure all code is testable and covered by automated tests.
- Use descriptive commit messages and PR descriptions.
- plan.md file for coding whole Project
## JavaScript & TypeScript
- Use **ES6+ features** unless otherwise specified.
- Variable names: `camelCase`
- Function names: `camelCase`
- Class names: `PascalCase`
- Use `const` and `let`; avoid `var`.
- Prefer arrow functions for callbacks and anonymous functions.
- Always use **single quotes** for strings.
- Use 2 spaces for indentation.
- Write **JSDoc** comments for all public functions and classes.
- Ensure all async code uses `async/await`.
- Always handle errors gracefully.
- Keep functions short and modular.
**TypeScript-specific:**
- Always specify explicit types for function parameters and return values.
- Use interfaces for object shapes.
- Prefer enums and type aliases for unions and options.
- Do not use `any`; use more specific types.
- Avoid using `as` type assertions unless necessary.
---
## HTML & CSS
    Key Principles
    - Write semantic HTML to improve accessibility and SEO.
    - Use CSS for styling, avoiding inline styles.
    - Ensure responsive design using media queries and flexible layouts.
    - Prioritize accessibility by using ARIA roles and attributes.
    HTML
    - Use semantic elements (e.g., <header>, <main>, <footer>, <article>, <section>).
    - Use <button> for clickable elements, not <div> or <span>.
    - Use <a> for links, ensuring href attribute is present.
    - Use <img> with alt attribute for images.
    - Use <form> for forms, with appropriate input types and labels.
    - Avoid using deprecated elements (e.g., <font>, <center>).
    CSS
    - Use external stylesheets for CSS.
    - Use class selectors over ID selectors for styling.
    - Use Flexbox and Grid for layout.
    - Use rem and em units for scalable and accessible typography.
    - Use CSS variables for consistent theming.
    - Use BEM (Block Element Modifier) methodology for naming classes.
    - Avoid !important; use specificity to manage styles.
    Responsive Design
    - Use media queries to create responsive layouts.
    - Use mobile-first approach for media queries.
    - Ensure touch targets are large enough for touch devices.
    - Use responsive images with srcset and sizes attributes.
    - Use viewport meta tag for responsive scaling.
    Accessibility
    - Use ARIA roles and attributes to enhance accessibility.
    - Ensure sufficient color contrast for text.
    - Provide keyboard navigation for interactive elements.
    - Use focus styles to indicate focus state.
    - Use landmarks (e.g., <nav>, <main>, <aside>) for screen readers.
    Performance
    - Minimize CSS and HTML file sizes.
    - Use CSS minification and compression.
    - Avoid excessive use of animations and transitions.
    - Use lazy loading for images and other media.
    Testing
    - Test HTML and CSS in multiple browsers and devices.
    - Use tools like Lighthouse for performance and accessibility audits.
    - Validate HTML and CSS using W3C validators.
    Documentation
    - Comment complex CSS rules and HTML structures.
    - Use consistent naming conventions for classes and IDs.
    - Document responsive breakpoints and design decisions.
    Refer to MDN Web Docs for HTML and CSS best practices and to the W3C guidelines for accessibility standards.
---
## Tailwind CSS
- Prefer Tailwind utility classes over writing custom CSS.
- Organize classes by layout → spacing → color → typography → effects.
- Use responsive and state variants (`md:`, `hover:`, etc.) where needed.
- Do not override Tailwind defaults unless justified in code comments.
---
## Python
- Follow [PEP 8](https://peps.python.org/pep-0008/) style guide.
- Use 4 spaces for indentation.
- Variable names: `snake_case`
- Function names: `snake_case`
- Class names: `PascalCase`
- Write docstrings for all public functions and classes.
- Use type hints for function arguments and return types.
- Prefer f-strings for string formatting.
- Handle exceptions with try/except blocks; log errors as needed.
- Write unit tests for all functions in the `/tests/` directory.
- Avoid global variables unless absolutely necessary.
- Keep functions short and modular.
---
**References:**
‎.gitignore
+3
Lines changed: 3 additions & 0 deletions
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,3 @@
.env
node_modules/
*.log
‎README.md
+5
Lines changed: 5 additions & 0 deletions


Original file line number	Diff line number	Diff line change
@@ -0,0 +1,5 @@
# Solana Meme‑Coin Sniper Bot – Buy/Sell Engine
A concise, linear playbook to scaffold and wire a fully‑working buy & sell core for Solana meme-coin sniping.
See `plan.md` for full instructions and architecture.
‎bot/autoSell.js
+43
Lines changed: 43 additions & 0 deletions
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,43 @@
import { Connection, PublicKey } from '@solana/web3.js';
import { wallet } from './wallet.js';
import { CONFIG } from './config.js';
import { sellToken } from './sell.js';
const BONK_MINT = new PublicKey('DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263');
const connection = new Connection(CONFIG.rpcUrl, 'confirmed');
async function getTokenBalance() {
  const accs = await connection.getTokenAccountsByOwner(
    wallet.publicKey,
    { mint: BONK_MINT },
    'confirmed'
  );
  if (!accs.value.length) return '0';
  const { amount } = await connection.getTokenAccountBalance(accs.value[0].pubkey);
  return amount;
}
async function autoSell(delayMinutes = 1) {
  console.log(`⏰ Setting auto-sell for ${delayMinutes} minutes...`);
  setTimeout(async () => {
    try {
      const lamports = await getTokenBalance();
      if (lamports === '0') {
        console.log('No tokens to sell!');
        return;
      }
      console.log(`Found ${lamports} lamports to sell...`);
      const txid = await sellToken(BONK_MINT.toString(), lamports);
      console.log(`Auto-sell complete! TX: ${txid}`);
    } catch (err) {
      console.error('Auto-sell failed:', err);
    }
  }, delayMinutes * 60 * 1000);
}
// Usage:
// import { autoSell } from './autoSell.js';
// await autoSell(10); // Sell after 10 minutes
‎bot/buy.js
+14
Lines changed: 14 additions & 0 deletions
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,14 @@
import { LAMPORTS_PER_SOL } from '@solana/web3.js';
import { CONFIG } from './config.js';
import { getQuote, getSwapTx, executeSerializedTx } from './jupiterApiClient.js';
const WSOL = 'So11111111111111111111111111111111111111112';
export async function buyToken(tokenMint) {
  const lamports = CONFIG.buyAmountSOL * LAMPORTS_PER_SOL;
  const quote = await getQuote(WSOL, tokenMint, lamports, CONFIG.slippagePct * 100);
  const b64Tx = await getSwapTx(quote);
  const sig = await executeSerializedTx(b64Tx);
  console.log(`BUY ✅ ${sig}`);
  return sig;
}
‎bot/config.js
+20
Lines changed: 20 additions & 0 deletions
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,20 @@
import 'dotenv/config';
export const CONFIG = {
  // ───────────────────────── Execution size
  buyAmountSOL: 0.015,          // ≈ $2.35 per snipe → four bullets in the clip
                                 // (leaves ~0.001 SOL buffer for gas + fees)
  // ───────────────────────── Swap safety
  slippagePct: 2,               // 2 % max ‒ tiny caps can move fast
  // ───────────────────────── Exit logic
  profitTargets: [1.10, 1.25, 1.50, 2.0], // sell 10 %, 25 %, 50 %, 100 % gains
  stopLoss: 0.85,               // cut if price falls -15 %
  // ───────────────────────── Infra
  rpcUrl: process.env.RPC_URL,  // Helius
  jupiter: {
    endpoint: process.env.JUP_ENDPOINT // lite-api.jup.ag for free tier
  }
};
‎bot/jupiterApiClient.js
+72
Lines changed: 72 additions & 0 deletions
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,72 @@
import axios from 'axios';
import { Connection, VersionedTransaction } from '@solana/web3.js';
import { wallet } from './wallet.js';
import { CONFIG } from './config.js';
import 'dotenv/config';
const RPC = CONFIG.rpcUrl;
// Validate RPC URL
if (!RPC || !RPC.startsWith('https://')) {
  throw new Error(`Invalid RPC URL: ${RPC}. Make sure your .env file is loaded correctly.`);
}
const JUP = CONFIG.jupiter.endpoint;
// Validate Jupiter endpoint
if (!JUP || !JUP.startsWith('https://')) {
  throw new Error(`Invalid Jupiter endpoint: ${JUP}. Make sure your .env file is loaded correctly.`);
}
const connection = new Connection(RPC, 'confirmed');
// STEP 1 – Quote
export async function getQuote(inMint, outMint, amountLamports, slippageBps) {
  const url = `${JUP}/swap/v1/quote`;
  const params = {
    inputMint: inMint,
    outputMint: outMint,
    amount: amountLamports.toString(),
    slippageBps
  };
  const headers = process.env.JUP_API_KEY
    ? { 'x-api-key': process.env.JUP_API_KEY }
    : {};
  const { data } = await axios.get(url, { params, headers });
  return data;
}
// STEP 2 – Build swap transaction
export async function getSwapTx(quoteResp) {
  const url = `${JUP}/swap/v1/swap`;
  const body = {
    quoteResponse: quoteResp,
    userPublicKey: wallet.publicKey.toString(),
    wrapAndUnwrapSol: true
  };
  const headers = {
    'Content-Type': 'application/json',
    ...(process.env.JUP_API_KEY && { 'x-api-key': process.env.JUP_API_KEY })
  };
  const { data } = await axios.post(url, body, { headers });
  return data.swapTransaction;
}
// STEP 3 – Sign & send
export async function executeSerializedTx(b64Tx) {
  const buf = Buffer.from(b64Tx, 'base64');
  const tx = VersionedTransaction.deserialize(buf);
  tx.sign([wallet]);
  const { blockhash, lastValidBlockHeight } =
    await connection.getLatestBlockhash();
  const sig = await connection.sendRawTransaction(tx.serialize(), {
    skipPreflight: true,
    maxRetries: 3
  });
  await connection.confirmTransaction(
    { signature: sig, blockhash, lastValidBlockHeight },
    'confirmed'
  );
  return sig;
}
‎bot/jupiterClient.js
+20
Lines changed: 20 additions & 0 deletions
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,20 @@
import { Connection, PublicKey } from '@solana/web3.js';
import { Jupiter } from '@jup-ag/core';
import { CONFIG } from './config.js';
const connection = new Connection(CONFIG.rpcUrl, 'confirmed');
export async function getBestRoute(inMint, outMint, amountLamports) {
  const jup = await Jupiter.load({ connection, cluster: 'mainnet-beta' });
  const { routesInfos } = await jup.computeRoutes({
    inputMint: new PublicKey(inMint),
    outputMint: new PublicKey(outMint),
    amount: amountLamports,
    slippageBps: CONFIG.slippagePct * 100,
  });
  return routesInfos?.[0] ?? null;
}
export async function executeRoute(route, wallet) {
  return route.execute({ wallet, feeAccount: null });
}
‎bot/sell.js
+13
Lines changed: 13 additions & 0 deletions
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,13 @@
import { LAMPORTS_PER_SOL } from '@solana/web3.js';
import { CONFIG } from './config.js';
import { getQuote, getSwapTx, executeSerializedTx } from './jupiterApiClient.js';
const WSOL = 'So11111111111111111111111111111111111111112';
export async function sellToken(tokenMint, amountLamports) {
  const quote = await getQuote(tokenMint, WSOL, amountLamports, CONFIG.slippagePct * 100);
  const b64Tx = await getSwapTx(quote);
  const sig = await executeSerializedTx(b64Tx);
  console.log(`SELL ✅ ${sig}`);
  return sig;
}
‎bot/wallet.js
+7
Lines changed: 7 additions & 0 deletions
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,7 @@
import { Keypair } from '@solana/web3.js';
import bs58 from 'bs58';
import 'dotenv/config';
export const wallet = Keypair.fromSecretKey(
  bs58.decode(process.env.PRIVATE_KEY)
);
‎package-lock.json
+1,012
Lines changed: 1012 additions & 0 deletions
Some generated files are not rendered by default. Learn more about customizing how changed files appear on GitHub.
‎package.json
+20
Lines changed: 20 additions & 0 deletions
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,20 @@
{
  "name": "solana-sniper-bot",
  "version": "1.0.0",
  "description": "Solana Meme-Coin Sniper Bot – Buy/Sell Engine",
  "main": "index.js",
  "type": "module",
  "scripts": {
    "test": "node test/buyOne.js"
  },
  "keywords": [],
  "author": "",
  "license": "ISC",
  "dependencies": {
    "@jup-ag/api": "^6.0.42",
    "@solana/web3.js": "^1.98.2",
    "axios": "^1.9.0",
    "bs58": "^6.0.0",
    "dotenv": "^16.5.0"
  }
}
‎plan.md
+221
Lines changed: 221 additions & 0 deletions


Original file line number	Diff line number	Diff line change
@@ -0,0 +1,221 @@
# Solana Meme‑Coin Sniper Bot – **Buy/Sell Engine**
> **Purpose**  A concise, linear playbook for VS Code / Copilot Agent to scaffold and wire a fully‑working buy & sell core. Follow sections **in order**; each step is self‑contained and build‑ready.
---
## 1  Folder Layout
```text
solana-sniper-bot/
  ├─ bot/                # business logic
  │   ├─ buy.js
  │   ├─ sell.js
  │   ├─ jupiterClient.js
  │   ├─ wallet.js
  │   └─ config.js
  ├─ test/
  │   └─ buyOne.js       # smoke test
  ├─ utils/              # (empty for now)
  ├─ .env
  ├─ .gitignore
  ├─ package.json
  └─ README.md           # optional
```
---
## 2  Initialise Project
```bash
mkdir solana-sniper-bot && cd $_
npm init -y                    # default package.json
mkdir bot utils test           # create working dirs
touch .env .gitignore          # secrets & ignores
```
Add to **.gitignore**:
```
.env
node_modules/
*.log
```
---
## 3  Install Core Dependencies
```bash
npm install @solana/web3.js \
            @jup-ag/core \
            dotenv bs58
```
**Why these?**
| Package           | Reason                               |
| ----------------- | ------------------------------------ |
| `@solana/web3.js` | Raw RPC, TX building, keypairs       |
| `@jup-ag/core`    | Finds + executes best DEX swap route |
| `dotenv`          | Loads secrets from `.env` safely     |
| `bs58`            | Decodes base58 wallet secret         |
---
## 4  Set Environment Variables (`.env`)
```env
# base58 private key (one long line)
PRIVATE_KEY=
# Helius mainnet endpoint with your API key
RPC_URL=https://mainnet.helius-rpc.com/?api-key=YOUR_KEY
```
> **Security Tip** – keep only enough SOL for testing in this hot wallet.
---
## 5  Static Config (`bot/config.js`)
```js
export const CONFIG = {
  buyAmountSOL: 0.5,            // SOL spent per snipe
  slippagePct: 1,               // 1 % max slippage
  profitTargets: [1.05, 1.10, 1.5, 2],
  stopLoss: 0.90,               // sell if ‑10 %
  rpcUrl: process.env.RPC_URL,
};
```
*Single source of tweakable parameters.*
---
## 6  Wallet Loader (`bot/wallet.js`)
```js
import { Keypair } from '@solana/web3.js';
import bs58 from 'bs58';
import 'dotenv/config';
export const wallet = Keypair.fromSecretKey(
  bs58.decode(process.env.PRIVATE_KEY)
);
```
*Converts the secret key string into a reusable **`Keypair`**.*
---
## 7  Jupiter Helper (`bot/jupiterClient.js`)
```js
import { Connection, PublicKey } from '@solana/web3.js';
import { Jupiter } from '@jup-ag/core';
import { CONFIG } from './config.js';
const connection = new Connection(CONFIG.rpcUrl, 'confirmed');
export async function getBestRoute(inMint, outMint, amountLamports) {
  const jup = await Jupiter.load({ connection, cluster: 'mainnet-beta' });
  const { routesInfos } = await jup.computeRoutes({
    inputMint: new PublicKey(inMint),
    outputMint: new PublicKey(outMint),
    amount: amountLamports,
    slippageBps: CONFIG.slippagePct * 100,
  });
  return routesInfos?.[0] ?? null;
}
export async function executeRoute(route, wallet) {
  return route.execute({ wallet, feeAccount: null });
}
```
*Wraps Jupiter’s heavy lifting behind two async helpers.*
---
## 8  Buy Logic (`bot/buy.js`)
```js
import { LAMPORTS_PER_SOL } from '@solana/web3.js';
import { CONFIG } from './config.js';
import { wallet } from './wallet.js';
import { getBestRoute, executeRoute } from './jupiterClient.js';
const WSOL = 'So11111111111111111111111111111111111111112';
export async function buyToken(tokenMint) {
  const amt = CONFIG.buyAmountSOL * LAMPORTS_PER_SOL;
  const route = await getBestRoute(WSOL, tokenMint, amt);
  if (!route) throw new Error('No buy route found');
  const { txid } = await executeRoute(route, wallet);
  console.log(`BUY ✅ ${txid}`);
  return txid;
}
```
---
## 9  Sell Logic (`bot/sell.js`)
```js
import { wallet } from './wallet.js';
import { getBestRoute, executeRoute } from './jupiterClient.js';
const WSOL = 'So11111111111111111111111111111111111111112';
export async function sellToken(tokenMint, amountLamports) {
  const route = await getBestRoute(tokenMint, WSOL, amountLamports);
  if (!route) throw new Error('No sell route');
  const { txid } = await executeRoute(route, wallet);
  console.log(`SELL ✅ ${txid}`);
  return txid;
}
```
---
## 10  Smoke Test (`test/buyOne.js`)
```js
import { buyToken } from '../bot/buy.js';
await buyToken('TOKEN_MINT_HERE');   // replace
process.exit();
```
Run:
```bash
node --experimental-modules test/buyOne.js
```
Expect: console logs a real transaction signature.
---
## 11  Next‑Step TODOs (outside core scope)
- **Price oracle** – query Birdeye every \~10 s, decide when to trigger sells.
- **ROI tracker** – persist buys & sells to SQLite / Supabase for later analytics.
- **Safety checks** – integrate RugCheck + Helius *simulateTransaction* before buy.
- **Dashboard** – Node REST or Socket.IO → React/Tailwind front‑end.
- **Telegram bridge** – GramJS scraper feeds `buyToken()`.
---
## 12  Security & Ops
1. Keep hot wallet under **1 SOL**; sweep profits daily.
2. Use `` for all secrets; lock file permissions.
3. Enable **Helius rate‑limit** (set HTTP `Origin` header or IP whitelist).
4. Plan CI with *Jupiter mocked* to avoid spending SOL in tests.
---
**End of plan.md**
‎test/buyOne.js
+19
Lines changed: 19 additions & 0 deletions
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,19 @@
import 'dotenv/config';
import { buyToken } from '../bot/buy.js';
// PUMP token address
const PUMP = '8SQQ1urC3Dynq9C2ieM6AozWgi4GCrLE6fnRoiWdpump';
async function main() {
  try {
    console.log('Starting test buy...');
    const sig = await buyToken(PUMP);
    console.log('Buy successful! Transaction:', sig);
  } catch (err) {
    console.error('Error:', err);
    process.exit(1);
  }
  process.exit(0);
}
main();
‎test/checkBalance.js
+34
Lines changed: 34 additions & 0 deletions
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,34 @@
import { Connection, PublicKey } from '@solana/web3.js';
import { wallet } from '../bot/wallet.js';
import { CONFIG } from '../bot/config.js';
const connection = new Connection(CONFIG.rpcUrl, 'confirmed');
const BONK_MINT = new PublicKey('DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263'); // BONK token
async function checkBalance() {
  const accs = await connection.getTokenAccountsByOwner(
    wallet.publicKey,
    { mint: BONK_MINT }
  );
  if (!accs.value.length) {
    console.log('No BONK tokens found in wallet:', wallet.publicKey.toString());
    return '0';
  }
  const { amount } = await connection.getTokenAccountBalance(
    accs.value[0].pubkey
  );
  console.log('Token Account Balance:', {
    lamports: amount,
    raw: await connection.getTokenAccountBalance(accs.value[0].pubkey)
  });
  console.log('Token account:', accs.value[0].pubkey.toString());
  return amount;
}
checkBalance().catch(err => {
  console.error('Error checking balance:', err);
  process.exit(1);
});
‎test/checkPumpBalance.js
+33
Lines changed: 33 additions & 0 deletions
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,33 @@
import { Connection, PublicKey } from '@solana/web3.js';
import { wallet } from '../bot/wallet.js';
import { CONFIG } from '../bot/config.js';
const connection = new Connection(CONFIG.rpcUrl, 'confirmed');
const PUMP_MINT = new PublicKey('8SQQ1urC3Dynq9C2ieM6AozWgi4GCrLE6fnRoiWdpump');
async function checkBalance() {
  const accs = await connection.getTokenAccountsByOwner(
    wallet.publicKey,
    { mint: PUMP_MINT }
  );
  if (!accs.value.length) {
    console.log('No PUMP tokens found in wallet:', wallet.publicKey.toString());
    return '0';
  }
  const { amount } = await connection.getTokenAccountBalance(
    accs.value[0].pubkey
  );
  console.log('PUMP Token Balance:', {
    raw: await connection.getTokenAccountBalance(accs.value[0].pubkey)
  });
  console.log('Token account:', accs.value[0].pubkey.toString());
  return amount;
}
checkBalance().catch(err => {
  console.error('Error checking balance:', err);
  process.exit(1);
});
‎test/sellOne.js
+12
Lines changed: 12 additions & 0 deletions
Original file line number	Diff line number	Diff line change
@@ -0,0 +1,12 @@
import { sellToken } from '../bot/sell.js';
const BONK_MINT = 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263';
const TOTAL_AMOUNT = 12643562297n; // Current balance after first sale
const SELL_PERCENTAGE = 10; // Sell 10% as a test
const AMOUNT = (TOTAL_AMOUNT * BigInt(SELL_PERCENTAGE) / 100n).toString();
console.log(`Selling ${SELL_PERCENTAGE}% of your BONK holdings...`);
console.log(`Amount to sell: ${AMOUNT} lamports`);
await sellToken(BONK_MINT, AMOUNT);
process.exit();
