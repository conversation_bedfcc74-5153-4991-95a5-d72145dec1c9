TITLE: Install Zustand package via npm
DESCRIPTION: Instructions to install the Zustand state management library. Use npm or any other preferred package manager to add Zustand to your project dependencies.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/getting-started/introduction.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install zustand
# Or, use any package manager of your choice.
```

----------------------------------------

TITLE: Zustand: Correct Immutable State Update for Form Inputs (TypeScript)
DESCRIPTION: This TypeScript example demonstrates the recommended way to update state in Zustand. Instead of mutating the existing state, it creates a new `person` object by spreading the current `personStore.getState().person` and then overriding the specific field (`firstName`, `lastName`, or `email`). This immutable update ensures that <PERSON><PERSON><PERSON> detects the change and triggers UI re-renders correctly.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/apis/create-store.md#_snippet_14

LANGUAGE: ts
CODE:
```
import { createStore } from 'zustand/vanilla'

type PersonStoreState = {
  person: { firstName: string; lastName: string; email: string }
}

type PersonStoreActions = {
  setPerson: (nextPerson: PersonStoreState['person']) => void
}

type PersonStore = PersonStoreState & PersonStoreActions

const personStore = createStore<PersonStore>()((set) => ({
  person: {
    firstName: 'Barbara',
    lastName: 'Hepworth',
    email: '<EMAIL>',
  },
  setPerson: (person) => set({ person }),
}))

const $firstNameInput = document.getElementById(
  'first-name',
) as HTMLInputElement
const $lastNameInput = document.getElementById('last-name') as HTMLInputElement
const $emailInput = document.getElementById('email') as HTMLInputElement
const $result = document.getElementById('result') as HTMLDivElement

function handleFirstNameChange(event: Event) {
  personStore.getState().setPerson({
    ...personStore.getState().person,
    firstName: (event.target as any).value,
  })
}

function handleLastNameChange(event: Event) {
  personStore.getState().setPerson({
    ...personStore.getState().person,
    lastName: (event.target as any).value,
  })
}

function handleEmailChange(event: Event) {
  personStore.getState().setPerson({
    ...personStore.getState().person,
    email: (event.target as any).value,
  })
}

$firstNameInput.addEventListener('input', handleFirstNameChange)
$lastNameInput.addEventListener('input', handleLastNameChange)
$emailInput.addEventListener('input', handleEmailChange)

const render: Parameters<typeof personStore.subscribe>[0] = (state) => {
  $firstNameInput.value = state.person.firstName
```

----------------------------------------

TITLE: Zustand v5: Fixing unstable selector outputs with `useShallow`
DESCRIPTION: Provides the recommended solution for selectors that return new references in Zustand v5. By wrapping the selector with `useShallow`, a stable reference is returned, preventing infinite re-renders and ensuring correct behavior.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/migrations/migrating-to-v5.md#_snippet_4

LANGUAGE: javascript
CODE:
```
// v5
import { useShallow } from 'zustand/shallow'

const [searchValue, setSearchValue] = useStore(
  useShallow((state) => [state.searchValue, state.setSearchValue]),
)
```

----------------------------------------

TITLE: Complete Zustand State Persistence with URL Search Parameters Example
DESCRIPTION: This comprehensive TypeScript code block combines all previous components into a single, runnable example. It includes the URL search parameter utilities, the custom storage object, and the Zustand store initialization with persistence, demonstrating a full implementation of a mouse position tracker that persists its state in the browser's URL.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/middlewares/persist.md#_snippet_19

LANGUAGE: TypeScript
CODE:
```
import { createStore } from 'zustand/vanilla'
import { persist, createJSONStorage } from 'zustand/middleware'

type PositionStoreState = { position: { x: number; y: number } }

type PositionStoreActions = {
  setPosition: (nextPosition: PositionStoreState['position']) => void
}

type PositionStore = PositionStoreState & PositionStoreActions

const getSearchParams = () => {
  return new URL(location.href).searchParams
}

const updateSearchParams = (searchParams: URLSearchParams) => {
  window.history.replaceState(
    {},
    '',
    `${location.pathname}?${searchParams.toString()}`,
  )
}

const getSearchParam = (key: string) => {
  const searchParams = getSearchParams()
  return searchParams.get(key)
}

const updateSearchParam = (key: string, value: string) => {
  const searchParams = getSearchParams()
  searchParams.set(key, value)

  updateSearchParams(searchParams)
}

const removeSearchParam = (key: string) => {
  const searchParams = getSearchParams()
  searchParams.delete(key)

  updateSearchParams(searchParams)
}

const searchParamsStorage = {
  getItem: (key: string) => getSearchParam(key),
  setItem: (key: string, value: string) => updateSearchParam(key, value),
  removeItem: (key: string) => removeSearchParam(key),
}

const positionStore = createStore<PositionStore>()(
  persist(
    (set) => ({
      position: { x: 0, y: 0 },
      setPosition: (position) => set({ position }),
    },
    {
      name: 'position-storage',

```

----------------------------------------

TITLE: Mocking Zustand Stores for Vitest Testing
DESCRIPTION: This TypeScript file provides a mock implementation for Zustand's `create` and `createStore` functions. It captures the initial state of each store and registers a reset function, allowing all stores to be reset to their initial state after each test run using `afterEach`. This ensures test isolation and prevents state leakage between tests.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/guides/testing.md#_snippet_4

LANGUAGE: TypeScript
CODE:
```
// __mocks__/zustand.ts
import { act } from '@testing-library/react'
import type * as ZustandExportedTypes from 'zustand'
export * from 'zustand'

const { create: actualCreate, createStore: actualCreateStore } =
  await vi.importActual<typeof ZustandExportedTypes>('zustand')

// a variable to hold reset functions for all stores declared in the app
export const storeResetFns = new Set<() => void>()

const createUncurried = <T>(
  stateCreator: ZustandExportedTypes.StateCreator<T>,
) => {
  const store = actualCreate(stateCreator)
  const initialState = store.getInitialState()
  storeResetFns.add(() => {
    store.setState(initialState, true)
  })
  return store
}

// when creating a store, we get its initial state, create a reset function and add it in the set
export const create = (<T>(
  stateCreator: ZustandExportedTypes.StateCreator<T>,
) => {
  console.log('zustand create mock')

  // to support curried version of create
  return typeof stateCreator === 'function'
    ? createUncurried(stateCreator)
    : createUncurried
}) as typeof ZustandExportedTypes.create

const createStoreUncurried = <T>(
  stateCreator: ZustandExportedTypes.StateCreator<T>,
) => {
  const store = actualCreateStore(stateCreator)
  const initialState = store.getInitialState()
  storeResetFns.add(() => {
    store.setState(initialState, true)
  })
  return store
}

// when creating a store, we get its initial state, create a reset function and add it in the set
export const createStore = (<T>(
  stateCreator: ZustandExportedTypes.StateCreator<T>,
) => {
  console.log('zustand createStore mock')

  // to support curried version of createStore
  return typeof stateCreator === 'function'
    ? createStoreUncurried(stateCreator)
    : createStoreUncurried
}) as typeof ZustandExportedTypes.createStore

// reset all stores after each test run
afterEach(() => {
  act(() => {
    storeResetFns.forEach((resetFn) => {
      resetFn()
    })
  })
})
```

----------------------------------------

TITLE: Install Zustand Package
DESCRIPTION: This snippet provides the command to install Zustand using npm, making it available for use in your project.
SOURCE: https://github.com/pmndrs/zustand/blob/main/README.md#_snippet_0

LANGUAGE: bash
CODE:
```
npm install zustand
```

----------------------------------------

TITLE: Create a Basic Zustand Store
DESCRIPTION: Demonstrates how to define a Zustand store using the `create` function. The store acts as a hook and can hold various types of state, including primitives and functions. State updates are immutable, and the `set` function assists with merging state.
SOURCE: https://github.com/pmndrs/zustand/blob/main/README.md#_snippet_1

LANGUAGE: jsx
CODE:
```
import { create } from 'zustand'

const useBearStore = create((set) => ({
  bears: 0,
  increasePopulation: () => set((state) => ({ bears: state.bears + 1 })),
  removeAllBears: () => set({ bears: 0 })
}))
```

----------------------------------------

TITLE: Create Factory to Manage Multiple Counter Stores in TypeScript
DESCRIPTION: Implements a factory function `createCounterStoreFactory` that manages the creation and retrieval of `CounterStore` instances. It takes a `Map` of existing stores and returns a function to get or create a store based on a unique key, ensuring each instance has its own independent state.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/hooks/use-store-with-equality-fn.md#_snippet_21

LANGUAGE: ts
CODE:
```
const createCounterStoreFactory = (
  counterStores: Map<string, ReturnType<typeof createCounterStore>>,
) => {
  return (counterStoreKey: string) => {
    if (!counterStores.has(counterStoreKey)) {
      counterStores.set(counterStoreKey, createCounterStore())
    }
    return counterStores.get(counterStoreKey)!
  }
}
```

----------------------------------------

TITLE: Zustand: Updating Arrays in State with TypeScript
DESCRIPTION: This TypeScript snippet illustrates how to update an array within a Zustand store, treating it as immutable. It uses `setState` with the `replace` parameter set to `true` to completely replace the array. The example also shows how to subscribe to state changes and update a DOM element based on the array values.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/apis/create-store.md#_snippet_6

LANGUAGE: ts
CODE:
```
import { createStore } from 'zustand/vanilla'

type PositionStore = [number, number]

const positionStore = createStore<PositionStore>()(() => [0, 0])

const $dotContainer = document.getElementById('dot-container') as HTMLDivElement
const $dot = document.getElementById('dot') as HTMLDivElement

$dotContainer.addEventListener('pointermove', (event) => {
  positionStore.setState([event.clientX, event.clientY], true)
})

const render: Parameters<typeof positionStore.subscribe>[0] = ([x, y]) => {
  $dot.style.transform = `translate(${x}px, ${y}px)`
}

render(positionStore.getInitialState(), positionStore.getInitialState())

positionStore.subscribe(render)
```

----------------------------------------

TITLE: Corrected Zustand Form Component with Immutable Updates
DESCRIPTION: This complete `tsx` component shows the corrected implementation of a form using Zustand. Instead of mutating the state directly, each `onChange` handler now creates a new `person` object using the spread syntax and passes it to `setPerson`. This immutable update pattern ensures that the UI re-renders correctly when any field is changed.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/apis/create.md#_snippet_12

LANGUAGE: tsx
CODE:
```
import { create } from 'zustand'

type PersonStoreState = {
  person: { firstName: string; lastName: string; email: string }
}

type PersonStoreActions = {
  setPerson: (nextPerson: PersonStoreState['person']) => void
}

type PersonStore = PersonStoreState & PersonStoreActions

const usePersonStore = create<PersonStore>()((set) => ({
  person: {
    firstName: 'Barbara',
    lastName: 'Hepworth',
    email: '<EMAIL>',
  },
  setPerson: (nextPerson) => set(nextPerson),
}))

export default function Form() {
  const person = usePersonStore((state) => state.person)
  const setPerson = usePersonStore((state) => state.setPerson)

  function handleFirstNameChange(e: ChangeEvent<HTMLInputElement>) {
    setPerson({ ...person, firstName: e.target.value })
  }

  function handleLastNameChange(e: ChangeEvent<HTMLInputElement>) {
    setPerson({ ...person, lastName: e.target.value })
  }

  function handleEmailChange(e: ChangeEvent<HTMLInputElement>) {
    setPerson({ ...person, email: e.target.value })
  }

  return (
    <>
      <label style={{ display: 'block' }}>
        First name:
        <input value={person.firstName} onChange={handleFirstNameChange} />
      </label>
      <label style={{ display: 'block' }}>
        Last name:
        <input value={person.lastName} onChange={handleLastNameChange} />
      </label>
      <label style={{ display: 'block' }}>
        Email:
        <input value={person.email} onChange={handleEmailChange} />
      </label>
      <p>
        {person.firstName} {person.lastName} ({person.email})
      </p>
    </>
  )
}
```

----------------------------------------

TITLE: Define Colocated Actions in Zustand Store
DESCRIPTION: This snippet demonstrates the recommended usage of Zustand, where actions and state are colocated within the store definition. This approach creates a self-contained store where data and its associated actions are encapsulated together.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/guides/practice-with-no-store-actions.md#_snippet_0

LANGUAGE: js
CODE:
```
export const useBoundStore = create((set) => ({
  count: 0,
  text: 'hello',
  inc: () => set((state) => ({ count: state.count + 1 })),
  setText: (text) => set({ text }),
}))
```

----------------------------------------

TITLE: useStore Hook API Reference
DESCRIPTION: Detailed API documentation for the `useStore` hook, including its parameters (`storeApi`, `selectorFn`) and return value, explaining how it integrates with Zustand stores in React.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/hooks/use-store.md#_snippet_2

LANGUAGE: APIDOC
CODE:
```
useStore(store, selectorFn)
  Parameters:
    storeApi: The instance that lets you access to store API utilities.
    selectorFn: A function that lets you return data that is based on current state.
  Returns:
    Any data based on current state depending on the selector function. It should take a store, and a selector function as arguments.
```

----------------------------------------

TITLE: Bind Zustand Store to React Components
DESCRIPTION: Illustrates how to consume state from a Zustand store within React components. The store's hook can be used directly in any component without the need for providers. Components will automatically re-render only when the selected state changes.
SOURCE: https://github.com/pmndrs/zustand/blob/main/README.md#_snippet_2

LANGUAGE: jsx
CODE:
```
function BearCounter() {
  const bears = useBearStore((state) => state.bears)
  return <h1>{bears} around here ...</h1>
}

function Controls() {
  const increasePopulation = useBearStore((state) => state.increasePopulation)
  return <button onClick={increasePopulation}>one up</button>
}
```

----------------------------------------

TITLE: Basic Zustand Store Creation
DESCRIPTION: Demonstrates the fundamental way to create a Zustand store using the `create` function, which returns a React Hook.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/apis/create.md#_snippet_0

LANGUAGE: js
CODE:
```
const useSomeStore = create(stateCreatorFn)
```

----------------------------------------

TITLE: Create Zustand Store Factory for Next.js Per-Request State
DESCRIPTION: This TypeScript code defines a factory function, `createCounterStore`, which generates a new Zustand store instance. This pattern is crucial for Next.js applications to ensure that each server request receives its own isolated store, preventing state leakage between concurrent requests and supporting proper server-side rendering hydration.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/guides/nextjs.md#_snippet_1

LANGUAGE: typescript
CODE:
```
import { createStore } from 'zustand/vanilla'

export type CounterState = {
  count: number
}

export type CounterActions = {
  decrementCount: () => void
  incrementCount: () => void
}

export type CounterStore = CounterState & CounterActions

export const defaultInitState: CounterState = {
  count: 0,
}

export const createCounterStore = (
  initState: CounterState = defaultInitState,
) => {
  return createStore<CounterStore>()((set) => ({
    ...initState,
    decrementCount: () => set((state) => ({ count: state.count - 1 })),
    incrementCount: () => set((state) => ({ count: state.count + 1 })),
  }))
}
```

----------------------------------------

TITLE: Wrap Next.js App Router Root Layout with Zustand Provider
DESCRIPTION: This snippet shows how to integrate the `CounterStoreProvider` at the root layout level in a Next.js App Router application. Wrapping `children` with the provider makes the Zustand store accessible to all nested routes and components.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/guides/nextjs.md#_snippet_10

LANGUAGE: tsx
CODE:
```
// src/app/layout.tsx
import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

import { CounterStoreProvider } from '@/providers/counter-store-provider'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <CounterStoreProvider>{children}</CounterStoreProvider>
      </body>
    </html>
  )
}
```

----------------------------------------

TITLE: Testing Counter Component with Zustand (TSX)
DESCRIPTION: This test file uses `@testing-library/react` and `user-event` to test the `Counter` component. It verifies the initial state and confirms that clicking the 'One Up' button correctly increments the count displayed, ensuring the Zustand integration works as expected.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/guides/testing.md#_snippet_13

LANGUAGE: tsx
CODE:
```
// components/counter/counter.test.tsx
import { act, render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

import { Counter } from './counter'

describe('Counter', () => {
  test('should render with initial state of 1', async () => {
    renderCounter()

    expect(await screen.findByText(/^1$/)).toBeInTheDocument()
    expect(
      await screen.findByRole('button', { name: /one up/i }),
    ).toBeInTheDocument()
  })

  test('should increase count by clicking a button', async () => {
    const user = userEvent.setup()

    renderCounter()

    expect(await screen.findByText(/^1$/)).toBeInTheDocument()

    await user.click(await screen.findByRole('button', { name: /one up/i }))

    expect(await screen.findByText(/^2$/)).toBeInTheDocument()
  })
})

const renderCounter = () => {
  return render(<Counter />)
}
```

----------------------------------------

TITLE: Basic createStore Initialization in JavaScript
DESCRIPTION: Shows the simplest way to create a vanilla Zustand store, taking a state creator function as an argument.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/apis/create-store.md#_snippet_0

LANGUAGE: js
CODE:
```
const someStore = createStore(stateCreatorFn)
```

----------------------------------------

TITLE: Zustand vs. Recoil: Render Optimization with Selectors
DESCRIPTION: Similar to Jotai, Recoil performs render optimizations via atom dependency. This snippet shows how Zustand, in contrast, recommends manual render optimizations using selectors to control component re-renders based on state changes.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/getting-started/comparison.md#_snippet_10

LANGUAGE: ts
CODE:
```
import { create } from 'zustand'

type State = {
  count: number
}

type Actions = {
  setCount: (countCallback: (count: State['count']) => State['count']) => void
}

const useCountStore = create<State & Actions>((set) => ({
  count: 0,
  setCount: (countCallback) =>
    set((state) => ({ count: countCallback(state.count) })),
}))

const Component = () => {
  const count = useCountStore((state) => state.count)
  const setCount = useCountStore((state) => state.setCount)
  // ...
}
```

LANGUAGE: ts
CODE:
```
import { atom, useRecoilState } from 'recoil'

const countAtom = atom({
  key: 'count',
  default: 0,
})

const Component = () => {
  const [count, setCount] = useRecoilState(countAtom)
  // ...
}
```

----------------------------------------

TITLE: Create Bounded useStore Hook for Vanilla Zustand Store
DESCRIPTION: This snippet demonstrates how to create a custom `useBearStore` hook that is tightly coupled (bounded) to a specific vanilla Zustand store (`bearStore`). This approach ensures type safety and simplifies state access within React components by abstracting the `useStore` call.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/guides/typescript.md#_snippet_17

LANGUAGE: ts
CODE:
```
import { useStore } from 'zustand'
import { createStore } from 'zustand/vanilla'

interface BearState {
  bears: number
  increase: (by: number) => void
}

const bearStore = createStore<BearState>()((set) => ({
  bears: 0,
  increase: (by) => set((state) => ({ bears: state.bears + by })),
}))

function useBearStore(): BearState
function useBearStore<T>(selector: (state: BearState) => T): T
function useBearStore<T>(selector?: (state: BearState) => T) {
  return useStore(bearStore, selector!)
}
```

----------------------------------------

TITLE: Basic useStore Hook Usage in JavaScript
DESCRIPTION: Demonstrates the fundamental syntax for using the `useStore` React Hook with a vanilla Zustand store and an optional selector function.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/hooks/use-store.md#_snippet_0

LANGUAGE: js
CODE:
```
const someState = useStore(store, selectorFn)
```

----------------------------------------

TITLE: Implement Render Optimizations in Zustand vs Valtio
DESCRIPTION: Compares render optimization strategies in Zustand and Valtio. Zustand relies on manual selector-based optimization, whereas Valtio automatically optimizes renders through property access via `useSnapshot`.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/getting-started/comparison.md#_snippet_6

LANGUAGE: ts
CODE:
```
import { create } from 'zustand'

type State = {
  count: number
}

const useCountStore = create<State>(() => ({
  count: 0,
}))

const Component = () => {
  const count = useCountStore((state) => state.count)
  // ...
}
```

LANGUAGE: ts
CODE:
```
import { proxy, useSnapshot } from 'valtio'

const state = proxy({
  count: 0,
})

const Component = () => {
  const { count } = useSnapshot(state)
  // ...
}
```

----------------------------------------

TITLE: Persisting Zustand State with Persist Middleware (Simple)
DESCRIPTION: Demonstrates a basic setup for persisting Zustand store data using `persist` and `createJSONStorage` with `sessionStorage`.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/integrations/persisting-store-data.md#_snippet_0

LANGUAGE: ts
CODE:
```
import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'

export const useBearStore = create()(
  persist(
    (set, get) => ({
      bears: 0,
      addABear: () => set({ bears: get().bears + 1 }),
    }),
    {
      name: 'food-storage', // name of the item in the storage (must be unique)
      storage: createJSONStorage(() => sessionStorage), // (optional) by default, 'localStorage' is used
    },
  ),
)
```

----------------------------------------

TITLE: Update Object State in Zustand with Immutable Approach
DESCRIPTION: This snippet illustrates the recommended way to update objects in Zustand state by treating them as immutable. Instead of directly modifying the existing object, a new object is created (or a copy is made) with the desired changes, and then the state is updated using `set({ position })`. This ensures that state updates are properly detected and reactive, leveraging the default shallow merge behavior of the `set` function for efficiency.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/apis/create-with-equality-fn.md#_snippet_5

LANGUAGE: tsx
CODE:
```
import { createWithEqualityFn } from 'zustand/traditional'
import { shallow } from 'zustand/vanilla/shallow'

type PositionStoreState = { position: { x: number; y: number } }

type PositionStoreActions = {
  setPosition: (nextPosition: PositionStoreState['position']) => void
}

type PositionStore = PositionStoreState & PositionStoreActions

const usePositionStore = createWithEqualityFn<PositionStore>()(
  (set) => ({
    position: { x: 0, y: 0 },
    setPosition: (position) => set({ position }),
  }),
  shallow,
)

export default function MovingDot() {
  const position = usePositionStore((state) => state.position)
  const setPosition = usePositionStore((state) => state.setPosition)

  return (
    <div
      onPointerMove={(e) => {
        setPosition({
          x: e.clientX,
          y: e.clientY,
        })
      }}
      style={{
        position: 'relative',
        width: '100vw',
        height: '100vh',
      }}
    >
      <div
        style={{
          position: 'absolute',
          backgroundColor: 'red',
          borderRadius: '50%',
          transform: `translate(${position.x}px, ${position.y}px)`,
          left: -10,
          top: -10,
          width: 20,
          height: 20,
        }}
      />
    </div>
  )
}
```

----------------------------------------

TITLE: Define Zustand Store with TypeScript and Middleware
DESCRIPTION: This snippet illustrates basic TypeScript usage for defining a Zustand store, including the integration of `devtools` and `persist` middleware. It also shows the necessary type import for Redux Devtools extension compatibility, ensuring type safety for state and actions.
SOURCE: https://github.com/pmndrs/zustand/blob/main/README.md#_snippet_26

LANGUAGE: ts
CODE:
```
import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import type {} from '@redux-devtools/extension' // required for devtools typing

interface BearState {
  bears: number
  increase: (by: number) => void
}

const useBearStore = create<BearState>()(
  devtools(
    persist(
      (set) => ({
        bears: 0,
        increase: (by) => set((state) => ({ bears: state.bears + by })),
      }),
      {
        name: 'bear-storage',
      },
    ),
  ),
)
```

----------------------------------------

TITLE: Correct Immutable State Update for a Single Field in Zustand
DESCRIPTION: This snippet demonstrates the correct, immutable way to update a single field within an object in a Zustand store. By using the spread operator (`...person`), a new object is created with existing properties, and only the `firstName` property is updated. This ensures that Zustand and React detect the change and trigger a re-render.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/apis/create.md#_snippet_11

LANGUAGE: ts
CODE:
```
setPerson({ ...person, firstName: e.target.value })
```

----------------------------------------

TITLE: Providing Zustand Store via React Context Provider
DESCRIPTION: This snippet demonstrates how to create a `CounterStoreProvider` component using React's `createContext` and `useRef` to provide a Zustand store to its children. It ensures the store is instantiated only once, making it re-render-safe. The `useCounterStore` hook is also defined for consuming the store within child components, throwing an error if used outside the provider. This setup ensures the store is created only once per request on the server and handles potential client-side re-renders.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/guides/nextjs.md#_snippet_2

LANGUAGE: tsx
CODE:
```
// src/providers/counter-store-provider.tsx
'use client'

import { type ReactNode, createContext, useRef, useContext } from 'react'
import { useStore } from 'zustand'

import { type CounterStore, createCounterStore } from '@/stores/counter-store'

export type CounterStoreApi = ReturnType<typeof createCounterStore>

export const CounterStoreContext = createContext<CounterStoreApi | undefined>(
  undefined,
)

export interface CounterStoreProviderProps {
  children: ReactNode
}

export const CounterStoreProvider = ({
  children,
}: CounterStoreProviderProps) => {
  const storeRef = useRef<CounterStoreApi | null>(null)
  if (storeRef.current === null) {
    storeRef.current = createCounterStore()
  }

  return (
    <CounterStoreContext.Provider value={storeRef.current}>
      {children}
    </CounterStoreContext.Provider>
  )
}

export const useCounterStore = <T,>(
  selector: (store: CounterStore) => T,
): T => {
  const counterStoreContext = useContext(CounterStoreContext)

  if (!counterStoreContext) {
    throw new Error(`useCounterStore must be used within CounterStoreProvider`)
  }

  return useStore(counterStoreContext, selector)
}
```

----------------------------------------

TITLE: React Form with Correct Zustand State Updates
DESCRIPTION: This complete React component demonstrates the proper method for handling form input and updating nested state in a Zustand store. Each `onChange` handler now correctly creates a new `person` object using the spread operator and passes it to `setPerson`. This ensures that state changes are immutably applied, triggering necessary re-renders and making the form fully functional and responsive to user input.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/apis/create-with-equality-fn.md#_snippet_12

LANGUAGE: tsx
CODE:
```
import { type ChangeEvent } from 'react'
import { createWithEqualityFn } from 'zustand/traditional'
import { shallow } from 'zustand/vanilla/shallow'

type PersonStoreState = {
  person: { firstName: string; lastName: string; email: string }
}

type PersonStoreActions = {
  setPerson: (nextPerson: PersonStoreState['person']) => void
}

type PersonStore = PersonStoreState & PersonStoreActions

const usePersonStore = createWithEqualityFn<PersonStore>()(
  (set) => ({
    person: {
      firstName: 'Barbara',
      lastName: 'Hepworth',
      email: '<EMAIL>',
    },
    setPerson: (nextPerson) => set({ person: nextPerson }),
  }),
  shallow,
)

export default function Form() {
  const person = usePersonStore((state) => state.person)
  const setPerson = usePersonStore((state) => state.setPerson)

  function handleFirstNameChange(e: ChangeEvent<HTMLInputElement>) {
    setPerson({ ...person, firstName: e.target.value })
  }

  function handleLastNameChange(e: ChangeEvent<HTMLInputElement>) {
    setPerson({ ...person, lastName: e.target.value })
  }

  function handleEmailChange(e: ChangeEvent<HTMLInputElement>) {
    setPerson({ ...person, email: e.target.value })
  }

  return (
    <>
      <label style={{ display: 'block' }}>
        First name:
        <input value={person.firstName} onChange={handleFirstNameChange} />
      </label>
      <label style={{ display: 'block' }}>
        Last name:
        <input value={person.lastName} onChange={handleLastNameChange} />
      </label>
      <label style={{ display: 'block' }}>
        Email:
        <input value={person.email} onChange={handleEmailChange} />
      </label>
      <p>
        {person.firstName} {person.lastName} ({person.email})
      </p>
    </>
  )
}
```

----------------------------------------

TITLE: Complete Example: Global Vanilla Zustand Store with React Component
DESCRIPTION: Provides a comprehensive example demonstrating the full integration of a global vanilla Zustand store with a React component. It includes store definition, type declarations, and a `MovingDot` component that updates its position based on user interaction, rendered within an `App` component.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/hooks/use-store.md#_snippet_6

LANGUAGE: tsx
CODE:
```
import { createStore, useStore } from 'zustand'

type PositionStoreState = { position: { x: number; y: number } }

type PositionStoreActions = {
  setPosition: (nextPosition: PositionStoreState['position']) => void
}

type PositionStore = PositionStoreState & PositionStoreActions

const positionStore = createStore<PositionStore>()((set) => ({
  position: { x: 0, y: 0 },
  setPosition: (position) => set({ position }),
}))

function MovingDot() {
  const position = useStore(positionStore, (state) => state.position)
  const setPosition = useStore(positionStore, (state) => state.setPosition)

  return (
    <div
      onPointerMove={(e) => {
        setPosition({
          x: e.clientX,
          y: e.clientY,
        })
      }}
      style={{
        position: 'relative',
        width: '100vw',
        height: '100vh',
      }}
    >
      <div
        style={{
          position: 'absolute',
          backgroundColor: 'red',
          borderRadius: '50%',
          transform: `translate(${position.x}px, ${position.y}px)`,
          left: -10,
          top: -10,
          width: 20,
          height: 20,
        }}
      />
    </div>
  )
}

export default function App() {
  return <MovingDot />
}
```

----------------------------------------

TITLE: Basic Zustand Store Definition with TypeScript
DESCRIPTION: Demonstrates how to define a basic Zustand store using TypeScript. It shows the `create<T>()(...)` syntax to explicitly type the store's state interface, including state properties and actions.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/guides/typescript.md#_snippet_0

LANGUAGE: ts
CODE:
```
import { create } from 'zustand'

interface BearState {
  bears: number
  increase: (by: number) => void
}

const useBearStore = create<BearState>()((set) => ({
  bears: 0,
  increase: (by) => set((state) => ({ bears: state.bears + by })),
}))
```

----------------------------------------

TITLE: Complete Zustand State Persistence with Versioning and Mouse Tracking
DESCRIPTION: This comprehensive TypeScript example demonstrates a full implementation of Zustand's `persist` middleware with state versioning and migration. It includes defining the store, types, simulating an initial version 0 state, implementing a migration function from version 0 to 1, and integrating with a mouse tracking application to update and render a dot's position on the screen.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/middlewares/persist.md#_snippet_25

LANGUAGE: typescript
CODE:
```
import { createStore } from 'zustand/vanilla'
import { persist } from 'zustand/middleware'

// For tutorial purposes only
if (!localStorage.getItem('position-storage')) {
  localStorage.setItem(
    'position-storage',
    JSON.stringify({
      state: { x: 100, y: 100 },
      version: 0,
    }),
  )
}

type PositionStoreState = { position: { x: number; y: number } }

type PositionStoreActions = {
  setPosition: (nextPosition: PositionStoreState['position']) => void
}

type PositionStore = PositionStoreState & PositionStoreActions

const positionStore = createStore<PositionStore>()(
  persist(
    (set) => ({
      position: { x: 0, y: 0 }, // version 0: just x: 0, y: 0
      setPosition: (position) => set({ position }),
    }),
    {
      name: 'position-storage',
      version: 1,
      migrate: (persisted: any, version) => {
        if (version === 0) {
          persisted.position = { x: persisted.x, y: persisted.y }
          delete persisted.x
          delete persisted.y
        }

        return persisted
      }
    }
  )
)

const $dotContainer = document.getElementById('dot-container') as HTMLDivElement
const $dot = document.getElementById('dot') as HTMLDivElement

$dotContainer.addEventListener('pointermove', (event) => {
  positionStore.getState().setPosition({
    x: event.clientX,
    y: event.clientY,
  })
})

const render: Parameters<typeof positionStore.subscribe>[0] = (state) => {
  $dot.style.transform = `translate(${state.position.x}px, ${state.position.y}px)`
}

render(positionStore.getState(), positionStore.getState())

positionStore.subscribe(render)
```

----------------------------------------

TITLE: Example of Direct State Mutation in Zustand
DESCRIPTION: This line of code exemplifies the direct mutation of a state property (`firstName`) within an object obtained from a Zustand store. Such direct assignments do not create a new object reference, which is necessary for Zustand (and React) to detect a state change and trigger a re-render.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/apis/create-with-equality-fn.md#_snippet_10

LANGUAGE: tsx
CODE:
```
person.firstName = e.target.value
```

----------------------------------------

TITLE: Example of Direct State Mutation in Zustand
DESCRIPTION: This line of code specifically illustrates the direct mutation of a state property (`firstName`) within an object retrieved from a Zustand store. This direct assignment bypasses React's reconciliation process, leading to a lack of UI updates.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/apis/create.md#_snippet_10

LANGUAGE: tsx
CODE:
```
person.firstName = e.target.value
```

----------------------------------------

TITLE: Create a basic Zustand store
DESCRIPTION: Demonstrates how to initialize a Zustand store using `create`. The store can hold various data types and functions. The `set` function is used to update and merge state.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/getting-started/introduction.md#_snippet_1

LANGUAGE: javascript
CODE:
```
import { create } from 'zustand'

const useStore = create((set) => ({
  bears: 0,
  increasePopulation: () => set((state) => ({ bears: state.bears + 1 })),
  removeAllBears: () => set({ bears: 0 }),
  updateBears: (newBears) => set({ bears: newBears })
}))
```

----------------------------------------

TITLE: Updating Zustand State with Updater Functions
DESCRIPTION: Illustrates how to implement updater functions within Zustand actions to update state based on its previous value, similar to React's `setState` updater pattern. This ensures correct state updates, especially when multiple updates are queued.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/apis/create.md#_snippet_3

LANGUAGE: tsx
CODE:
```
import { create } from 'zustand'

type AgeStoreState = { age: number }

type AgeStoreActions = {
  setAge: (
    nextAge:
      | AgeStoreState['age']
      | ((currentAge: AgeStoreState['age']) => AgeStoreState['age']),
  ) => void
}

type AgeStore = AgeStoreState & AgeStoreActions

const useAgeStore = create<AgeStore>()((set) => ({
  age: 42,
  setAge: (nextAge) => {
    set((state) => ({
      age: typeof nextAge === 'function' ? nextAge(state.age) : nextAge,
    }))
  },
}))

export default function App() {
  const age = useAgeStore((state) => state.age)
  const setAge = useAgeStore((state) => state.setAge)

  function increment() {
    setAge((currentAge) => currentAge + 1)
  }

  return (
    <>
      <h1>Your age: {age}</h1>
      <button
        onClick={() => {
          increment()
          increment()
          increment()
        }}
      >
        +3
      </button>
      <button
        onClick={() => {
          increment()
        }}
      >
        +1
      </button>
    </>
  )
}
```

----------------------------------------

TITLE: Vitest Configuration for Zustand Testing
DESCRIPTION: This configuration file defines how Vitest runs tests. It merges with the existing `vite.config` and sets up `globals: true` for global API access, `environment: 'jsdom'` for browser-like DOM environment, and `setupFiles: ['./setup-vitest.ts']` to include the test setup script. This comprehensive setup is essential for testing Zustand applications effectively.
SOURCE: https://github.com/pmndrs/zustand/blob/main/docs/guides/testing.md#_snippet_7

LANGUAGE: TypeScript
CODE:
```
// vitest.config.ts
import { defineConfig, mergeConfig } from 'vitest/config'
import viteConfig from './vite.config'

export default defineConfig((configEnv) =>
  mergeConfig(
    viteConfig(configEnv),
    defineConfig({
      test: {
        globals: true,
        environment: 'jsdom',
        setupFiles: ['./setup-vitest.ts'],
      },
    }),
  ),
)
```
 