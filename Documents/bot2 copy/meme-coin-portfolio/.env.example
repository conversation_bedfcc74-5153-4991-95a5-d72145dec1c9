# Wallet Configuration
WALLET_ADDRESS=968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT
WALLET_PRIVATE_KEY=TODO_REPLACE_WITH_YOUR_PRIVATE_KEY

# Solana RPC Configuration
RPC_URL=TODO_REPLACE_WITH_YOUR_HELIUS_RPC_URL
HELIUS_KEY=TODO_REPLACE_WITH_YOUR_HELIUS_API_KEY

# Jupiter DEX Configuration
JUPITER_BASE_URL=https://quote-api.jup.ag/v6
JUP_API_KEY=TODO_OPTIONAL_FOR_PAID_PLAN

# Redis Configuration (for Docker Compose)
REDIS_URL=redis://redis:6379

# Server Configuration
BACKEND_PORT=4000
FRONTEND_PORT=3000

# Logging & Monitoring
LOG_LEVEL=info
SENTRY_DSN=TODO_OPTIONAL_FOR_ERROR_TRACKING

# Frontend Environment Variables
NEXT_PUBLIC_API_URL=http://localhost:4000
NEXT_PUBLIC_WS_URL=ws://localhost:4000

# DO NOT COMMIT .env WITH REAL PRIVATE KEYS
# Copy this file to .env and fill in your actual values
