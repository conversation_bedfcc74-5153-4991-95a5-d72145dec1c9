# 🚀 Deployment Guide

This guide covers deploying the Meme Coin Portfolio application in various environments.

## 📋 Prerequisites

### Required
- Docker and Docker Compose
- Solana wallet with private key
- Helius RPC endpoint (recommended)
- Minimum 2GB RAM, 1 CPU core
- 10GB available disk space

### Recommended
- 4GB+ RAM for production
- SSD storage for better performance
- Load balancer for high availability
- SSL certificate for HTTPS

## 🏃‍♂️ Quick Start

### One-Command Setup
```bash
# Clone and bootstrap
git clone <repository-url>
cd meme-coin-portfolio
chmod +x scripts/bootstrap.sh
./scripts/bootstrap.sh
```

### Manual Docker Setup
```bash
# Copy environment file
cp .env.example .env

# Edit with your credentials
nano .env

# Start services
docker compose up --build -d

# Check status
docker compose ps
docker compose logs -f
```

## 🔧 Environment Configuration

### Required Environment Variables
```bash
# Wallet Configuration
WALLET_ADDRESS=your_solana_wallet_address
WALLET_PRIVATE_KEY=your_base64_encoded_private_key

# Solana Network
RPC_URL=https://mainnet.helius-rpc.com/?api-key=your_key
HELIUS_KEY=your_helius_api_key

# Optional Services
JUP_API_KEY=your_jupiter_api_key_for_paid_features
SENTRY_DSN=your_sentry_dsn_for_error_tracking
```

### Service Configuration
```bash
# Ports
BACKEND_PORT=4000
FRONTEND_PORT=3000

# Redis
REDIS_URL=redis://redis:6379

# Logging
LOG_LEVEL=info

# Security
NODE_ENV=production
```

## 🐳 Docker Deployment

### Development Environment
```bash
# Start development stack
docker compose up --build

# With file watching
docker compose -f docker-compose.yml -f docker-compose.dev.yml up
```

### Production Environment
```bash
# Production deployment
docker compose -f docker-compose.prod.yml up --build -d

# With custom environment
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d

# Scale services
docker compose -f docker-compose.prod.yml up --scale backend=2 -d
```

### Production with Nginx
```bash
# Start with reverse proxy
docker compose -f docker-compose.prod.yml --profile nginx up -d

# Custom nginx config
cp nginx.conf.example nginx.conf
# Edit nginx.conf for your domain
docker compose restart nginx
```

## ☁️ Cloud Deployment

### AWS ECS Deployment
```bash
# Build and push images
docker build -t your-registry/meme-portfolio-backend:latest ./backend
docker build -t your-registry/meme-portfolio-frontend:latest ./frontend
docker push your-registry/meme-portfolio-backend:latest
docker push your-registry/meme-portfolio-frontend:latest

# Deploy with ECS CLI or CloudFormation
```

### Google Cloud Run
```bash
# Build for Cloud Run
docker build -t gcr.io/your-project/meme-portfolio-backend ./backend
docker build -t gcr.io/your-project/meme-portfolio-frontend ./frontend

# Deploy
gcloud run deploy backend --image gcr.io/your-project/meme-portfolio-backend
gcloud run deploy frontend --image gcr.io/your-project/meme-portfolio-frontend
```

### DigitalOcean App Platform
```yaml
# app.yaml
name: meme-portfolio
services:
- name: backend
  source_dir: /backend
  github:
    repo: your-username/meme-coin-portfolio
    branch: main
  run_command: npm start
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  
- name: frontend
  source_dir: /frontend
  github:
    repo: your-username/meme-coin-portfolio
    branch: main
  run_command: npm start
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
```

## 🔒 Security Configuration

### SSL/TLS Setup
```bash
# Generate self-signed certificate (development)
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout ssl/key.pem -out ssl/cert.pem

# Let's Encrypt (production)
certbot certonly --webroot -w /var/www/html -d your-domain.com
```

### Firewall Configuration
```bash
# UFW (Ubuntu)
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw deny 3000/tcp   # Block direct frontend access
ufw deny 4000/tcp   # Block direct backend access
ufw enable
```

### Environment Security
```bash
# Secure .env file
chmod 600 .env
chown root:root .env

# Use Docker secrets (production)
echo "your_private_key" | docker secret create wallet_private_key -
```

## 📊 Monitoring & Logging

### Health Checks
```bash
# Application health
curl http://localhost:4000/health
curl http://localhost:4000/health/detailed

# Service health
docker compose ps
docker stats
```

### Logging Setup
```bash
# View logs
docker compose logs -f
docker compose logs backend
docker compose logs frontend

# Log rotation
echo '{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}' > /etc/docker/daemon.json
```

### Monitoring with Prometheus
```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      
  grafana:
    image: grafana/grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

## 🔄 Updates & Maintenance

### Application Updates
```bash
# Pull latest changes
git pull origin main

# Rebuild and restart
docker compose down
docker compose up --build -d

# Zero-downtime update (with load balancer)
docker compose up --scale backend=2 -d
docker compose stop backend_1
docker compose up --build backend_1 -d
docker compose stop backend_2
```

### Database Maintenance
```bash
# Redis maintenance
docker compose exec redis redis-cli FLUSHDB
docker compose exec redis redis-cli BGSAVE

# Backup Redis data
docker compose exec redis redis-cli --rdb /data/backup.rdb
docker cp $(docker compose ps -q redis):/data/backup.rdb ./backup/
```

### Log Cleanup
```bash
# Clean Docker logs
docker system prune -f
docker volume prune -f

# Clean application logs
find ./logs -name "*.log" -mtime +7 -delete
```

## 🚨 Troubleshooting

### Common Issues

**Services won't start**
```bash
# Check Docker resources
docker system df
docker system prune

# Check port conflicts
netstat -tulpn | grep :3000
netstat -tulpn | grep :4000

# Check environment variables
docker compose config
```

**Connection issues**
```bash
# Test Redis connection
docker compose exec backend npm run test:redis

# Test Solana RPC
curl -X POST $RPC_URL -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":1,"method":"getHealth"}'

# Check network connectivity
docker compose exec backend ping redis
docker compose exec frontend ping backend
```

**Performance issues**
```bash
# Monitor resource usage
docker stats
htop

# Check application metrics
curl http://localhost:4000/health/metrics

# Analyze logs
docker compose logs backend | grep ERROR
docker compose logs frontend | grep ERROR
```

### Debug Mode
```bash
# Enable debug logging
LOG_LEVEL=debug docker compose up

# Debug specific service
docker compose exec backend sh
docker compose exec frontend sh

# Check service dependencies
docker compose exec backend npm run test:health
```

## 📈 Scaling

### Horizontal Scaling
```bash
# Scale backend services
docker compose up --scale backend=3 -d

# Load balancer configuration
# Update nginx.conf with multiple backend servers
upstream backend {
    server backend_1:4000;
    server backend_2:4000;
    server backend_3:4000;
}
```

### Vertical Scaling
```yaml
# docker-compose.prod.yml
services:
  backend:
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
```

### Database Scaling
```bash
# Redis clustering (for high availability)
# Use Redis Sentinel or Redis Cluster
# Configure multiple Redis instances
```

## 🔐 Backup & Recovery

### Backup Strategy
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)

# Backup Redis data
docker compose exec redis redis-cli BGSAVE
docker cp $(docker compose ps -q redis):/data/dump.rdb ./backups/redis_$DATE.rdb

# Backup configuration
tar -czf ./backups/config_$DATE.tar.gz .env nginx.conf

# Backup logs
tar -czf ./backups/logs_$DATE.tar.gz ./logs/
```

### Recovery Process
```bash
# Restore Redis data
docker compose down
docker cp ./backups/redis_backup.rdb $(docker compose ps -q redis):/data/dump.rdb
docker compose up -d

# Restore configuration
tar -xzf ./backups/config_backup.tar.gz
```

## 📞 Support

### Getting Help
- Check the [README.md](README.md) for basic setup
- Review logs: `docker compose logs -f`
- Check health endpoints: `curl http://localhost:4000/health/detailed`
- Open an issue on GitHub with logs and configuration

### Performance Tuning
- Monitor resource usage with `docker stats`
- Adjust Redis memory limits based on usage
- Scale services based on load
- Use CDN for static assets in production

---

**Need help?** Check our troubleshooting guide or open an issue with your deployment logs.
