# 🚀 Meme Coin Portfolio - Automated Trading Dashboard

An intelligent, real-time meme coin trading assistant designed to automate exit strategies, track portfolios live, and identify high-potential coins before they explode—removing emotion and maximizing gains.

![Dashboard Preview](./docs/images/dashboard-preview.png)

## ✨ Features

### 🎯 Core Features
- **Automated Exit Strategies** - Multi-layer take-profit and stop-loss automation
- **Real-time Portfolio Tracking** - Live P&L, positions, and performance analytics
- **Dead Hunter Signals** - Volume spikes, price movements, and transaction rate detection
- **Smart Trading Panel** - Dynamic slippage management and position validation
- **Capital Exposure Management** - Real-time risk monitoring and limits

### 🛡️ Risk Management
- **Stop Loss Protection** - 15% configurable stop-loss (5-30% range)
- **Trailing Stop Loss** - 15% trailing protection (10-25% range)
- **Progressive Take Profit** - Automatic sells at +50%, +100%, +150%, +200%
- **Moon Bag Strategy** - Hold 25% for +500% targets
- **Position Size Limits** - Configurable capital allocation controls

### 📊 Analytics & Monitoring
- **Performance Metrics** - Sharpe ratio, max drawdown, win rate tracking
- **Asset Allocation Charts** - Real-time portfolio distribution visualization
- **Trade History** - Complete transaction log with performance analysis
- **Signal History** - Dead Hunter signal tracking and performance

## 🏗️ Architecture

### Backend Stack
- **Runtime**: Node.js with TypeScript
- **API Framework**: Express.js with Socket.IO
- **Blockchain**: Solana Web3.js integration
- **DEX Trading**: Jupiter Aggregator API
- **Job Processing**: BullMQ with Redis
- **Logging**: Pino structured logging
- **Validation**: AJV schema validation

### Frontend Stack
- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS with shadcn/ui
- **State Management**: Zustand with persistence
- **Charts**: Recharts for data visualization
- **Real-time**: Socket.IO client
- **Animations**: Framer Motion

### Infrastructure
- **Containerization**: Docker Compose
- **Database**: Redis for caching and job queues
- **Process Management**: PM2 for production
- **Health Monitoring**: Comprehensive health checks

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- Docker and Docker Compose
- Solana wallet with private key
- Helius RPC endpoint (recommended)

### 1. Clone and Setup
```bash
git clone <repository-url>
cd meme-coin-portfolio

# Copy environment configuration
cp .env.example .env
```

### 2. Configure Environment
Edit `.env` with your credentials:
```bash
# Required: Your wallet configuration
WALLET_ADDRESS=your_wallet_address_here
WALLET_PRIVATE_KEY=your_base64_private_key_here

# Required: Solana RPC endpoint
RPC_URL=https://mainnet.helius-rpc.com/?api-key=your_helius_key
HELIUS_KEY=your_helius_api_key

# Optional: Jupiter API key for paid features
JUP_API_KEY=your_jupiter_api_key

# Optional: Error tracking
SENTRY_DSN=your_sentry_dsn
```

### 3. Start with Docker (Recommended)
```bash
# Build and start all services
docker compose up --build

# Or run in background
docker compose up --build -d
```

### 4. Manual Development Setup
```bash
# Install backend dependencies
cd backend
npm install

# Install frontend dependencies  
cd ../frontend
npm install

# Start Redis (required)
docker run -d -p 6379:6379 redis:7-alpine

# Start backend (terminal 1)
cd backend
npm run dev

# Start frontend (terminal 2)
cd frontend
npm run dev
```

### 5. Access the Application
- **Frontend Dashboard**: http://localhost:3000
- **Backend API**: http://localhost:4000
- **Health Check**: http://localhost:4000/health
- **API Documentation**: http://localhost:4000/docs (if enabled)

## 📖 Usage Guide

### Initial Setup
1. **Configure Wallet**: Ensure your wallet has SOL for trading and fees
2. **Set Exit Strategy**: Configure stop-loss, take-profit levels, and moon bag settings
3. **Add Watched Tokens**: Set up Dead Hunter signals for tokens you want to monitor
4. **Set Capital Limits**: Configure maximum exposure and position sizes

### Trading Workflow
1. **Monitor Signals**: Watch the Dead Hunter panel for volume spikes and price movements
2. **Execute Trades**: Use the trading panel to buy tokens with automatic exit strategy application
3. **Track Positions**: Monitor active positions with real-time P&L updates
4. **Automated Exits**: Let the system handle stop-losses and take-profits automatically

### Exit Strategy Configuration
```javascript
{
  "stopLoss": { "enabled": true, "percentage": 15 },
  "trailingStopLoss": { "enabled": true, "percentage": 15 },
  "takeProfitLevels": [
    { "thresholdPercent": 50, "sellPercent": 15 },
    { "thresholdPercent": 100, "sellPercent": 15 },
    { "thresholdPercent": 150, "sellPercent": 15 },
    { "thresholdPercent": 200, "sellPercent": 15 }
  ],
  "moonBag": { "enabled": true, "percentage": 25, "targetPercent": 500 }
}
```

## 🔧 Configuration

### Environment Variables
| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `WALLET_ADDRESS` | Your Solana wallet address | ✅ | - |
| `WALLET_PRIVATE_KEY` | Base64 encoded private key | ✅ | - |
| `RPC_URL` | Solana RPC endpoint | ✅ | - |
| `HELIUS_KEY` | Helius API key | ⚠️ | - |
| `JUP_API_KEY` | Jupiter API key (optional) | ❌ | - |
| `REDIS_URL` | Redis connection string | ❌ | `redis://redis:6379` |
| `BACKEND_PORT` | Backend server port | ❌ | `4000` |
| `FRONTEND_PORT` | Frontend server port | ❌ | `3000` |
| `LOG_LEVEL` | Logging level | ❌ | `info` |
| `SENTRY_DSN` | Error tracking DSN | ❌ | - |

### Trading Configuration
- **Slippage Control**: Dynamic (0.1-5%) or static slippage settings
- **Priority Fees**: Configurable transaction priority for faster execution
- **Retry Logic**: Automatic retry with exponential backoff
- **Safety Checks**: Liquidity, honeypot, and authority validation

## 📊 API Documentation

### Portfolio Endpoints
```bash
GET  /api/portfolio              # Get portfolio overview
GET  /api/portfolio/positions/:mint  # Get specific position
GET  /api/portfolio/analytics    # Get performance analytics
POST /api/portfolio/refresh      # Force refresh portfolio data
```

### Trading Endpoints
```bash
POST /api/trade/quote           # Get price quote
POST /api/trade/execute         # Execute trade
GET  /api/trade/status/:jobId   # Get trade status
GET  /api/trade/history         # Get trade history
GET  /api/trade/tokens          # Get supported tokens
```

### Configuration Endpoints
```bash
GET  /api/config/exit-strategy  # Get exit strategy config
POST /api/config/exit-strategy  # Update exit strategy
GET  /api/config/trading        # Get trading config
POST /api/config/trading        # Update trading config
GET  /api/config/bot-rules      # Get bot rules
POST /api/config/bot-rules      # Update bot rules
```

### Health Endpoints
```bash
GET /health                     # Basic health check
GET /health/detailed           # Detailed system status
GET /health/ready              # Readiness probe
GET /health/live               # Liveness probe
GET /health/metrics            # System metrics
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
npm test                       # Run all tests
npm run test:watch            # Watch mode
npm run test:coverage         # Coverage report
```

### Frontend Tests
```bash
cd frontend
npm test                      # Run component tests
npm run test:e2e             # End-to-end tests
```

### Integration Tests
```bash
# Test full stack integration
docker compose -f docker-compose.test.yml up --build
```

## 🚀 Deployment

### Production Docker Deployment
```bash
# Production build
docker compose -f docker-compose.prod.yml up --build -d

# With custom environment
docker compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

### Manual Production Deployment
```bash
# Backend
cd backend
npm run build
npm start

# Frontend
cd frontend
npm run build
npm start
```

### Environment-Specific Configurations
- **Development**: `.env`
- **Staging**: `.env.staging`
- **Production**: `.env.production`

## 🔒 Security Considerations

### Wallet Security
- **Never commit private keys** to version control
- Use environment variables for sensitive data
- Consider hardware wallet integration for production
- Implement key rotation policies

### API Security
- Rate limiting on all endpoints
- Input validation and sanitization
- CORS configuration for frontend domains
- Request logging and monitoring

### Infrastructure Security
- Use HTTPS in production
- Implement proper firewall rules
- Regular security updates
- Monitor for suspicious activity

## 🐛 Troubleshooting

### Common Issues

**Connection Issues**
```bash
# Check Redis connection
docker logs meme-coin-portfolio-redis-1

# Check backend logs
docker logs meme-coin-portfolio-backend-1

# Check frontend logs
docker logs meme-coin-portfolio-frontend-1
```

**Trading Issues**
- Verify wallet has sufficient SOL balance
- Check RPC endpoint connectivity
- Validate token mint addresses
- Review slippage settings

**Performance Issues**
- Monitor Redis memory usage
- Check job queue processing
- Review API response times
- Optimize database queries

### Debug Mode
```bash
# Enable debug logging
LOG_LEVEL=debug docker compose up

# Backend debug mode
cd backend
DEBUG=* npm run dev
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Write comprehensive tests
- Update documentation
- Use conventional commit messages
- Ensure Docker builds pass

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This software is for educational and research purposes only. Trading cryptocurrencies involves substantial risk of loss. The authors and contributors are not responsible for any financial losses incurred through the use of this software. Always do your own research and never invest more than you can afford to lose.

## 🙏 Acknowledgments

- [Solana Foundation](https://solana.org/) for the blockchain infrastructure
- [Jupiter](https://jup.ag/) for DEX aggregation services
- [Helius](https://helius.xyz/) for RPC and data services
- [Next.js](https://nextjs.org/) and [Tailwind CSS](https://tailwindcss.com/) for the frontend framework
- The open-source community for the amazing tools and libraries

---

**Built with ❤️ for the meme coin community**
