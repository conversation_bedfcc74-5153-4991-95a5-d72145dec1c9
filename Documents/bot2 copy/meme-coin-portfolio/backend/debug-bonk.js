const { Connection, PublicKey } = require('@solana/web3.js');

async function debugBonkBalance() {
  const connection = new Connection(process.env.RPC_URL || 'https://api.mainnet-beta.solana.com', 'confirmed');
  const walletAddress = '968aqNdZ5ZRLByseFo18YiSHywzqXj3S9z56Vwr62CGT';
  const bonkMint = 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263';

  console.log('Wallet:', walletAddress);
  console.log('Bonk Mint:', bonkMint);
  console.log('---');

  try {
    // Get all token accounts for this wallet
    const tokenAccounts = await connection.getParsedTokenAccountsByOwner(
      new PublicKey(walletAddress),
      { programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA') }
    );

    console.log(`Found ${tokenAccounts.value.length} token accounts`);
    console.log('---');

    // Find Bonk specifically
    const bonkAccounts = tokenAccounts.value.filter(account => 
      account.account.data.parsed.info.mint === bonkMint
    );

    if (bonkAccounts.length === 0) {
      console.log('No Bonk token accounts found!');
      return;
    }

    console.log(`Found ${bonkAccounts.length} Bonk account(s):`);
    
    bonkAccounts.forEach((account, index) => {
      const info = account.account.data.parsed.info;
      console.log(`\nBonk Account ${index + 1}:`);
      console.log('  Mint:', info.mint);
      console.log('  Owner:', info.owner);
      console.log('  Raw Amount:', info.tokenAmount.amount);
      console.log('  Decimals:', info.tokenAmount.decimals);
      console.log('  UI Amount:', info.tokenAmount.uiAmount);
      console.log('  UI Amount String:', info.tokenAmount.uiAmountString);
    });

    // Also check the mint account to verify decimals
    console.log('\n--- Checking Bonk Mint Account ---');
    const mintInfo = await connection.getParsedAccountInfo(new PublicKey(bonkMint));
    if (mintInfo.value && mintInfo.value.data && 'parsed' in mintInfo.value.data) {
      console.log('Mint Decimals:', mintInfo.value.data.parsed.info.decimals);
      console.log('Supply:', mintInfo.value.data.parsed.info.supply);
    }

  } catch (error) {
    console.error('Error:', error);
  }
}

debugBonkBalance();
