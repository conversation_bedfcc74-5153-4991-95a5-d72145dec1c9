"use strict";
/**
 * Main entry point for the Meme Coin Portfolio Backend
 *
 * This server provides:
 * - REST API for portfolio management and trading
 * - WebSocket connections for real-time updates
 * - Background job processing for trading automation
 * - Integration with Solana blockchain and Jupiter DEX
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.io = exports.app = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const dotenv_1 = __importDefault(require("dotenv"));
const pino_1 = __importDefault(require("pino"));
// Import route handlers
const portfolio_1 = __importDefault(require("./routes/portfolio"));
const trade_1 = __importDefault(require("./routes/trade"));
const config_1 = __importDefault(require("./routes/config"));
const health_1 = __importDefault(require("./routes/health"));
// Import services
const redis_1 = require("./services/redis");
const queueManager_1 = require("./jobs/queueManager");
const websocket_1 = require("./services/websocket");
const solanaClient_1 = require("./services/solanaClient");
const tokenMetadata_1 = require("./services/tokenMetadata");
const priceUpdater_1 = require("./services/priceUpdater");
const exitStrategy_1 = require("./services/exitStrategy");
// Load environment variables
dotenv_1.default.config();
// Initialize logger
const logger = (0, pino_1.default)({
    level: process.env['LOG_LEVEL'] || 'info',
    transport: {
        target: 'pino-pretty',
        options: {
            colorize: true,
            translateTime: 'SYS:standard',
            ignore: 'pid,hostname'
        }
    }
});
exports.logger = logger;
// Validate required environment variables
const requiredEnvVars = ['WALLET_ADDRESS', 'RPC_URL'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missingEnvVars.length > 0) {
    logger.error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
    logger.error('Please check your .env file and ensure all required variables are set');
    process.exit(1);
}
// Create Express app
const app = (0, express_1.default)();
exports.app = app;
const server = (0, http_1.createServer)(app);
// Initialize Socket.IO with CORS
const io = new socket_io_1.Server(server, {
    cors: {
        origin: process.env['FRONTEND_URL'] || "http://localhost:3000",
        methods: ["GET", "POST"]
    }
});
exports.io = io;
// Middleware
app.use((0, cors_1.default)({
    origin: [
        process.env['FRONTEND_URL'] || "http://localhost:3000",
        "http://localhost:3001" // Allow Next.js dev server on alternate port
    ],
    credentials: true
}));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true }));
// Request logging middleware
app.use((req, _res, next) => {
    logger.info({
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent')
    }, 'Incoming request');
    next();
});
// API Routes
app.use('/api/portfolio', portfolio_1.default);
app.use('/api/trade', trade_1.default);
app.use('/api/config', config_1.default);
app.use('/health', health_1.default);
// WebSocket namespace for real-time updates
const wsNamespace = io.of('/ws');
(0, websocket_1.setupWebSocketHandlers)(wsNamespace, logger);
// Global error handler
app.use((err, req, res, _next) => {
    logger.error({
        error: err.message,
        stack: err.stack,
        url: req.url,
        method: req.method
    }, 'Unhandled error');
    res.status(500).json({
        error: 'Internal server error',
        message: process.env['NODE_ENV'] === 'development' ? err.message : 'Something went wrong'
    });
});
// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not found',
        message: `Route ${req.originalUrl} not found`
    });
});
// Initialize services and start server
async function startServer() {
    try {
        // Initialize Redis connection
        logger.info('Initializing Redis connection...');
        await (0, redis_1.initializeRedis)();
        // Initialize Solana connection
        logger.info('Initializing Solana connection...');
        await (0, solanaClient_1.initializeSolana)();
        // Initialize token metadata service
        logger.info('Initializing token metadata service...');
        await tokenMetadata_1.tokenMetadataService.initialize();
        // Initialize job queues
        logger.info('Initializing job queues...');
        try {
            await (0, queueManager_1.initializeJobQueues)();
            logger.info('Job queues initialized successfully');
        }
        catch (error) {
            logger.warn('Job queues initialization failed, continuing without background jobs:', error);
        }
        // Initialize exit strategy engine
        logger.info('Initializing exit strategy engine...');
        try {
            await exitStrategy_1.exitStrategyEngine.loadPositionsFromCache();
            logger.info('Exit strategy engine initialized successfully');
        }
        catch (error) {
            logger.warn('Exit strategy engine initialization failed:', error);
        }
        // Start server
        const port = process.env['BACKEND_PORT'] || 4000;
        server.listen(port, () => {
            logger.info(`🚀 Meme Coin Portfolio Backend running on port ${port}`);
            logger.info(`📊 Health check available at http://localhost:${port}/health`);
            logger.info(`🔌 WebSocket endpoint: ws://localhost:${port}/ws`);
            // Start real-time price updates
            logger.info('Starting real-time price update service...');
            priceUpdater_1.priceUpdateService.start();
            // Log configuration status
            if (!process.env['WALLET_PRIVATE_KEY'] || process.env['WALLET_PRIVATE_KEY'] === 'TODO_REPLACE_WITH_YOUR_PRIVATE_KEY') {
                logger.warn('⚠️  WALLET_PRIVATE_KEY not configured - trading will be disabled');
            }
            if (!process.env['HELIUS_KEY'] || process.env['HELIUS_KEY'] === 'TODO_REPLACE_WITH_YOUR_HELIUS_API_KEY') {
                logger.warn('⚠️  HELIUS_KEY not configured - some features may be limited');
            }
        });
    }
    catch (error) {
        logger.error('Failed to start server:', error);
        process.exit(1);
    }
}
// Graceful shutdown
process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully');
    server.close(() => {
        logger.info('Server closed');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down gracefully');
    server.close(() => {
        logger.info('Server closed');
        process.exit(0);
    });
});
// Start the server
startServer();
//# sourceMappingURL=index.js.map