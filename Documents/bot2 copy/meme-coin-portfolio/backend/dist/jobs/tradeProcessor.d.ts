/**
 * Trade processing job handlers
 *
 * Handles the execution of trading operations:
 * - Trade validation and execution
 * - Exit strategy application
 * - Position tracking
 * - Error handling and retries
 */
import { Job } from 'bullmq';
export interface TradeJobData {
    inputMint: string;
    outputMint: string;
    amount: number;
    slippageBps: number;
    quote: any;
    walletAddress: string;
    applyExitStrategy: boolean;
    timestamp: string;
}
export interface TradeResult {
    signature: string;
    inputAmount: string;
    outputAmount: string;
    actualSlippage: number;
    fee: number;
    timestamp: string;
    exitStrategyApplied?: boolean;
}
/**
 * Process a trade execution job
 */
export declare function processTradeJob(job: Job<TradeJobData>): Promise<TradeResult>;
/**
 * Add a trade job to the processing queue
 */
export declare function addTradeJob(data: TradeJobData): Promise<Job<TradeJobData>>;
/**
 * Validate trade parameters before execution
 */
export declare function validateTradeData(data: TradeJobData): string[];
//# sourceMappingURL=tradeProcessor.d.ts.map