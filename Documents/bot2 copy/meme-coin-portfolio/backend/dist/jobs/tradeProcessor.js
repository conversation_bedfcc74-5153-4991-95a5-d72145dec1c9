"use strict";
/**
 * Trade processing job handlers
 *
 * Handles the execution of trading operations:
 * - Trade validation and execution
 * - Exit strategy application
 * - Position tracking
 * - Error handling and retries
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processTradeJob = processTradeJob;
exports.addTradeJob = addTradeJob;
exports.validateTradeData = validateTradeData;
const bullmq_1 = require("bullmq");
const jupiterClient_1 = require("../services/jupiterClient");
const solanaClient_1 = require("../services/solanaClient");
const redis_1 = require("../services/redis");
const pino_1 = __importDefault(require("pino"));
// Initialize trade queue
const tradeQueue = new bullmq_1.Queue('trade-processing', {
    connection: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
    },
});
const logger = (0, pino_1.default)({ name: 'trade-processor' });
/**
 * Process a trade execution job
 */
async function processTradeJob(job) {
    const { inputMint, outputMint, amount, slippageBps, quote, applyExitStrategy } = job.data;
    logger.info(`Processing trade: ${amount} ${inputMint} -> ${outputMint}`);
    try {
        // Validate wallet
        const wallet = (0, solanaClient_1.getWallet)();
        if (!wallet) {
            throw new Error('Wallet not configured');
        }
        // Execute the swap using Jupiter client
        const signature = await jupiterClient_1.jupiterClient.executeSwap(inputMint, outputMint, amount, slippageBps);
        // Get fresh quote for result calculation
        const freshQuote = await jupiterClient_1.jupiterClient.getQuote(inputMint, outputMint, amount, slippageBps);
        // Calculate actual results
        const inputAmount = freshQuote.inAmount;
        const outputAmount = freshQuote.outAmount;
        const actualSlippage = Math.abs((parseFloat(outputAmount) - parseFloat(quote.outAmount)) / parseFloat(quote.outAmount)) * 100;
        const result = {
            signature,
            inputAmount,
            outputAmount,
            actualSlippage,
            fee: 0.00025, // Estimated transaction fee
            timestamp: new Date().toISOString()
        };
        // Apply exit strategy if requested
        if (applyExitStrategy) {
            await applyExitStrategyToPosition({
                tokenMint: outputMint,
                entryPrice: parseFloat(outputAmount) / parseFloat(inputAmount),
                amount: parseFloat(outputAmount),
                signature,
                timestamp: result.timestamp
            });
            result.exitStrategyApplied = true;
        }
        logger.info(`Trade completed successfully: ${signature}`);
        return result;
    }
    catch (error) {
        logger.error('Trade processing failed:', error);
        throw error;
    }
}
/**
 * Apply exit strategy to a new position
 */
async function applyExitStrategyToPosition(position) {
    try {
        logger.info(`Applying exit strategy to position: ${position.tokenMint}`);
        // Store position in cache for monitoring
        await redis_1.PortfolioCache.setPosition(position.tokenMint, {
            ...position,
            exitStrategy: {
                stopLoss: 15, // 15% stop loss
                trailingStop: 15, // 15% trailing stop
                takeProfitLevels: [50, 100, 150, 200], // Take profit at 50%, 100%, 150%, 200%
                moonBag: 25 // Keep 25% as moon bag
            }
        });
        logger.info(`Exit strategy applied to ${position.tokenMint}`);
    }
    catch (error) {
        logger.error('Failed to apply exit strategy:', error);
        throw error;
    }
}
/**
 * Add a trade job to the processing queue
 */
async function addTradeJob(data) {
    try {
        const job = await tradeQueue.add('execute-trade', data, {
            attempts: 3,
            backoff: {
                type: 'exponential',
                delay: 2000,
            },
            removeOnComplete: 100,
            removeOnFail: 50,
        });
        logger.info(`Trade job added to queue: ${job.id}`);
        return job;
    }
    catch (error) {
        logger.error('Failed to add trade job:', error);
        throw error;
    }
}
/**
 * Validate trade parameters before execution
 */
function validateTradeData(data) {
    const errors = [];
    if (!data.inputMint) {
        errors.push('Input mint is required');
    }
    if (!data.outputMint) {
        errors.push('Output mint is required');
    }
    if (!data.amount || data.amount <= 0) {
        errors.push('Amount must be positive');
    }
    if (!data.slippageBps || data.slippageBps < 0 || data.slippageBps > 10000) {
        errors.push('Slippage must be between 0 and 10000 basis points');
    }
    if (!data.walletAddress) {
        errors.push('Wallet address is required');
    }
    if (!data.quote) {
        errors.push('Quote is required');
    }
    return errors;
}
//# sourceMappingURL=tradeProcessor.js.map