{"version": 3, "file": "tradeProcessor.js", "sourceRoot": "", "sources": ["../../src/jobs/tradeProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;GAQG;;;;;AA0CH,0CAoDC;AAoCD,kCAkBC;AAKD,8CA4BC;AAnLD,mCAAoC;AACpC,6DAA0D;AAC1D,2DAAgF;AAChF,6CAAmD;AACnD,gDAAwB;AAExB,yBAAyB;AACzB,MAAM,UAAU,GAAG,IAAI,cAAK,CAAC,kBAAkB,EAAE;IAC/C,UAAU,EAAE;QACV,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,WAAW;QAC3C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,MAAM,CAAC;KACjD;CACF,CAAC,CAAC;AAEH,MAAM,MAAM,GAAG,IAAA,cAAI,EAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,CAAC,CAAC;AAuBjD;;GAEG;AACI,KAAK,UAAU,eAAe,CAAC,GAAsB;IAC1D,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,KAAK,EAAE,iBAAiB,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAE1F,MAAM,CAAC,IAAI,CAAC,qBAAqB,MAAM,IAAI,SAAS,OAAO,UAAU,EAAE,CAAC,CAAC;IAEzE,IAAI,CAAC;QACH,kBAAkB;QAClB,MAAM,MAAM,GAAG,IAAA,wBAAS,GAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;QAC3C,CAAC;QAED,wCAAwC;QACxC,MAAM,SAAS,GAAG,MAAM,6BAAa,CAAC,WAAW,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;QAE9F,yCAAyC;QACzC,MAAM,UAAU,GAAG,MAAM,6BAAa,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;QAE5F,2BAA2B;QAC3B,MAAM,WAAW,GAAG,UAAU,CAAC,QAAQ,CAAC;QACxC,MAAM,YAAY,GAAG,UAAU,CAAC,SAAS,CAAC;QAC1C,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAC7B,CAAC,UAAU,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CACvF,GAAG,GAAG,CAAC;QAER,MAAM,MAAM,GAAgB;YAC1B,SAAS;YACT,WAAW;YACX,YAAY;YACZ,cAAc;YACd,GAAG,EAAE,OAAO,EAAE,4BAA4B;YAC1C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,mCAAmC;QACnC,IAAI,iBAAiB,EAAE,CAAC;YACtB,MAAM,2BAA2B,CAAC;gBAChC,SAAS,EAAE,UAAU;gBACrB,UAAU,EAAE,UAAU,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC;gBAC9D,MAAM,EAAE,UAAU,CAAC,YAAY,CAAC;gBAChC,SAAS;gBACT,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B,CAAC,CAAC;YACF,MAAc,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAC7C,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,iCAAiC,SAAS,EAAE,CAAC,CAAC;QAC1D,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QAChD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,2BAA2B,CAAC,QAM1C;IACC,IAAI,CAAC;QACH,MAAM,CAAC,IAAI,CAAC,uCAAuC,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;QAEzE,yCAAyC;QACzC,MAAM,sBAAc,CAAC,WAAW,CAAC,QAAQ,CAAC,SAAS,EAAE;YACnD,GAAG,QAAQ;YACX,YAAY,EAAE;gBACZ,QAAQ,EAAE,EAAE,EAAE,gBAAgB;gBAC9B,YAAY,EAAE,EAAE,EAAE,oBAAoB;gBACtC,gBAAgB,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,uCAAuC;gBAC9E,OAAO,EAAE,EAAE,CAAC,uBAAuB;aACpC;SACF,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC,4BAA4B,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;IAChE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACtD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,WAAW,CAAC,IAAkB;IAClD,IAAI,CAAC;QACH,MAAM,GAAG,GAAG,MAAM,UAAU,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,EAAE;YACtD,QAAQ,EAAE,CAAC;YACX,OAAO,EAAE;gBACP,IAAI,EAAE,aAAa;gBACnB,KAAK,EAAE,IAAI;aACZ;YACD,gBAAgB,EAAE,GAAG;YACrB,YAAY,EAAE,EAAE;SACjB,CAAC,CAAC;QAEH,MAAM,CAAC,IAAI,CAAC,6BAA6B,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;QACnD,OAAO,GAAG,CAAC;IACb,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;QAChD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAgB,iBAAiB,CAAC,IAAkB;IAClD,MAAM,MAAM,GAAa,EAAE,CAAC;IAE5B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;QACpB,MAAM,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;IACxC,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;QACrB,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;IACzC,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;QACrC,MAAM,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;IACzC,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,EAAE,CAAC;QAC1E,MAAM,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;IACnE,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;QACxB,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAED,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;QAChB,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;IACnC,CAAC;IAED,OAAO,MAAM,CAAC;AAChB,CAAC"}