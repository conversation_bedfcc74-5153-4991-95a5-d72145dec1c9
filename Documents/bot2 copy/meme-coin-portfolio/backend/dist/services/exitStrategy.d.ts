/**
 * Exit Strategy Engine
 *
 * Implements automated trading rules and exit strategies:
 * - Stop-loss orders
 * - Take-profit levels
 * - Trailing stops
 * - Moon bag management
 * - Risk management
 */
export interface ExitStrategy {
    stopLoss?: number;
    trailingStop?: number;
    takeProfitLevels?: number[];
    moonBag?: number;
    maxSlippage?: number;
    enabled?: boolean;
}
export interface Position {
    tokenMint: string;
    symbol: string;
    entryPrice: number;
    currentPrice: number;
    amount: number;
    value: number;
    entryTimestamp: string;
    entrySignature: string;
    exitStrategy: ExitStrategy;
    highestPrice?: number;
    profitTaken?: number;
    moonBagAmount?: number;
}
export interface ExitTrigger {
    type: 'stop-loss' | 'take-profit' | 'trailing-stop' | 'manual';
    reason: string;
    targetPrice: number;
    sellPercentage: number;
}
declare class ExitStrategyEngine {
    private positions;
    private monitoringInterval;
    private readonly MONITORING_INTERVAL_MS;
    constructor();
    /**
     * Add a position to monitor
     */
    addPosition(position: Position): Promise<void>;
    /**
     * Remove a position from monitoring
     */
    removePosition(tokenMint: string): Promise<void>;
    /**
     * Update position with current price
     */
    updatePosition(tokenMint: string, currentPrice: number): Promise<void>;
    /**
     * Check if any exit conditions are met
     */
    private checkExitTriggers;
    /**
     * Check if profit has already been taken at a specific level
     */
    private hasTakenProfitAt;
    /**
     * Calculate how much to sell for take-profit
     */
    private calculateTakeProfitSellPercentage;
    /**
     * Execute exit trade
     */
    private executeExit;
    /**
     * Start monitoring positions
     */
    private startMonitoring;
    /**
     * Stop monitoring positions
     */
    stopMonitoring(): void;
    /**
     * Monitor all positions for exit conditions
     */
    private monitorPositions;
    /**
     * Get all monitored positions
     */
    getPositions(): Position[];
    /**
     * Get position by token mint
     */
    getPosition(tokenMint: string): Position | undefined;
    /**
     * Update exit strategy for a position
     */
    updateExitStrategy(tokenMint: string, exitStrategy: Partial<ExitStrategy>): Promise<void>;
    /**
     * Load positions from cache on startup
     */
    loadPositionsFromCache(): Promise<void>;
}
export declare const exitStrategyEngine: ExitStrategyEngine;
export {};
//# sourceMappingURL=exitStrategy.d.ts.map