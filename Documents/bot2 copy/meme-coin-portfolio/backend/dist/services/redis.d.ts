/**
 * Redis connection and management service
 *
 * Provides centralized Redis connection for:
 * - BullMQ job queues
 * - Caching portfolio data
 * - Session management
 * - Real-time data storage
 */
import Redis from 'ioredis';
/**
 * Initialize Redis connection with retry logic
 */
export declare function initializeRedis(): Promise<Redis>;
/**
 * Get the Redis client instance
 */
export declare function getRedisClient(): Redis;
/**
 * Portfolio data caching utilities
 */
export declare class PortfolioCache {
    private static readonly PORTFOLIO_KEY;
    private static readonly POSITIONS_KEY;
    private static readonly SIGNALS_KEY;
    private static readonly TTL;
    /**
     * Cache portfolio summary data
     */
    static setPortfolioData(data: any): Promise<void>;
    /**
     * Get cached portfolio summary data
     */
    static getPortfolioData(): Promise<any | null>;
    /**
     * Cache active positions
     */
    static setPositions(positions: any[]): Promise<void>;
    /**
     * Get cached positions
     */
    static getPositions(): Promise<any[] | null>;
    /**
     * Add a new signal to the recent signals list
     */
    static addSignal(signal: any): Promise<void>;
    /**
     * Get recent signals
     */
    static getRecentSignals(limit?: number): Promise<any[]>;
    /**
     * Cache individual position with exit strategy
     */
    static setPosition(tokenMint: string, position: any): Promise<void>;
    /**
     * Get individual position
     */
    static getPosition(tokenMint: string): Promise<any | null>;
    /**
     * Get all active positions
     */
    static getAllPositions(): Promise<any[]>;
    /**
     * Delete individual position
     */
    static deletePosition(tokenMint: string): Promise<void>;
    /**
     * Invalidate portfolio cache
     */
    static invalidatePortfolio(): Promise<void>;
    /**
     * Clear all cached data
     */
    static clearAll(): Promise<void>;
}
/**
 * Configuration caching utilities
 */
export declare class ConfigCache {
    private static readonly EXIT_STRATEGY_KEY;
    private static readonly TRADING_CONFIG_KEY;
    private static readonly TTL;
    /**
     * Cache exit strategy configuration
     */
    static setExitStrategy(config: any): Promise<void>;
    /**
     * Get cached exit strategy configuration
     */
    static getExitStrategy(): Promise<any | null>;
    /**
     * Cache trading configuration
     */
    static setTradingConfig(config: any): Promise<void>;
    /**
     * Get cached trading configuration
     */
    static getTradingConfig(): Promise<any | null>;
}
/**
 * Close Redis connection
 */
export declare function closeRedis(): Promise<void>;
//# sourceMappingURL=redis.d.ts.map