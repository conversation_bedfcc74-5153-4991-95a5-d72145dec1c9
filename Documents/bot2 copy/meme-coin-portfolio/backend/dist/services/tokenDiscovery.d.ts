/**
 * Token Discovery Service
 *
 * Provides comprehensive token search and discovery functionality:
 * - Search tokens by symbol, name, or address
 * - Get trending tokens
 * - Filter by market cap, liquidity, etc.
 * - Integration with Jupiter token list
 */
export interface TokenInfo {
    address: string;
    symbol: string;
    name: string;
    decimals: number;
    logoURI?: string;
    tags?: string[];
    verified?: boolean;
    price?: number;
    marketCap?: number;
    volume24h?: number;
    priceChange24h?: number;
    liquidity?: number;
    holders?: number;
    isMemeCoin?: boolean;
}
export interface TokenSearchFilters {
    query?: string;
    minPrice?: number;
    maxPrice?: number;
    minMarketCap?: number;
    maxMarketCap?: number;
    minVolume?: number;
    maxVolume?: number;
    minLiquidity?: number;
    maxLiquidity?: number;
    verified?: boolean;
    memeCoinsOnly?: boolean;
    limit?: number;
    offset?: number;
}
declare class TokenDiscoveryService {
    private jupiterTokens;
    private lastTokenListUpdate;
    private readonly TOKEN_LIST_CACHE_DURATION;
    constructor();
    /**
     * Initialize and cache Jupiter token list
     */
    private initializeTokenList;
    /**
     * Check if token list needs refresh
     */
    private ensureTokenListFresh;
    /**
     * Determine if a token is likely a meme coin
     */
    private isMemeCoin;
    /**
     * Search tokens with filters
     */
    searchTokens(filters?: TokenSearchFilters): Promise<TokenInfo[]>;
    /**
     * Get token by address
     */
    getTokenByAddress(address: string): Promise<TokenInfo | null>;
    /**
     * Get trending meme coins
     */
    getTrendingMemeCoins(limit?: number): Promise<TokenInfo[]>;
    /**
     * Get popular tokens by volume
     */
    getPopularTokens(limit?: number): Promise<TokenInfo[]>;
    /**
     * Enrich tokens with market data
     */
    private enrichTokensWithMarketData;
    /**
     * Get token suggestions for autocomplete
     */
    getTokenSuggestions(query: string, limit?: number): Promise<TokenInfo[]>;
}
export declare const tokenDiscoveryService: TokenDiscoveryService;
export {};
//# sourceMappingURL=tokenDiscovery.d.ts.map