"use strict";
/**
 * Token Discovery Service
 *
 * Provides comprehensive token search and discovery functionality:
 * - Search tokens by symbol, name, or address
 * - Get trending tokens
 * - Filter by market cap, liquidity, etc.
 * - Integration with Jupiter token list
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.tokenDiscoveryService = void 0;
const axios_1 = __importDefault(require("axios"));
const pino_1 = __importDefault(require("pino"));
const jupiterClient_1 = require("./jupiterClient");
const logger = (0, pino_1.default)({ name: 'token-discovery' });
class TokenDiscoveryService {
    jupiterTokens = new Map();
    lastTokenListUpdate = 0;
    TOKEN_LIST_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
    constructor() {
        this.initializeTokenList();
    }
    /**
     * Initialize and cache Jupiter token list
     */
    async initializeTokenList() {
        try {
            logger.info('Initializing token list from Jupiter...');
            const response = await axios_1.default.get('https://token.jup.ag/all');
            const tokens = response.data;
            this.jupiterTokens.clear();
            for (const token of tokens) {
                const tokenInfo = {
                    address: token.address,
                    symbol: token.symbol,
                    name: token.name,
                    decimals: token.decimals,
                    logoURI: token.logoURI,
                    tags: token.tags || [],
                    verified: token.tags?.includes('verified') || false,
                    isMemeCoin: this.isMemeCoin(token)
                };
                this.jupiterTokens.set(token.address, tokenInfo);
            }
            this.lastTokenListUpdate = Date.now();
            logger.info(`Loaded ${this.jupiterTokens.size} tokens from Jupiter`);
        }
        catch (error) {
            logger.error('Failed to load Jupiter token list:', error);
        }
    }
    /**
     * Check if token list needs refresh
     */
    async ensureTokenListFresh() {
        if (Date.now() - this.lastTokenListUpdate > this.TOKEN_LIST_CACHE_DURATION) {
            await this.initializeTokenList();
        }
    }
    /**
     * Determine if a token is likely a meme coin
     */
    isMemeCoin(token) {
        const memeKeywords = ['meme', 'doge', 'shib', 'pepe', 'wojak', 'chad', 'moon', 'rocket', 'ape', 'diamond'];
        const tokenText = `${token.symbol} ${token.name}`.toLowerCase();
        return memeKeywords.some(keyword => tokenText.includes(keyword)) ||
            token.tags?.includes('meme') ||
            token.tags?.includes('community');
    }
    /**
     * Search tokens with filters
     */
    async searchTokens(filters = {}) {
        await this.ensureTokenListFresh();
        let results = Array.from(this.jupiterTokens.values());
        // Apply text search filter
        if (filters.query) {
            const query = filters.query.toLowerCase();
            results = results.filter(token => token.symbol.toLowerCase().includes(query) ||
                token.name.toLowerCase().includes(query) ||
                token.address.toLowerCase().includes(query));
        }
        // Apply verification filter
        if (filters.verified !== undefined) {
            results = results.filter(token => token.verified === filters.verified);
        }
        // Apply meme coin filter
        if (filters.memeCoinsOnly) {
            results = results.filter(token => token.isMemeCoin);
        }
        // Enrich with market data for top results
        const limit = Math.min(filters.limit || 50, 100);
        const enrichedResults = await this.enrichTokensWithMarketData(results.slice(filters.offset || 0, (filters.offset || 0) + limit));
        // Apply market data filters
        let filteredResults = enrichedResults;
        if (filters.minPrice !== undefined) {
            filteredResults = filteredResults.filter(token => (token.price || 0) >= filters.minPrice);
        }
        if (filters.maxPrice !== undefined) {
            filteredResults = filteredResults.filter(token => (token.price || 0) <= filters.maxPrice);
        }
        if (filters.minMarketCap !== undefined) {
            filteredResults = filteredResults.filter(token => (token.marketCap || 0) >= filters.minMarketCap);
        }
        if (filters.maxMarketCap !== undefined) {
            filteredResults = filteredResults.filter(token => (token.marketCap || 0) <= filters.maxMarketCap);
        }
        if (filters.minVolume !== undefined) {
            filteredResults = filteredResults.filter(token => (token.volume24h || 0) >= filters.minVolume);
        }
        if (filters.maxVolume !== undefined) {
            filteredResults = filteredResults.filter(token => (token.volume24h || 0) <= filters.maxVolume);
        }
        if (filters.minLiquidity !== undefined) {
            filteredResults = filteredResults.filter(token => (token.liquidity || 0) >= filters.minLiquidity);
        }
        if (filters.maxLiquidity !== undefined) {
            filteredResults = filteredResults.filter(token => (token.liquidity || 0) <= filters.maxLiquidity);
        }
        return filteredResults;
    }
    /**
     * Get token by address
     */
    async getTokenByAddress(address) {
        await this.ensureTokenListFresh();
        const token = this.jupiterTokens.get(address);
        if (!token)
            return null;
        // Enrich with market data
        const enriched = await this.enrichTokensWithMarketData([token]);
        return enriched[0] || null;
    }
    /**
     * Get trending meme coins
     */
    async getTrendingMemeCoins(limit = 20) {
        const memeCoins = await this.searchTokens({
            memeCoinsOnly: true,
            limit: 100
        });
        // Sort by volume and market cap
        return memeCoins
            .filter(token => token.volume24h && token.marketCap)
            .sort((a, b) => {
            const scoreA = (a.volume24h || 0) * Math.log(a.marketCap || 1);
            const scoreB = (b.volume24h || 0) * Math.log(b.marketCap || 1);
            return scoreB - scoreA;
        })
            .slice(0, limit);
    }
    /**
     * Get popular tokens by volume
     */
    async getPopularTokens(limit = 20) {
        const tokens = await this.searchTokens({ limit: 100 });
        return tokens
            .filter(token => token.volume24h)
            .sort((a, b) => (b.volume24h || 0) - (a.volume24h || 0))
            .slice(0, limit);
    }
    /**
     * Enrich tokens with market data
     */
    async enrichTokensWithMarketData(tokens) {
        const enrichedTokens = [];
        for (const token of tokens) {
            try {
                // Get price from Jupiter
                const price = await jupiterClient_1.jupiterClient.getTokenPrice(token.address);
                // Mock market data (in production, use real market data APIs)
                const enrichedToken = {
                    ...token,
                    price,
                    marketCap: price * 1000000, // Mock market cap
                    volume24h: Math.random() * 100000, // Mock volume
                    priceChange24h: (Math.random() - 0.5) * 20, // Mock price change
                    liquidity: Math.random() * 50000, // Mock liquidity
                    holders: Math.floor(Math.random() * 10000) // Mock holders
                };
                enrichedTokens.push(enrichedToken);
            }
            catch (error) {
                // If enrichment fails, include token without market data
                enrichedTokens.push(token);
            }
        }
        return enrichedTokens;
    }
    /**
     * Get token suggestions for autocomplete
     */
    async getTokenSuggestions(query, limit = 10) {
        if (!query || query.length < 2)
            return [];
        await this.ensureTokenListFresh();
        const queryLower = query.toLowerCase();
        const suggestions = [];
        // Exact symbol matches first
        for (const token of this.jupiterTokens.values()) {
            if (token.symbol.toLowerCase() === queryLower) {
                suggestions.push(token);
            }
        }
        // Symbol starts with query
        for (const token of this.jupiterTokens.values()) {
            if (token.symbol.toLowerCase().startsWith(queryLower) &&
                !suggestions.find(t => t.address === token.address)) {
                suggestions.push(token);
            }
        }
        // Name starts with query
        for (const token of this.jupiterTokens.values()) {
            if (token.name.toLowerCase().startsWith(queryLower) &&
                !suggestions.find(t => t.address === token.address)) {
                suggestions.push(token);
            }
        }
        // Contains query
        for (const token of this.jupiterTokens.values()) {
            if ((token.symbol.toLowerCase().includes(queryLower) ||
                token.name.toLowerCase().includes(queryLower)) &&
                !suggestions.find(t => t.address === token.address)) {
                suggestions.push(token);
            }
        }
        return suggestions.slice(0, limit);
    }
}
exports.tokenDiscoveryService = new TokenDiscoveryService();
//# sourceMappingURL=tokenDiscovery.js.map