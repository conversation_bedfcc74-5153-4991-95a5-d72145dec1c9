{"name": "meme-coin-portfolio-backend", "version": "1.0.0", "description": "Backend API and trading bot for meme coin portfolio management", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["solana", "trading", "meme-coin", "portfolio", "defi"], "author": "Meme Coin Portfolio Team", "license": "MIT", "dependencies": {"@jup-ag/api": "^6.0.25", "@solana/web3.js": "^1.95.2", "ajv": "^8.17.1", "axios": "^1.7.7", "bs58": "^6.0.0", "bullmq": "^5.12.15", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "ioredis": "^5.4.1", "pino": "^9.4.0", "pino-pretty": "^11.2.2", "socket.io": "^4.7.5"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^22.5.4", "@typescript-eslint/eslint-plugin": "^8.5.0", "@typescript-eslint/parser": "^8.5.0", "eslint": "^9.10.0", "jest": "^29.7.0", "nodemon": "^3.1.4", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "engines": {"node": ">=18.0.0"}}