/**
 * Main entry point for the Meme Coin Portfolio Backend
 *
 * This server provides:
 * - REST API for portfolio management and trading
 * - WebSocket connections for real-time updates
 * - Background job processing for trading automation
 * - Integration with Solana blockchain and Jupiter DEX
 */

import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';
import pino from 'pino';

// Import route handlers
import portfolioRoutes from './routes/portfolio';
import tradeRoutes from './routes/trade';
import configRoutes from './routes/config';
import healthRoutes from './routes/health';

// Import services
import { initializeRedis } from './services/redis';
import { initializeJobQueues } from './jobs/queueManager';
import { setupWebSocketHandlers } from './services/websocket';
import { initializeSolana } from './services/solanaClient';

// Load environment variables
dotenv.config();

// Initialize logger
const logger = pino({
  level: process.env['LOG_LEVEL'] || 'info',
  transport: {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname'
    }
  }
});

// Validate required environment variables
const requiredEnvVars = ['WALLET_ADDRESS', 'RPC_URL'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  logger.error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
  logger.error('Please check your .env file and ensure all required variables are set');
  process.exit(1);
}

// Create Express app
const app = express();
const server = createServer(app);

// Initialize Socket.IO with CORS
const io = new Server(server, {
  cors: {
    origin: process.env['FRONTEND_URL'] || "http://localhost:3000",
    methods: ["GET", "POST"]
  }
});

// Middleware
app.use(cors({
  origin: process.env['FRONTEND_URL'] || "http://localhost:3000",
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Request logging middleware
app.use((req, _res, next) => {
  logger.info({
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  }, 'Incoming request');
  next();
});

// API Routes
app.use('/api/portfolio', portfolioRoutes);
app.use('/api/trade', tradeRoutes);
app.use('/api/config', configRoutes);
app.use('/health', healthRoutes);

// WebSocket namespace for real-time updates
const wsNamespace = io.of('/ws');
setupWebSocketHandlers(wsNamespace, logger);

// Global error handler
app.use((err: Error, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  logger.error({
    error: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method
  }, 'Unhandled error');

  res.status(500).json({
    error: 'Internal server error',
    message: process.env['NODE_ENV'] === 'development' ? err.message : 'Something went wrong'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Not found',
    message: `Route ${req.originalUrl} not found`
  });
});

// Initialize services and start server
async function startServer() {
  try {
    // Initialize Redis connection
    logger.info('Initializing Redis connection...');
    await initializeRedis();

    // Initialize Solana connection
    logger.info('Initializing Solana connection...');
    await initializeSolana();

    // Initialize job queues
    logger.info('Initializing job queues...');
    await initializeJobQueues();

    // Start server
    const port = process.env['BACKEND_PORT'] || 4000;
    server.listen(port, () => {
      logger.info(`🚀 Meme Coin Portfolio Backend running on port ${port}`);
      logger.info(`📊 Health check available at http://localhost:${port}/health`);
      logger.info(`🔌 WebSocket endpoint: ws://localhost:${port}/ws`);

      // Log configuration status
      if (!process.env['WALLET_PRIVATE_KEY'] || process.env['WALLET_PRIVATE_KEY'] === 'TODO_REPLACE_WITH_YOUR_PRIVATE_KEY') {
        logger.warn('⚠️  WALLET_PRIVATE_KEY not configured - trading will be disabled');
      }

      if (!process.env['HELIUS_KEY'] || process.env['HELIUS_KEY'] === 'TODO_REPLACE_WITH_YOUR_HELIUS_API_KEY') {
        logger.warn('⚠️  HELIUS_KEY not configured - some features may be limited');
      }
    });

  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

// Start the server
startServer();

export { app, io, logger };
