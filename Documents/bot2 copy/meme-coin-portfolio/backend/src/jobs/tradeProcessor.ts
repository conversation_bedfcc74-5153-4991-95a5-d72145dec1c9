/**
 * Trade processing job handlers
 * 
 * Handles the execution of trading operations:
 * - Trade validation and execution
 * - Exit strategy application
 * - Position tracking
 * - Error handling and retries
 */

import { Job } from 'bullmq';
import { jupiterClient } from '../services/jupiterClient';
import { sendAndConfirmTransaction, getWallet } from '../services/solanaClient';
import { PortfolioCache } from '../services/redis';
import pino from 'pino';

const logger = pino({ name: 'trade-processor' });

export interface TradeJobData {
  inputMint: string;
  outputMint: string;
  amount: number;
  slippageBps: number;
  quote: any;
  walletAddress: string;
  applyExitStrategy: boolean;
  timestamp: string;
}

export interface TradeResult {
  signature: string;
  inputAmount: string;
  outputAmount: string;
  actualSlippage: number;
  fee: number;
  timestamp: string;
  exitStrategyApplied?: boolean;
}

/**
 * Process a trade execution job
 */
export async function processTradeJob(job: Job<TradeJobData>): Promise<TradeResult> {
  const { inputMint, outputMint, amount, slippageBps, quote, applyExitStrategy } = job.data;
  
  logger.info(`Processing trade: ${amount} ${inputMint} -> ${outputMint}`);

  try {
    // Validate wallet
    const wallet = getWallet();
    if (!wallet) {
      throw new Error('Wallet not configured');
    }

    // Re-validate quote (prices may have changed)
    const freshQuote = await jupiterClient.getQuote(inputMint, outputMint, amount, slippageBps);
    const priceImpact = parseFloat(freshQuote.priceImpactPct);
    
    // Check if price impact is still acceptable
    if (priceImpact > 5) {
      throw new Error(`Price impact too high: ${priceImpact}%`);
    }

    // Build and execute transaction
    const transaction = await jupiterClient.getSwapTransaction(
      freshQuote,
      wallet.publicKey.toString()
    );

    // Sign transaction
    transaction.sign([wallet]);

    // Send and confirm transaction
    const signature = await sendAndConfirmTransaction(transaction);

    // Calculate actual results
    const inputAmount = freshQuote.inAmount;
    const outputAmount = freshQuote.outAmount;
    const actualSlippage = Math.abs(
      (parseFloat(outputAmount) - parseFloat(quote.outAmount)) / parseFloat(quote.outAmount)
    ) * 100;

    const result: TradeResult = {
      signature,
      inputAmount,
      outputAmount,
      actualSlippage,
      fee: 0.00025, // Estimated transaction fee
      timestamp: new Date().toISOString()
    };

    // Apply exit strategy if requested
    if (applyExitStrategy) {
      await applyExitStrategyToPosition({
        tokenMint: outputMint,
        entryPrice: parseFloat(inputAmount) / parseFloat(outputAmount),
        amount: parseFloat(outputAmount),
        signature
      });
      result.exitStrategyApplied = true;
    }

    // Update portfolio cache
    await updatePortfolioAfterTrade(result, job.data);

    logger.info(`Trade completed successfully: ${signature}`);
    return result;

  } catch (error) {
    logger.error('Trade execution failed:', error);
    throw error;
  }
}

/**
 * Apply exit strategy to a new position
 */
async function applyExitStrategyToPosition(position: {
  tokenMint: string;
  entryPrice: number;
  amount: number;
  signature: string;
}): Promise<void> {
  try {
    logger.info(`Applying exit strategy to position: ${position.tokenMint}`);

    // TODO: Implement exit strategy logic
    // This would involve:
    // 1. Setting up stop-loss monitoring
    // 2. Setting up take-profit levels
    // 3. Configuring trailing stop
    // 4. Setting up moon bag rules

    // For now, just log the strategy application
    logger.info(`Exit strategy applied to ${position.tokenMint}:`, {
      entryPrice: position.entryPrice,
      amount: position.amount,
      stopLoss: position.entryPrice * 0.85, // 15% stop loss
      takeProfitLevels: [
        position.entryPrice * 1.5,  // 50% profit
        position.entryPrice * 2.0,  // 100% profit
        position.entryPrice * 2.5,  // 150% profit
        position.entryPrice * 3.0   // 200% profit
      ]
    });

  } catch (error) {
    logger.error('Failed to apply exit strategy:', error);
    // Don't throw here - trade was successful even if exit strategy setup failed
  }
}

/**
 * Update portfolio data after a successful trade
 */
async function updatePortfolioAfterTrade(
  tradeResult: TradeResult, 
  tradeData: TradeJobData
): Promise<void> {
  try {
    // Get current portfolio data
    const portfolioData = await PortfolioCache.getPortfolioData();
    
    if (portfolioData) {
      // Update trade history
      const newTrade = {
        id: tradeResult.signature,
        type: tradeData.inputMint === 'So11111111111111111111111111111111111111112' ? 'buy' : 'sell',
        inputMint: tradeData.inputMint,
        outputMint: tradeData.outputMint,
        inputAmount: parseFloat(tradeResult.inputAmount),
        outputAmount: parseFloat(tradeResult.outputAmount),
        slippage: tradeResult.actualSlippage,
        signature: tradeResult.signature,
        timestamp: tradeResult.timestamp
      };

      portfolioData.recentTrades = portfolioData.recentTrades || [];
      portfolioData.recentTrades.unshift(newTrade);
      
      // Keep only last 50 trades
      if (portfolioData.recentTrades.length > 50) {
        portfolioData.recentTrades = portfolioData.recentTrades.slice(0, 50);
      }

      // Update portfolio cache
      await PortfolioCache.setPortfolioData(portfolioData);
    }

    logger.debug('Portfolio updated after trade');
  } catch (error) {
    logger.error('Failed to update portfolio after trade:', error);
    // Don't throw - this is not critical
  }
}

/**
 * Validate trade parameters before execution
 */
export function validateTradeData(data: TradeJobData): string[] {
  const errors: string[] = [];

  if (!data.inputMint) {
    errors.push('Input mint is required');
  }

  if (!data.outputMint) {
    errors.push('Output mint is required');
  }

  if (!data.amount || data.amount <= 0) {
    errors.push('Amount must be positive');
  }

  if (!data.slippageBps || data.slippageBps < 0 || data.slippageBps > 10000) {
    errors.push('Slippage must be between 0 and 10000 basis points');
  }

  if (!data.walletAddress) {
    errors.push('Wallet address is required');
  }

  if (!data.quote) {
    errors.push('Quote is required');
  }

  return errors;
}

/**
 * Export the addTradeJob function for use in routes
 */
export { addTradeJob } from './queueManager';
