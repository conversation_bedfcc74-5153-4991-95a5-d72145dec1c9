/**
 * Trade processing job handlers
 *
 * Handles the execution of trading operations:
 * - Trade validation and execution
 * - Exit strategy application
 * - Position tracking
 * - Error handling and retries
 */

import { Job, Queue } from 'bullmq';
import { jupiterClient } from '../services/jupiterClient';
import { sendAndConfirmTransaction, getWallet } from '../services/solanaClient';
import { PortfolioCache } from '../services/redis';
import pino from 'pino';

// Initialize trade queue
const tradeQueue = new Queue('trade-processing', {
  connection: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
  },
});

const logger = pino({ name: 'trade-processor' });

export interface TradeJobData {
  inputMint: string;
  outputMint: string;
  amount: number;
  slippageBps: number;
  quote: any;
  walletAddress: string;
  applyExitStrategy: boolean;
  timestamp: string;
}

export interface TradeResult {
  signature: string;
  inputAmount: string;
  outputAmount: string;
  actualSlippage: number;
  fee: number;
  timestamp: string;
  exitStrategyApplied?: boolean;
}

/**
 * Process a trade execution job
 */
export async function processTradeJob(job: Job<TradeJobData>): Promise<TradeResult> {
  const { inputMint, outputMint, amount, slippageBps, quote, applyExitStrategy } = job.data;

  logger.info(`Processing trade: ${amount} ${inputMint} -> ${outputMint}`);

  try {
    // Validate wallet
    const wallet = getWallet();
    if (!wallet) {
      throw new Error('Wallet not configured');
    }

    // Execute the swap using Jupiter client
    const signature = await jupiterClient.executeSwap(inputMint, outputMint, amount, slippageBps);

    // Get fresh quote for result calculation
    const freshQuote = await jupiterClient.getQuote(inputMint, outputMint, amount, slippageBps);

    // Calculate actual results
    const inputAmount = freshQuote.inAmount;
    const outputAmount = freshQuote.outAmount;
    const actualSlippage = Math.abs(
      (parseFloat(outputAmount) - parseFloat(quote.outAmount)) / parseFloat(quote.outAmount)
    ) * 100;

    const result: TradeResult = {
      signature,
      inputAmount,
      outputAmount,
      actualSlippage,
      fee: 0.00025, // Estimated transaction fee
      timestamp: new Date().toISOString()
    };

    // Apply exit strategy if requested
    if (applyExitStrategy) {
      await applyExitStrategyToPosition({
        tokenMint: outputMint,
        entryPrice: parseFloat(outputAmount) / parseFloat(inputAmount),
        amount: parseFloat(outputAmount),
        signature,
        timestamp: result.timestamp
      });
      (result as any).exitStrategyApplied = true;
    }

    logger.info(`Trade completed successfully: ${signature}`);
    return result;
  } catch (error) {
    logger.error('Trade processing failed:', error);
    throw error;
  }
}

/**
 * Apply exit strategy to a new position
 */
async function applyExitStrategyToPosition(position: {
  tokenMint: string;
  entryPrice: number;
  amount: number;
  signature: string;
  timestamp: string;
}): Promise<void> {
  try {
    logger.info(`Applying exit strategy to position: ${position.tokenMint}`);

    // Store position in cache for monitoring
    await PortfolioCache.setPosition(position.tokenMint, {
      ...position,
      exitStrategy: {
        stopLoss: 15, // 15% stop loss
        trailingStop: 15, // 15% trailing stop
        takeProfitLevels: [50, 100, 150, 200], // Take profit at 50%, 100%, 150%, 200%
        moonBag: 25 // Keep 25% as moon bag
      }
    });

    logger.info(`Exit strategy applied to ${position.tokenMint}`);
  } catch (error) {
    logger.error('Failed to apply exit strategy:', error);
    throw error;
  }
}

/**
 * Add a trade job to the processing queue
 */
export async function addTradeJob(data: TradeJobData): Promise<Job<TradeJobData>> {
  try {
    const job = await tradeQueue.add('execute-trade', data, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
      removeOnComplete: 100,
      removeOnFail: 50,
    });

    logger.info(`Trade job added to queue: ${job.id}`);
    return job;
  } catch (error) {
    logger.error('Failed to add trade job:', error);
    throw error;
  }
}

/**
 * Validate trade parameters before execution
 */
export function validateTradeData(data: TradeJobData): string[] {
  const errors: string[] = [];

  if (!data.inputMint) {
    errors.push('Input mint is required');
  }

  if (!data.outputMint) {
    errors.push('Output mint is required');
  }

  if (!data.amount || data.amount <= 0) {
    errors.push('Amount must be positive');
  }

  if (!data.slippageBps || data.slippageBps < 0 || data.slippageBps > 10000) {
    errors.push('Slippage must be between 0 and 10000 basis points');
  }

  if (!data.walletAddress) {
    errors.push('Wallet address is required');
  }

  if (!data.quote) {
    errors.push('Quote is required');
  }

  return errors;
}
