/**
 * Configuration management routes
 * 
 * Provides endpoints for:
 * - Exit strategy configuration
 * - Trading parameters
 * - Risk management settings
 * - Alert preferences
 */

import express from 'express';
import Ajv from 'ajv';
import { ConfigCache } from '../services/redis';
import pino from 'pino';

// Import the bot rules schema
import botRulesSchema from '../rules/botRules.schema.json';

const router = express.Router();
const logger = pino({ name: 'config-routes' });

// Initialize AJV for schema validation
const ajv = new Ajv();
const validateBotRules = ajv.compile(botRulesSchema);

/**
 * Get current exit strategy configuration
 */
router.get('/exit-strategy', async (req, res) => {
  try {
    logger.info('Fetching exit strategy configuration');

    // Check cache first
    let config = await ConfigCache.getExitStrategy();
    
    if (!config) {
      // Return default configuration
      config = {
        stopLoss: {
          enabled: true,
          percentage: 15
        },
        trailingStopLoss: {
          enabled: true,
          percentage: 15
        },
        takeProfitLevels: [
          { thresholdPercent: 50, sellPercent: 15 },
          { thresholdPercent: 100, sellPercent: 15 },
          { thresholdPercent: 150, sellPercent: 15 },
          { thresholdPercent: 200, sellPercent: 15 }
        ],
        moonBag: {
          enabled: true,
          percentage: 25,
          targetPercent: 500
        },
        riskManagement: {
          maxPositionSize: 1000, // USD
          maxTotalExposure: 5000, // USD
          maxPriceImpact: 5 // Percentage
        }
      };
      
      // Cache the default config
      await ConfigCache.setExitStrategy(config);
    }

    res.json(config);
  } catch (error) {
    logger.error('Failed to get exit strategy:', error);
    res.status(500).json({
      error: 'Failed to get exit strategy',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Update exit strategy configuration
 */
router.post('/exit-strategy', async (req, res) => {
  try {
    const config = req.body;
    logger.info('Updating exit strategy configuration');

    // Validate configuration
    if (!config || typeof config !== 'object') {
      return res.status(400).json({
        error: 'Invalid configuration',
        message: 'Configuration must be a valid object'
      });
    }

    // Validate stop loss
    if (config.stopLoss) {
      if (typeof config.stopLoss.percentage !== 'number' || 
          config.stopLoss.percentage < 1 || 
          config.stopLoss.percentage > 50) {
        return res.status(400).json({
          error: 'Invalid stop loss percentage',
          message: 'Stop loss percentage must be between 1 and 50'
        });
      }
    }

    // Validate trailing stop loss
    if (config.trailingStopLoss) {
      if (typeof config.trailingStopLoss.percentage !== 'number' || 
          config.trailingStopLoss.percentage < 1 || 
          config.trailingStopLoss.percentage > 50) {
        return res.status(400).json({
          error: 'Invalid trailing stop loss percentage',
          message: 'Trailing stop loss percentage must be between 1 and 50'
        });
      }
    }

    // Validate take profit levels
    if (config.takeProfitLevels && Array.isArray(config.takeProfitLevels)) {
      for (const level of config.takeProfitLevels) {
        if (typeof level.thresholdPercent !== 'number' || level.thresholdPercent < 1) {
          return res.status(400).json({
            error: 'Invalid take profit threshold',
            message: 'Take profit thresholds must be positive numbers'
          });
        }
        if (typeof level.sellPercent !== 'number' || 
            level.sellPercent < 1 || 
            level.sellPercent > 100) {
          return res.status(400).json({
            error: 'Invalid take profit sell percentage',
            message: 'Take profit sell percentages must be between 1 and 100'
          });
        }
      }
    }

    // Cache the updated configuration
    await ConfigCache.setExitStrategy(config);

    res.json({
      message: 'Exit strategy updated successfully',
      config,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to update exit strategy:', error);
    res.status(500).json({
      error: 'Failed to update exit strategy',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get trading configuration
 */
router.get('/trading', async (req, res) => {
  try {
    logger.info('Fetching trading configuration');

    let config = await ConfigCache.getTradingConfig();
    
    if (!config) {
      // Return default trading configuration
      config = {
        slippage: {
          mode: 'dynamic', // 'dynamic' or 'static'
          staticValue: 50, // basis points
          dynamicRange: {
            min: 10,
            max: 500
          }
        },
        priorityFee: {
          enabled: true,
          amount: 1000 // microlamports
        },
        retries: {
          enabled: true,
          maxRetries: 3,
          backoffMs: 1000
        },
        limits: {
          maxPositionSizeUSD: 1000,
          maxTotalExposureUSD: 5000,
          maxPriceImpact: 5,
          minTradeSize: 0.01 // SOL
        }
      };
      
      await ConfigCache.setTradingConfig(config);
    }

    res.json(config);
  } catch (error) {
    logger.error('Failed to get trading config:', error);
    res.status(500).json({
      error: 'Failed to get trading config',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Update trading configuration
 */
router.post('/trading', async (req, res) => {
  try {
    const config = req.body;
    logger.info('Updating trading configuration');

    // Basic validation
    if (!config || typeof config !== 'object') {
      return res.status(400).json({
        error: 'Invalid configuration',
        message: 'Configuration must be a valid object'
      });
    }

    // Cache the updated configuration
    await ConfigCache.setTradingConfig(config);

    res.json({
      message: 'Trading configuration updated successfully',
      config,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to update trading config:', error);
    res.status(500).json({
      error: 'Failed to update trading config',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get bot rules configuration (using the schema)
 */
router.get('/bot-rules', async (req, res) => {
  try {
    logger.info('Fetching bot rules configuration');

    // Default bot rules configuration
    const defaultRules = {
      buyRules: {
        newPairDetection: {
          enabled: false,
          dexes: ["raydium", "orca"],
          buyAmountSOL: 0.1
        },
        priceVelocityTrigger: {
          enabled: true,
          priceChangePercent: 10,
          timeframeSeconds: 300,
          minVolumeSOL: 100
        }
      },
      sellRules: {
        takeProfit: {
          enabled: true,
          percentage: 50
        },
        partialTakeProfit: [
          { thresholdPercent: 50, sellPercent: 25 },
          { thresholdPercent: 100, sellPercent: 25 },
          { thresholdPercent: 200, sellPercent: 25 }
        ],
        stopLoss: {
          enabled: true,
          percentage: 15
        },
        trailingStopLoss: {
          enabled: true,
          percentage: 15
        },
        manualOverride: {
          enabled: true
        }
      },
      riskManagement: {
        slippageControl: {
          enabled: true,
          slippageBps: 50
        },
        transactionPriority: {
          enabled: true,
          priorityFee: 1000
        },
        transactionRetry: {
          enabled: true,
          maxRetries: 3
        },
        preTradeSafetyChecks: {
          minLiquidity: 10000,
          honeypotDetection: true,
          revokedAuthoritiesCheck: true
        }
      }
    };

    res.json(defaultRules);
  } catch (error) {
    logger.error('Failed to get bot rules:', error);
    res.status(500).json({
      error: 'Failed to get bot rules',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Update bot rules configuration with schema validation
 */
router.post('/bot-rules', async (req, res) => {
  try {
    const rules = req.body;
    logger.info('Updating bot rules configuration');

    // Validate against schema
    const isValid = validateBotRules(rules);
    if (!isValid) {
      return res.status(400).json({
        error: 'Invalid bot rules configuration',
        message: 'Configuration does not match required schema',
        errors: validateBotRules.errors
      });
    }

    // TODO: Save to database or cache
    logger.info('Bot rules validated and would be saved');

    res.json({
      message: 'Bot rules updated successfully',
      rules,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to update bot rules:', error);
    res.status(500).json({
      error: 'Failed to update bot rules',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Reset configuration to defaults
 */
router.post('/reset', async (req, res) => {
  try {
    const { type } = req.body;
    
    if (!type || !['exit-strategy', 'trading', 'bot-rules', 'all'].includes(type)) {
      return res.status(400).json({
        error: 'Invalid reset type',
        message: 'Type must be one of: exit-strategy, trading, bot-rules, all'
      });
    }

    logger.info(`Resetting configuration: ${type}`);

    if (type === 'all' || type === 'exit-strategy') {
      await ConfigCache.setExitStrategy(null);
    }
    
    if (type === 'all' || type === 'trading') {
      await ConfigCache.setTradingConfig(null);
    }

    res.json({
      message: `Configuration reset successfully: ${type}`,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to reset configuration:', error);
    res.status(500).json({
      error: 'Failed to reset configuration',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
