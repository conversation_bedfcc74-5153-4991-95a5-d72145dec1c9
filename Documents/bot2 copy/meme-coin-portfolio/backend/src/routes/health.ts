/**
 * Health check routes
 * 
 * Provides system health monitoring endpoints for:
 * - Basic service availability
 * - Database connectivity
 * - External service status
 * - System metrics
 */

import express from 'express';
import { getSolanaConnection, getWallet } from '../services/solanaClient';
import { getRedisClient } from '../services/redis';
import { jupiterClient } from '../services/jupiterClient';
import pino from 'pino';

const router = express.Router();
const logger = pino({ name: 'health-routes' });

/**
 * Basic health check endpoint
 * Returns 200 if service is running
 */
router.get('/', (req, res) => {
  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'meme-coin-portfolio-backend',
    version: '1.0.0'
  });
});

/**
 * Detailed health check with dependency status
 */
router.get('/detailed', async (req, res) => {
  const healthCheck = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'meme-coin-portfolio-backend',
    version: '1.0.0',
    dependencies: {
      redis: { status: 'unknown', latency: 0, error: null },
      solana: { status: 'unknown', latency: 0, error: null, balance: 0 },
      jupiter: { status: 'unknown', latency: 0, error: null },
      wallet: { status: 'unknown', configured: false, address: null }
    },
    environment: {
      nodeEnv: process.env.NODE_ENV || 'development',
      port: process.env.BACKEND_PORT || 4000,
      redisUrl: process.env.REDIS_URL ? 'configured' : 'not configured',
      rpcUrl: process.env.RPC_URL ? 'configured' : 'not configured'
    }
  };

  let overallStatus = 'healthy';

  // Check Redis connectivity
  try {
    const redisStart = Date.now();
    const redisClient = getRedisClient();
    await redisClient.ping();
    healthCheck.dependencies.redis = {
      status: 'healthy',
      latency: Date.now() - redisStart,
      error: null
    };
  } catch (error) {
    healthCheck.dependencies.redis = {
      status: 'unhealthy',
      latency: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    overallStatus = 'degraded';
  }

  // Check Solana connectivity
  try {
    const solanaStart = Date.now();
    const connection = getSolanaConnection();
    const slot = await connection.getSlot();
    const wallet = getWallet();
    
    let balance = 0;
    if (wallet) {
      balance = await connection.getBalance(wallet.publicKey);
    }

    healthCheck.dependencies.solana = {
      status: 'healthy',
      latency: Date.now() - solanaStart,
      error: null,
      balance: balance / 1e9 // Convert to SOL
    };
  } catch (error) {
    healthCheck.dependencies.solana = {
      status: 'unhealthy',
      latency: 0,
      error: error instanceof Error ? error.message : 'Unknown error',
      balance: 0
    };
    overallStatus = 'degraded';
  }

  // Check Jupiter API
  try {
    const jupiterStart = Date.now();
    await jupiterClient.getSupportedTokens();
    healthCheck.dependencies.jupiter = {
      status: 'healthy',
      latency: Date.now() - jupiterStart,
      error: null
    };
  } catch (error) {
    healthCheck.dependencies.jupiter = {
      status: 'unhealthy',
      latency: 0,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
    overallStatus = 'degraded';
  }

  // Check wallet configuration
  try {
    const wallet = getWallet();
    healthCheck.dependencies.wallet = {
      status: wallet ? 'configured' : 'not configured',
      configured: !!wallet,
      address: wallet?.publicKey.toString() || null
    };
  } catch (error) {
    healthCheck.dependencies.wallet = {
      status: 'error',
      configured: false,
      address: null
    };
  }

  healthCheck.status = overallStatus;

  // Return appropriate status code
  const statusCode = overallStatus === 'healthy' ? 200 : 
                    overallStatus === 'degraded' ? 200 : 503;

  res.status(statusCode).json(healthCheck);
});

/**
 * Readiness probe for Kubernetes/Docker
 */
router.get('/ready', async (req, res) => {
  try {
    // Check critical dependencies
    const redisClient = getRedisClient();
    await redisClient.ping();
    
    const connection = getSolanaConnection();
    await connection.getSlot();

    res.status(200).json({
      status: 'ready',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Readiness check failed:', error);
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Liveness probe for Kubernetes/Docker
 */
router.get('/live', (req, res) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

/**
 * System metrics endpoint
 */
router.get('/metrics', (req, res) => {
  const memUsage = process.memoryUsage();
  
  res.status(200).json({
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: {
      rss: Math.round(memUsage.rss / 1024 / 1024), // MB
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024), // MB
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      external: Math.round(memUsage.external / 1024 / 1024) // MB
    },
    cpu: {
      usage: process.cpuUsage()
    },
    environment: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    }
  });
});

export default router;
