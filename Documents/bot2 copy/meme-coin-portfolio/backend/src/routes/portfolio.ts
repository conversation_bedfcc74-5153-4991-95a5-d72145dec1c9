/**
 * Portfolio management routes
 *
 * Provides endpoints for:
 * - Portfolio overview and statistics
 * - Position tracking and management
 * - Performance analytics
 * - Asset allocation data
 */

import express from 'express';
import { PublicKey } from '@solana/web3.js';
import { getSolanaConnection, getWallet, getTokenBalance, getSolBalance } from '../services/solanaClient';
import { jupiterClient } from '../services/jupiterClient';
import { PortfolioCache } from '../services/redis';
import { tokenMetadataService } from '../services/tokenMetadata';
import pino from 'pino';

const router = express.Router();
const logger = pino({ name: 'portfolio-routes' });

/**
 * Get portfolio overview with current positions and P&L
 */
router.get('/', async (req, res) => {
  try {
    logger.info('Fetching portfolio overview');

    // Check cache first
    const cachedData = await PortfolioCache.getPortfolioData();
    if (cachedData) {
      logger.debug('Returning cached portfolio data');
      return res.json(cachedData);
    }

    const wallet = getWallet();
    if (!wallet) {
      return res.status(400).json({
        error: 'Wallet not configured',
        message: 'Please configure WALLET_PRIVATE_KEY in environment variables'
      });
    }

    // Get SOL balance
    const solBalance = await getSolBalance();

    // Get SOL price in USD (approximate)
    const solPriceUSD = 180; // You can integrate with a price API later
    const totalValueUSD = solBalance * solPriceUSD;

    // Get all token accounts for the wallet
    const connection = getSolanaConnection();
    const tokenAccounts = await connection.getParsedTokenAccountsByOwner(wallet.publicKey, {
      programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA')
    });

    // Process token positions
    const positions = [];
    let totalTokenValue = 0;

    // Collect all token mints first
    const tokenMints: string[] = [];
    const tokenAccountsData: Array<{
      mint: string;
      amount: number;
      account: any;
    }> = [];

    for (const tokenAccount of tokenAccounts.value) {
      const accountData = tokenAccount.account.data.parsed.info;
      const tokenAmount = accountData.tokenAmount.uiAmount;

      if (tokenAmount && tokenAmount > 0) {
        const tokenMint = accountData.mint;
        tokenMints.push(tokenMint);
        tokenAccountsData.push({
          mint: tokenMint,
          amount: tokenAmount,
          account: tokenAccount
        });
      }
    }

    // Fetch metadata for all tokens in batch
    const metadataMap = await tokenMetadataService.getBatchTokenMetadata(tokenMints);

    // Process each token with metadata
    for (const tokenData of tokenAccountsData) {
      const { mint: tokenMint, amount: tokenAmount } = tokenData;

      // Get token metadata
      const metadata = metadataMap.get(tokenMint);
      const tokenSymbol = metadata?.symbol || `${tokenMint.slice(0, 4)}...${tokenMint.slice(-4)}`;
      const tokenName = metadata?.name || `Token ${tokenMint.slice(0, 8)}...`;

      // Get token price
      let tokenPrice = 0;
      try {
        tokenPrice = await jupiterClient.getTokenPrice(tokenMint);
      } catch (error) {
        logger.warn(`Failed to get price for token ${tokenMint}:`, error);
      }

      const tokenValue = tokenAmount * tokenPrice;
      totalTokenValue += tokenValue;

      // Only include tokens with significant value or amount
      if (tokenValue > 0.01 || tokenAmount > 1000) {
        positions.push({
          id: tokenMint,
          tokenMint,
          tokenSymbol,
          tokenName,
          amount: tokenAmount,
          entryPrice: tokenPrice, // This would need to be tracked from trade history
          currentPrice: tokenPrice,
          value: tokenValue,
          logoURI: metadata?.logoURI,
          isMemeCoin: metadata ? tokenMetadataService.isMemeCoin(metadata) : false,
          pnl: {
            amount: 0, // Would need historical data to calculate
            percentage: 0
          },
          entryTime: new Date().toISOString(), // Would need to track actual entry time
          exitStrategy: {
            stopLoss: 15,
            trailingStop: 15,
            takeProfitLevels: [50, 100, 150, 200]
          }
        });
      }
    }

    const portfolioData = {
      totalValue: totalValueUSD + totalTokenValue,
      totalValueSOL: solBalance,
      pnl24h: {
        amount: 0, // TODO: Calculate from trade history
        percentage: 0
      },
      pnl7d: {
        amount: 0, // TODO: Calculate from trade history
        percentage: 0
      },
      pnlTotal: {
        amount: 0, // TODO: Calculate from trade history
        percentage: 0
      },
      activePositions: positions.length,
      totalInvested: totalValueUSD + totalTokenValue, // Current total as invested amount
      availableBalance: solBalance,
      exposure: {
        current: totalTokenValue,
        limit: 5000, // Configurable limit
        percentage: Math.min((totalTokenValue / 5000) * 100, 100)
      },
      positions,
      assetAllocation: (() => {
        const totalPortfolioValue = totalValueUSD + totalTokenValue;
        const allocation = [];

        // Add SOL allocation
        if (totalValueUSD > 0) {
          allocation.push({
            name: 'SOL',
            value: totalValueUSD,
            percentage: Math.round((totalValueUSD / totalPortfolioValue) * 100),
            color: '#9945FF'
          });
        }

        // Add token allocations
        positions.forEach((position, index) => {
          if (position.value > 0) {
            const colors = ['#FF6B35', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'];
            allocation.push({
              name: position.tokenSymbol,
              value: position.value,
              percentage: Math.round((position.value / totalPortfolioValue) * 100),
              color: colors[index % colors.length]
            });
          }
        });

        return allocation;
      })(),
      recentTrades: [] // TODO: Implement trade history tracking
    };

    // Cache the data
    await PortfolioCache.setPortfolioData(portfolioData);

    res.json(portfolioData);
  } catch (error) {
    logger.error('Failed to fetch portfolio:', error);
    res.status(500).json({
      error: 'Failed to fetch portfolio',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get detailed position information
 */
router.get('/positions/:tokenMint', async (req, res) => {
  try {
    const { tokenMint } = req.params;
    logger.info(`Fetching position details for token: ${tokenMint}`);

    const wallet = getWallet();
    if (!wallet) {
      return res.status(400).json({
        error: 'Wallet not configured'
      });
    }

    // Get token balance
    const balance = await getTokenBalance(tokenMint);

    // Get current price
    const currentPrice = await jupiterClient.getTokenPrice(tokenMint);

    // Mock position data
    const position = {
      tokenMint,
      balance,
      currentPrice,
      value: balance * currentPrice,
      entryPrice: currentPrice * 0.8, // Mock entry price
      pnl: {
        amount: balance * currentPrice * 0.2,
        percentage: 25
      },
      priceHistory: [
        { timestamp: Date.now() - 3600000, price: currentPrice * 0.95 },
        { timestamp: Date.now() - 1800000, price: currentPrice * 0.98 },
        { timestamp: Date.now(), price: currentPrice }
      ],
      exitStrategy: {
        stopLoss: 15,
        trailingStop: 15,
        takeProfitLevels: [50, 100, 150, 200],
        moonBag: 25
      }
    };

    res.json(position);
  } catch (error) {
    logger.error('Failed to fetch position:', error);
    res.status(500).json({
      error: 'Failed to fetch position',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get portfolio performance analytics
 */
router.get('/analytics', async (req, res) => {
  try {
    logger.info('Fetching portfolio analytics');

    // Mock analytics data
    const analytics = {
      performance: {
        totalReturn: 15.67,
        sharpeRatio: 1.25,
        maxDrawdown: -8.5,
        winRate: 65,
        avgWin: 12.5,
        avgLoss: -7.2
      },
      monthlyReturns: [
        { month: '2024-01', return: 5.2 },
        { month: '2024-02', return: -2.1 },
        { month: '2024-03', return: 8.7 },
        { month: '2024-04', return: 12.3 },
        { month: '2024-05', return: -3.8 },
        { month: '2024-06', return: 4.9 }
      ],
      topPerformers: [
        { symbol: 'BONK', return: 45.2 },
        { symbol: 'WIF', return: 32.1 },
        { symbol: 'PEPE', return: 28.7 }
      ],
      worstPerformers: [
        { symbol: 'DOGE', return: -15.3 },
        { symbol: 'SHIB', return: -8.9 },
        { symbol: 'WEN', return: -5.2 }
      ]
    };

    res.json(analytics);
  } catch (error) {
    logger.error('Failed to fetch analytics:', error);
    res.status(500).json({
      error: 'Failed to fetch analytics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Refresh portfolio data (force cache invalidation)
 */
router.post('/refresh', async (req, res) => {
  try {
    logger.info('Refreshing portfolio data');

    // Clear cache
    await PortfolioCache.clearAll();

    res.json({
      message: 'Portfolio data refreshed successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to refresh portfolio:', error);
    res.status(500).json({
      error: 'Failed to refresh portfolio',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
