/**
 * Trading routes
 *
 * Provides endpoints for:
 * - Executing buy/sell orders
 * - Getting price quotes
 * - Managing trading parameters
 * - Trade history and status
 */

import express from 'express';
import { jupiterClient } from '../services/jupiterClient';
import { getWallet, getSolBalance, getTokenBalance } from '../services/solanaClient';
import { addTradeJob } from '../jobs/tradeProcessor';
import { tokenDiscoveryService } from '../services/tokenDiscovery';
import { exitStrategyEngine } from '../services/exitStrategy';
import pino from 'pino';

const router = express.Router();
const logger = pino({ name: 'trade-routes' });

/**
 * Get price quote for a potential trade
 */
router.post('/quote', async (req, res) => {
  try {
    const { inputMint, outputMint, amount, slippageBps = 50 } = req.body;

    if (!inputMint || !outputMint || !amount) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'inputMint, outputMint, and amount are required'
      });
    }

    logger.info(`Getting quote: ${amount} ${inputMint} -> ${outputMint}`);

    const quote = await jupiterClient.getQuote(inputMint, outputMint, amount, slippageBps);

    // Calculate additional metrics
    const priceImpact = parseFloat(quote.priceImpactPct);
    const inputAmount = parseFloat(quote.inAmount);
    const outputAmount = parseFloat(quote.outAmount);
    const rate = outputAmount / inputAmount;

    const response = {
      ...quote,
      metrics: {
        rate,
        priceImpact,
        minimumReceived: parseFloat(quote.otherAmountThreshold),
        estimatedFee: 0.00025 // Estimated Solana transaction fee
      },
      warnings: []
    };

    // Add warnings for high impact trades
    if (priceImpact > 1) {
      response.warnings.push(`High price impact: ${priceImpact.toFixed(2)}%`);
    }
    if (priceImpact > 5) {
      response.warnings.push('Very high price impact - consider reducing trade size');
    }

    res.json(response);
  } catch (error) {
    logger.error('Failed to get quote:', error);
    res.status(500).json({
      error: 'Failed to get quote',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Execute a trade
 */
router.post('/execute', async (req, res) => {
  try {
    const {
      inputMint,
      outputMint,
      amount,
      slippageBps = 50,
      applyExitStrategy = true,
      maxPriceImpact = 5
    } = req.body;

    if (!inputMint || !outputMint || !amount) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'inputMint, outputMint, and amount are required'
      });
    }

    const wallet = getWallet();
    if (!wallet) {
      return res.status(400).json({
        error: 'Wallet not configured',
        message: 'Please configure WALLET_PRIVATE_KEY in environment variables'
      });
    }

    logger.info(`Executing trade: ${amount} ${inputMint} -> ${outputMint}`);

    // Get quote first to validate trade
    const quote = await jupiterClient.getQuote(inputMint, outputMint, amount, slippageBps);
    const priceImpact = parseFloat(quote.priceImpactPct);

    // Check price impact limits
    if (priceImpact > maxPriceImpact) {
      return res.status(400).json({
        error: 'Price impact too high',
        message: `Price impact ${priceImpact.toFixed(2)}% exceeds maximum ${maxPriceImpact}%`,
        priceImpact
      });
    }

    // Check balance
    const SOL_MINT = 'So11111111111111111111111111111111111111112';
    let hasBalance = false;

    if (inputMint === SOL_MINT) {
      const solBalance = await getSolBalance();
      hasBalance = solBalance >= (amount / 1e9); // Convert lamports to SOL
    } else {
      const tokenBalance = await getTokenBalance(inputMint);
      hasBalance = tokenBalance >= amount;
    }

    if (!hasBalance) {
      return res.status(400).json({
        error: 'Insufficient balance',
        message: 'Not enough tokens to execute this trade'
      });
    }

    // Add trade to job queue for processing
    const tradeJob = await addTradeJob({
      inputMint,
      outputMint,
      amount,
      slippageBps,
      quote,
      walletAddress: wallet.publicKey.toString(),
      applyExitStrategy,
      timestamp: new Date().toISOString()
    });

    res.json({
      message: 'Trade queued for execution',
      jobId: tradeJob.id,
      quote,
      estimatedOutput: quote.outAmount,
      priceImpact,
      status: 'pending'
    });

  } catch (error) {
    logger.error('Failed to execute trade:', error);
    res.status(500).json({
      error: 'Failed to execute trade',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get trade status
 */
router.get('/status/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;

    // TODO: Implement job status lookup from BullMQ
    // For now, return mock status
    const status = {
      jobId,
      status: 'completed',
      result: {
        signature: 'mock_transaction_signature',
        inputAmount: '1000000',
        outputAmount: '950000',
        actualSlippage: 0.5,
        fee: 0.00025
      },
      timestamp: new Date().toISOString()
    };

    res.json(status);
  } catch (error) {
    logger.error('Failed to get trade status:', error);
    res.status(500).json({
      error: 'Failed to get trade status',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get trade history
 */
router.get('/history', async (req, res) => {
  try {
    const { limit = 50, offset = 0 } = req.query;

    // Mock trade history
    const trades = [
      {
        id: '1',
        type: 'buy',
        inputMint: 'So11111111111111111111111111111111111111112',
        outputMint: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
        inputSymbol: 'SOL',
        outputSymbol: 'BONK',
        inputAmount: 0.1,
        outputAmount: 1000000,
        price: 0.0000001,
        slippage: 0.3,
        priceImpact: 0.15,
        signature: 'mock_signature_1',
        status: 'completed',
        timestamp: new Date(Date.now() - 86400000).toISOString()
      },
      {
        id: '2',
        type: 'sell',
        inputMint: 'WENWENvqqNya429ubCdR81ZmD69brwQaaBYY6p3LCpk',
        outputMint: 'So11111111111111111111111111111111111111112',
        inputSymbol: 'WEN',
        outputSymbol: 'SOL',
        inputAmount: 500000,
        outputAmount: 0.05,
        price: 0.0000001,
        slippage: 0.8,
        priceImpact: 0.25,
        signature: 'mock_signature_2',
        status: 'completed',
        timestamp: new Date(Date.now() - 172800000).toISOString()
      }
    ];

    const paginatedTrades = trades.slice(Number(offset), Number(offset) + Number(limit));

    res.json({
      trades: paginatedTrades,
      total: trades.length,
      limit: Number(limit),
      offset: Number(offset)
    });
  } catch (error) {
    logger.error('Failed to get trade history:', error);
    res.status(500).json({
      error: 'Failed to get trade history',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Cancel a pending trade
 */
router.delete('/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;

    // TODO: Implement job cancellation in BullMQ
    logger.info(`Cancelling trade job: ${jobId}`);

    res.json({
      message: 'Trade cancelled successfully',
      jobId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to cancel trade:', error);
    res.status(500).json({
      error: 'Failed to cancel trade',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get supported tokens for trading
 */
router.get('/tokens', async (req, res) => {
  try {
    const tokens = await jupiterClient.getSupportedTokens();

    // Filter to popular meme coins for the frontend
    const memeCoins = tokens.filter(token =>
      ['BONK', 'WIF', 'PEPE', 'DOGE', 'SHIB', 'WEN', 'MYRO'].includes(token.symbol)
    );

    res.json({
      tokens: memeCoins,
      total: memeCoins.length
    });
  } catch (error) {
    logger.error('Failed to get supported tokens:', error);
    res.status(500).json({
      error: 'Failed to get supported tokens',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Search tokens
 */
router.get('/tokens/search', async (req, res) => {
  try {
    const {
      query,
      minPrice,
      maxPrice,
      minMarketCap,
      maxMarketCap,
      minVolume,
      maxVolume,
      minLiquidity,
      maxLiquidity,
      verified,
      memeCoinsOnly,
      limit = 20,
      offset = 0
    } = req.query;

    const filters = {
      query: query as string,
      minPrice: minPrice ? parseFloat(minPrice as string) : undefined,
      maxPrice: maxPrice ? parseFloat(maxPrice as string) : undefined,
      minMarketCap: minMarketCap ? parseFloat(minMarketCap as string) : undefined,
      maxMarketCap: maxMarketCap ? parseFloat(maxMarketCap as string) : undefined,
      minVolume: minVolume ? parseFloat(minVolume as string) : undefined,
      maxVolume: maxVolume ? parseFloat(maxVolume as string) : undefined,
      minLiquidity: minLiquidity ? parseFloat(minLiquidity as string) : undefined,
      maxLiquidity: maxLiquidity ? parseFloat(maxLiquidity as string) : undefined,
      verified: verified === 'true' ? true : verified === 'false' ? false : undefined,
      memeCoinsOnly: memeCoinsOnly === 'true',
      limit: parseInt(limit as string),
      offset: parseInt(offset as string)
    };

    const tokens = await tokenDiscoveryService.searchTokens(filters);

    res.json({
      tokens,
      total: tokens.length,
      filters
    });
  } catch (error) {
    logger.error('Failed to search tokens:', error);
    res.status(500).json({
      error: 'Failed to search tokens',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get token suggestions for autocomplete
 */
router.get('/tokens/suggestions', async (req, res) => {
  try {
    const { query, limit = 10 } = req.query;

    if (!query) {
      return res.json([]);
    }

    const suggestions = await tokenDiscoveryService.getTokenSuggestions(
      query as string,
      parseInt(limit as string)
    );

    res.json(suggestions);
  } catch (error) {
    logger.error('Failed to get token suggestions:', error);
    res.status(500).json({
      error: 'Failed to get token suggestions',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get trending meme coins
 */
router.get('/tokens/trending', async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    const trendingTokens = await tokenDiscoveryService.getTrendingMemeCoins(
      parseInt(limit as string)
    );

    res.json(trendingTokens);
  } catch (error) {
    logger.error('Failed to get trending tokens:', error);
    res.status(500).json({
      error: 'Failed to get trending tokens',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get popular tokens by volume
 */
router.get('/tokens/popular', async (req, res) => {
  try {
    const { limit = 20 } = req.query;

    const popularTokens = await tokenDiscoveryService.getPopularTokens(
      parseInt(limit as string)
    );

    res.json(popularTokens);
  } catch (error) {
    logger.error('Failed to get popular tokens:', error);
    res.status(500).json({
      error: 'Failed to get popular tokens',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get token details by address
 */
router.get('/tokens/:address', async (req, res) => {
  try {
    const { address } = req.params;

    const token = await tokenDiscoveryService.getTokenByAddress(address);

    if (!token) {
      return res.status(404).json({
        error: 'Token not found',
        message: `Token with address ${address} not found`
      });
    }

    res.json(token);
  } catch (error) {
    logger.error('Failed to get token details:', error);
    res.status(500).json({
      error: 'Failed to get token details',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get all monitored positions with exit strategies
 */
router.get('/positions', async (req, res) => {
  try {
    const positions = exitStrategyEngine.getPositions();
    res.json(positions);
  } catch (error) {
    logger.error('Failed to get positions:', error);
    res.status(500).json({
      error: 'Failed to get positions',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get specific position details
 */
router.get('/positions/:tokenMint', async (req, res) => {
  try {
    const { tokenMint } = req.params;
    const position = exitStrategyEngine.getPosition(tokenMint);

    if (!position) {
      return res.status(404).json({
        error: 'Position not found',
        message: `Position for token ${tokenMint} not found`
      });
    }

    res.json(position);
  } catch (error) {
    logger.error('Failed to get position:', error);
    res.status(500).json({
      error: 'Failed to get position',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Update exit strategy for a position
 */
router.put('/positions/:tokenMint/exit-strategy', async (req, res) => {
  try {
    const { tokenMint } = req.params;
    const exitStrategy = req.body;

    await exitStrategyEngine.updateExitStrategy(tokenMint, exitStrategy);

    res.json({
      success: true,
      message: 'Exit strategy updated successfully'
    });
  } catch (error) {
    logger.error('Failed to update exit strategy:', error);
    res.status(500).json({
      error: 'Failed to update exit strategy',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Manually trigger exit for a position
 */
router.post('/positions/:tokenMint/exit', async (req, res) => {
  try {
    const { tokenMint } = req.params;
    const { sellPercentage = 100, reason = 'Manual exit' } = req.body;

    const position = exitStrategyEngine.getPosition(tokenMint);
    if (!position) {
      return res.status(404).json({
        error: 'Position not found',
        message: `Position for token ${tokenMint} not found`
      });
    }

    // Calculate sell amount
    const sellAmount = (position.amount * sellPercentage) / 100;

    // Get quote for selling
    const quote = await jupiterClient.getQuote(
      tokenMint,
      'So11111111111111111111111111111111111111112', // SOL
      sellAmount,
      100 // 1% slippage
    );

    // Add trade job
    const job = await addTradeJob({
      inputMint: tokenMint,
      outputMint: 'So11111111111111111111111111111111111111112',
      amount: sellAmount,
      slippageBps: 100,
      quote,
      applyExitStrategy: false
    });

    res.json({
      success: true,
      jobId: job.id,
      message: `Manual exit initiated for ${sellPercentage}% of ${position.symbol}`,
      sellAmount,
      expectedOutput: quote.outAmount
    });
  } catch (error) {
    logger.error('Failed to execute manual exit:', error);
    res.status(500).json({
      error: 'Failed to execute manual exit',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
