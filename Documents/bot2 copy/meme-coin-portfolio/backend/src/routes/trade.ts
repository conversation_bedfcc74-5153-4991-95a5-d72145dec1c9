/**
 * Trading routes
 * 
 * Provides endpoints for:
 * - Executing buy/sell orders
 * - Getting price quotes
 * - Managing trading parameters
 * - Trade history and status
 */

import express from 'express';
import { jupiterClient } from '../services/jupiterClient';
import { getWallet, getSolBalance, getTokenBalance } from '../services/solanaClient';
import { addTradeJob } from '../jobs/tradeProcessor';
import pino from 'pino';

const router = express.Router();
const logger = pino({ name: 'trade-routes' });

/**
 * Get price quote for a potential trade
 */
router.post('/quote', async (req, res) => {
  try {
    const { inputMint, outputMint, amount, slippageBps = 50 } = req.body;

    if (!inputMint || !outputMint || !amount) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'inputMint, outputMint, and amount are required'
      });
    }

    logger.info(`Getting quote: ${amount} ${inputMint} -> ${outputMint}`);

    const quote = await jupiterClient.getQuote(inputMint, outputMint, amount, slippageBps);

    // Calculate additional metrics
    const priceImpact = parseFloat(quote.priceImpactPct);
    const inputAmount = parseFloat(quote.inAmount);
    const outputAmount = parseFloat(quote.outAmount);
    const rate = outputAmount / inputAmount;

    const response = {
      ...quote,
      metrics: {
        rate,
        priceImpact,
        minimumReceived: parseFloat(quote.otherAmountThreshold),
        estimatedFee: 0.00025 // Estimated Solana transaction fee
      },
      warnings: []
    };

    // Add warnings for high impact trades
    if (priceImpact > 1) {
      response.warnings.push(`High price impact: ${priceImpact.toFixed(2)}%`);
    }
    if (priceImpact > 5) {
      response.warnings.push('Very high price impact - consider reducing trade size');
    }

    res.json(response);
  } catch (error) {
    logger.error('Failed to get quote:', error);
    res.status(500).json({
      error: 'Failed to get quote',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Execute a trade
 */
router.post('/execute', async (req, res) => {
  try {
    const { 
      inputMint, 
      outputMint, 
      amount, 
      slippageBps = 50,
      applyExitStrategy = true,
      maxPriceImpact = 5
    } = req.body;

    if (!inputMint || !outputMint || !amount) {
      return res.status(400).json({
        error: 'Missing required parameters',
        message: 'inputMint, outputMint, and amount are required'
      });
    }

    const wallet = getWallet();
    if (!wallet) {
      return res.status(400).json({
        error: 'Wallet not configured',
        message: 'Please configure WALLET_PRIVATE_KEY in environment variables'
      });
    }

    logger.info(`Executing trade: ${amount} ${inputMint} -> ${outputMint}`);

    // Get quote first to validate trade
    const quote = await jupiterClient.getQuote(inputMint, outputMint, amount, slippageBps);
    const priceImpact = parseFloat(quote.priceImpactPct);

    // Check price impact limits
    if (priceImpact > maxPriceImpact) {
      return res.status(400).json({
        error: 'Price impact too high',
        message: `Price impact ${priceImpact.toFixed(2)}% exceeds maximum ${maxPriceImpact}%`,
        priceImpact
      });
    }

    // Check balance
    const SOL_MINT = 'So11111111111111111111111111111111111111112';
    let hasBalance = false;

    if (inputMint === SOL_MINT) {
      const solBalance = await getSolBalance();
      hasBalance = solBalance >= (amount / 1e9); // Convert lamports to SOL
    } else {
      const tokenBalance = await getTokenBalance(inputMint);
      hasBalance = tokenBalance >= amount;
    }

    if (!hasBalance) {
      return res.status(400).json({
        error: 'Insufficient balance',
        message: 'Not enough tokens to execute this trade'
      });
    }

    // Add trade to job queue for processing
    const tradeJob = await addTradeJob({
      inputMint,
      outputMint,
      amount,
      slippageBps,
      quote,
      walletAddress: wallet.publicKey.toString(),
      applyExitStrategy,
      timestamp: new Date().toISOString()
    });

    res.json({
      message: 'Trade queued for execution',
      jobId: tradeJob.id,
      quote,
      estimatedOutput: quote.outAmount,
      priceImpact,
      status: 'pending'
    });

  } catch (error) {
    logger.error('Failed to execute trade:', error);
    res.status(500).json({
      error: 'Failed to execute trade',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get trade status
 */
router.get('/status/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;
    
    // TODO: Implement job status lookup from BullMQ
    // For now, return mock status
    const status = {
      jobId,
      status: 'completed',
      result: {
        signature: 'mock_transaction_signature',
        inputAmount: '1000000',
        outputAmount: '950000',
        actualSlippage: 0.5,
        fee: 0.00025
      },
      timestamp: new Date().toISOString()
    };

    res.json(status);
  } catch (error) {
    logger.error('Failed to get trade status:', error);
    res.status(500).json({
      error: 'Failed to get trade status',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get trade history
 */
router.get('/history', async (req, res) => {
  try {
    const { limit = 50, offset = 0 } = req.query;

    // Mock trade history
    const trades = [
      {
        id: '1',
        type: 'buy',
        inputMint: 'So11111111111111111111111111111111111111112',
        outputMint: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
        inputSymbol: 'SOL',
        outputSymbol: 'BONK',
        inputAmount: 0.1,
        outputAmount: 1000000,
        price: 0.0000001,
        slippage: 0.3,
        priceImpact: 0.15,
        signature: 'mock_signature_1',
        status: 'completed',
        timestamp: new Date(Date.now() - 86400000).toISOString()
      },
      {
        id: '2',
        type: 'sell',
        inputMint: 'WENWENvqqNya429ubCdR81ZmD69brwQaaBYY6p3LCpk',
        outputMint: 'So11111111111111111111111111111111111111112',
        inputSymbol: 'WEN',
        outputSymbol: 'SOL',
        inputAmount: 500000,
        outputAmount: 0.05,
        price: 0.0000001,
        slippage: 0.8,
        priceImpact: 0.25,
        signature: 'mock_signature_2',
        status: 'completed',
        timestamp: new Date(Date.now() - 172800000).toISOString()
      }
    ];

    const paginatedTrades = trades.slice(Number(offset), Number(offset) + Number(limit));

    res.json({
      trades: paginatedTrades,
      total: trades.length,
      limit: Number(limit),
      offset: Number(offset)
    });
  } catch (error) {
    logger.error('Failed to get trade history:', error);
    res.status(500).json({
      error: 'Failed to get trade history',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Cancel a pending trade
 */
router.delete('/:jobId', async (req, res) => {
  try {
    const { jobId } = req.params;
    
    // TODO: Implement job cancellation in BullMQ
    logger.info(`Cancelling trade job: ${jobId}`);

    res.json({
      message: 'Trade cancelled successfully',
      jobId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to cancel trade:', error);
    res.status(500).json({
      error: 'Failed to cancel trade',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * Get supported tokens for trading
 */
router.get('/tokens', async (req, res) => {
  try {
    const tokens = await jupiterClient.getSupportedTokens();
    
    // Filter to popular meme coins for the frontend
    const memeCoins = tokens.filter(token => 
      ['BONK', 'WIF', 'PEPE', 'DOGE', 'SHIB', 'WEN', 'MYRO'].includes(token.symbol)
    );

    res.json({
      tokens: memeCoins,
      total: memeCoins.length
    });
  } catch (error) {
    logger.error('Failed to get supported tokens:', error);
    res.status(500).json({
      error: 'Failed to get supported tokens',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
