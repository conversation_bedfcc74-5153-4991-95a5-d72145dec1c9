{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Solana Trading Bot Rules", "type": "object", "properties": {"buyRules": {"type": "object", "properties": {"newPairDetection": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "dexes": {"type": "array", "items": {"type": "string"}}, "buyAmountSOL": {"type": "number"}}, "required": ["enabled", "dexes", "buyAmountSOL"]}, "priceVelocityTrigger": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "priceChangePercent": {"type": "number"}, "timeframeSeconds": {"type": "number"}, "minVolumeSOL": {"type": "number"}}, "required": ["enabled", "priceChangePercent", "timeframeSeconds"]}}}, "sellRules": {"type": "object", "properties": {"takeProfit": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "percentage": {"type": "number"}}, "required": ["enabled", "percentage"]}, "partialTakeProfit": {"type": "array", "items": {"type": "object", "properties": {"thresholdPercent": {"type": "number"}, "sellPercent": {"type": "number"}}, "required": ["thresholdPercent", "sellPercent"]}}, "stopLoss": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "percentage": {"type": "number"}}, "required": ["enabled", "percentage"]}, "trailingStopLoss": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "percentage": {"type": "number"}}, "required": ["enabled", "percentage"]}, "manualOverride": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"]}}}, "riskManagement": {"type": "object", "properties": {"slippageControl": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "slippageBps": {"type": "integer"}}, "required": ["enabled", "slippageBps"]}, "transactionPriority": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "priorityFee": {"type": "number"}}, "required": ["enabled", "priorityFee"]}, "transactionRetry": {"type": "object", "properties": {"enabled": {"type": "boolean"}, "maxRetries": {"type": "integer"}}, "required": ["enabled", "maxRetries"]}, "preTradeSafetyChecks": {"type": "object", "properties": {"minLiquidity": {"type": "number"}, "honeypotDetection": {"type": "boolean"}, "revokedAuthoritiesCheck": {"type": "boolean"}}, "required": ["minLiquidity", "honeypotDetection", "revokedAuthoritiesCheck"]}}}}, "required": ["buyRules", "sellRules", "riskManagement"]}