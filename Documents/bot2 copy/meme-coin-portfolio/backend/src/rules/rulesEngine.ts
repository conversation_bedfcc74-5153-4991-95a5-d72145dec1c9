/**
 * Trading rules engine
 * 
 * Implements the core trading logic based on the bot rules schema:
 * - Buy rule evaluation
 * - Sell rule evaluation
 * - Risk management checks
 * - Exit strategy execution
 */

import Ajv from 'ajv';
import { jupiterClient } from '../services/jupiterClient';
import { getTokenBalance, getSolBalance } from '../services/solanaClient';
import pino from 'pino';

// Import the bot rules schema
import botRulesSchema from './botRules.schema.json';

const logger = pino({ name: 'rules-engine' });

// Initialize AJV for schema validation
const ajv = new Ajv();
const validateBotRules = ajv.compile(botRulesSchema);

export interface BotRules {
  buyRules: {
    newPairDetection: {
      enabled: boolean;
      dexes: string[];
      buyAmountSOL: number;
    };
    priceVelocityTrigger: {
      enabled: boolean;
      priceChangePercent: number;
      timeframeSeconds: number;
      minVolumeSOL?: number;
    };
  };
  sellRules: {
    takeProfit: {
      enabled: boolean;
      percentage: number;
    };
    partialTakeProfit: Array<{
      thresholdPercent: number;
      sellPercent: number;
    }>;
    stopLoss: {
      enabled: boolean;
      percentage: number;
    };
    trailingStopLoss: {
      enabled: boolean;
      percentage: number;
    };
    manualOverride: {
      enabled: boolean;
    };
  };
  riskManagement: {
    slippageControl: {
      enabled: boolean;
      slippageBps: number;
    };
    transactionPriority: {
      enabled: boolean;
      priorityFee: number;
    };
    transactionRetry: {
      enabled: boolean;
      maxRetries: number;
    };
    preTradeSafetyChecks: {
      minLiquidity: number;
      honeypotDetection: boolean;
      revokedAuthoritiesCheck: boolean;
    };
  };
}

export interface MarketData {
  tokenMint: string;
  price: number;
  volume24h: number;
  priceChange24h: number;
  liquidity: number;
  timestamp: Date;
}

export interface Position {
  tokenMint: string;
  amount: number;
  entryPrice: number;
  currentPrice: number;
  entryTime: Date;
  highestPrice: number;
}

/**
 * Rules engine class for evaluating trading conditions
 */
export class RulesEngine {
  private rules: BotRules;

  constructor(rules: BotRules) {
    this.validateRules(rules);
    this.rules = rules;
  }

  /**
   * Validate rules against schema
   */
  private validateRules(rules: BotRules): void {
    const isValid = validateBotRules(rules);
    if (!isValid) {
      throw new Error(`Invalid bot rules: ${JSON.stringify(validateBotRules.errors)}`);
    }
  }

  /**
   * Evaluate buy conditions for a token
   */
  async evaluateBuyConditions(marketData: MarketData): Promise<{
    shouldBuy: boolean;
    reason: string;
    amount?: number;
  }> {
    try {
      // Check if any buy rules are enabled
      if (!this.rules.buyRules.newPairDetection.enabled && 
          !this.rules.buyRules.priceVelocityTrigger.enabled) {
        return { shouldBuy: false, reason: 'No buy rules enabled' };
      }

      // Evaluate price velocity trigger
      if (this.rules.buyRules.priceVelocityTrigger.enabled) {
        const velocityResult = this.evaluatePriceVelocity(marketData);
        if (velocityResult.shouldBuy) {
          return velocityResult;
        }
      }

      // Evaluate new pair detection
      if (this.rules.buyRules.newPairDetection.enabled) {
        const newPairResult = await this.evaluateNewPairDetection(marketData);
        if (newPairResult.shouldBuy) {
          return newPairResult;
        }
      }

      return { shouldBuy: false, reason: 'No buy conditions met' };
    } catch (error) {
      logger.error('Error evaluating buy conditions:', error);
      return { shouldBuy: false, reason: 'Evaluation error' };
    }
  }

  /**
   * Evaluate price velocity trigger
   */
  private evaluatePriceVelocity(marketData: MarketData): {
    shouldBuy: boolean;
    reason: string;
    amount?: number;
  } {
    const { priceChangePercent, minVolumeSOL } = this.rules.buyRules.priceVelocityTrigger;

    // Check price change threshold
    if (Math.abs(marketData.priceChange24h) < priceChangePercent) {
      return { 
        shouldBuy: false, 
        reason: `Price change ${marketData.priceChange24h}% below threshold ${priceChangePercent}%` 
      };
    }

    // Check minimum volume if specified
    if (minVolumeSOL && marketData.volume24h < minVolumeSOL) {
      return { 
        shouldBuy: false, 
        reason: `Volume ${marketData.volume24h} SOL below minimum ${minVolumeSOL} SOL` 
      };
    }

    return {
      shouldBuy: true,
      reason: `Price velocity trigger: ${marketData.priceChange24h}% change`,
      amount: this.rules.buyRules.newPairDetection.buyAmountSOL // Use default buy amount
    };
  }

  /**
   * Evaluate new pair detection
   */
  private async evaluateNewPairDetection(marketData: MarketData): Promise<{
    shouldBuy: boolean;
    reason: string;
    amount?: number;
  }> {
    // TODO: Implement new pair detection logic
    // This would involve checking if the token is newly listed on supported DEXes
    
    return {
      shouldBuy: false,
      reason: 'New pair detection not implemented'
    };
  }

  /**
   * Evaluate sell conditions for a position
   */
  async evaluateSellConditions(position: Position): Promise<{
    shouldSell: boolean;
    reason: string;
    sellAmount?: number;
    sellType: 'stop_loss' | 'take_profit' | 'trailing_stop' | 'manual';
  }> {
    try {
      // Check stop loss
      if (this.rules.sellRules.stopLoss.enabled) {
        const stopLossResult = this.evaluateStopLoss(position);
        if (stopLossResult.shouldSell) {
          return stopLossResult;
        }
      }

      // Check trailing stop loss
      if (this.rules.sellRules.trailingStopLoss.enabled) {
        const trailingStopResult = this.evaluateTrailingStopLoss(position);
        if (trailingStopResult.shouldSell) {
          return trailingStopResult;
        }
      }

      // Check take profit levels
      if (this.rules.sellRules.takeProfit.enabled) {
        const takeProfitResult = this.evaluateTakeProfit(position);
        if (takeProfitResult.shouldSell) {
          return takeProfitResult;
        }
      }

      // Check partial take profit levels
      const partialTakeProfitResult = this.evaluatePartialTakeProfit(position);
      if (partialTakeProfitResult.shouldSell) {
        return partialTakeProfitResult;
      }

      return { 
        shouldSell: false, 
        reason: 'No sell conditions met',
        sellType: 'manual'
      };
    } catch (error) {
      logger.error('Error evaluating sell conditions:', error);
      return { 
        shouldSell: false, 
        reason: 'Evaluation error',
        sellType: 'manual'
      };
    }
  }

  /**
   * Evaluate stop loss condition
   */
  private evaluateStopLoss(position: Position): {
    shouldSell: boolean;
    reason: string;
    sellAmount?: number;
    sellType: 'stop_loss' | 'take_profit' | 'trailing_stop' | 'manual';
  } {
    const stopLossPrice = position.entryPrice * (1 - this.rules.sellRules.stopLoss.percentage / 100);
    
    if (position.currentPrice <= stopLossPrice) {
      return {
        shouldSell: true,
        reason: `Stop loss triggered: ${position.currentPrice} <= ${stopLossPrice}`,
        sellAmount: position.amount, // Sell entire position
        sellType: 'stop_loss'
      };
    }

    return { shouldSell: false, reason: 'Stop loss not triggered', sellType: 'stop_loss' };
  }

  /**
   * Evaluate trailing stop loss condition
   */
  private evaluateTrailingStopLoss(position: Position): {
    shouldSell: boolean;
    reason: string;
    sellAmount?: number;
    sellType: 'stop_loss' | 'take_profit' | 'trailing_stop' | 'manual';
  } {
    const trailingStopPrice = position.highestPrice * (1 - this.rules.sellRules.trailingStopLoss.percentage / 100);
    
    if (position.currentPrice <= trailingStopPrice) {
      return {
        shouldSell: true,
        reason: `Trailing stop triggered: ${position.currentPrice} <= ${trailingStopPrice}`,
        sellAmount: position.amount, // Sell entire position
        sellType: 'trailing_stop'
      };
    }

    return { shouldSell: false, reason: 'Trailing stop not triggered', sellType: 'trailing_stop' };
  }

  /**
   * Evaluate take profit condition
   */
  private evaluateTakeProfit(position: Position): {
    shouldSell: boolean;
    reason: string;
    sellAmount?: number;
    sellType: 'stop_loss' | 'take_profit' | 'trailing_stop' | 'manual';
  } {
    const takeProfitPrice = position.entryPrice * (1 + this.rules.sellRules.takeProfit.percentage / 100);
    
    if (position.currentPrice >= takeProfitPrice) {
      return {
        shouldSell: true,
        reason: `Take profit triggered: ${position.currentPrice} >= ${takeProfitPrice}`,
        sellAmount: position.amount, // Sell entire position
        sellType: 'take_profit'
      };
    }

    return { shouldSell: false, reason: 'Take profit not triggered', sellType: 'take_profit' };
  }

  /**
   * Evaluate partial take profit conditions
   */
  private evaluatePartialTakeProfit(position: Position): {
    shouldSell: boolean;
    reason: string;
    sellAmount?: number;
    sellType: 'stop_loss' | 'take_profit' | 'trailing_stop' | 'manual';
  } {
    const currentProfitPercent = ((position.currentPrice - position.entryPrice) / position.entryPrice) * 100;

    for (const level of this.rules.sellRules.partialTakeProfit) {
      if (currentProfitPercent >= level.thresholdPercent) {
        const sellAmount = position.amount * (level.sellPercent / 100);
        
        return {
          shouldSell: true,
          reason: `Partial take profit triggered: ${currentProfitPercent}% >= ${level.thresholdPercent}%`,
          sellAmount,
          sellType: 'take_profit'
        };
      }
    }

    return { shouldSell: false, reason: 'No partial take profit levels reached', sellType: 'take_profit' };
  }

  /**
   * Perform pre-trade safety checks
   */
  async performSafetyChecks(tokenMint: string): Promise<{
    passed: boolean;
    issues: string[];
  }> {
    const issues: string[] = [];

    try {
      // Check minimum liquidity
      if (this.rules.riskManagement.preTradeSafetyChecks.minLiquidity > 0) {
        // TODO: Implement liquidity check
        // For now, assume liquidity is sufficient
      }

      // Check honeypot detection
      if (this.rules.riskManagement.preTradeSafetyChecks.honeypotDetection) {
        // TODO: Implement honeypot detection
        // For now, assume token is safe
      }

      // Check revoked authorities
      if (this.rules.riskManagement.preTradeSafetyChecks.revokedAuthoritiesCheck) {
        // TODO: Implement authority check
        // For now, assume authorities are properly revoked
      }

      return { passed: issues.length === 0, issues };
    } catch (error) {
      logger.error('Error performing safety checks:', error);
      issues.push('Safety check error');
      return { passed: false, issues };
    }
  }

  /**
   * Get recommended slippage for a trade
   */
  getRecommendedSlippage(): number {
    if (this.rules.riskManagement.slippageControl.enabled) {
      return this.rules.riskManagement.slippageControl.slippageBps;
    }
    return 50; // Default 0.5%
  }

  /**
   * Get maximum retry count for transactions
   */
  getMaxRetries(): number {
    if (this.rules.riskManagement.transactionRetry.enabled) {
      return this.rules.riskManagement.transactionRetry.maxRetries;
    }
    return 3; // Default
  }
}
