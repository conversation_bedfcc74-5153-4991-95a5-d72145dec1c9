/**
 * Exit Strategy Engine
 * 
 * Implements automated trading rules and exit strategies:
 * - Stop-loss orders
 * - Take-profit levels
 * - Trailing stops
 * - Moon bag management
 * - Risk management
 */

import pino from 'pino';
import { jupiterClient } from './jupiterClient';
import { addTradeJob } from '../jobs/tradeProcessor';
import { PortfolioCache } from './redis';

const logger = pino({ name: 'exit-strategy' });

export interface ExitStrategy {
  stopLoss?: number; // Percentage loss to trigger stop-loss
  trailingStop?: number; // Percentage for trailing stop
  takeProfitLevels?: number[]; // Array of profit percentages to take profit
  moonBag?: number; // Percentage to keep as moon bag
  maxSlippage?: number; // Maximum slippage for exit trades
  enabled?: boolean; // Whether strategy is active
}

export interface Position {
  tokenMint: string;
  symbol: string;
  entryPrice: number;
  currentPrice: number;
  amount: number;
  value: number;
  entryTimestamp: string;
  entrySignature: string;
  exitStrategy: ExitStrategy;
  highestPrice?: number; // For trailing stop calculation
  profitTaken?: number; // Amount already taken as profit
  moonBagAmount?: number; // Amount reserved as moon bag
}

export interface ExitTrigger {
  type: 'stop-loss' | 'take-profit' | 'trailing-stop' | 'manual';
  reason: string;
  targetPrice: number;
  sellPercentage: number; // Percentage of position to sell
}

class ExitStrategyEngine {
  private positions: Map<string, Position> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;
  private readonly MONITORING_INTERVAL_MS = 10000; // 10 seconds

  constructor() {
    this.startMonitoring();
  }

  /**
   * Add a position to monitor
   */
  async addPosition(position: Position): Promise<void> {
    try {
      logger.info(`Adding position to monitor: ${position.symbol} (${position.tokenMint})`);
      
      // Set initial highest price for trailing stop
      position.highestPrice = position.entryPrice;
      position.profitTaken = 0;
      position.moonBagAmount = 0;

      this.positions.set(position.tokenMint, position);
      
      // Cache position for persistence
      await PortfolioCache.setPosition(position.tokenMint, position);
      
      logger.info(`Position added: ${position.symbol} - Entry: $${position.entryPrice}`);
    } catch (error) {
      logger.error('Failed to add position:', error);
      throw error;
    }
  }

  /**
   * Remove a position from monitoring
   */
  async removePosition(tokenMint: string): Promise<void> {
    try {
      this.positions.delete(tokenMint);
      await PortfolioCache.deletePosition(tokenMint);
      logger.info(`Position removed from monitoring: ${tokenMint}`);
    } catch (error) {
      logger.error('Failed to remove position:', error);
      throw error;
    }
  }

  /**
   * Update position with current price
   */
  async updatePosition(tokenMint: string, currentPrice: number): Promise<void> {
    const position = this.positions.get(tokenMint);
    if (!position) return;

    position.currentPrice = currentPrice;
    position.value = position.amount * currentPrice;

    // Update highest price for trailing stop
    if (currentPrice > (position.highestPrice || 0)) {
      position.highestPrice = currentPrice;
    }

    // Check for exit triggers
    const trigger = this.checkExitTriggers(position);
    if (trigger) {
      await this.executeExit(position, trigger);
    }

    // Update cache
    await PortfolioCache.setPosition(tokenMint, position);
  }

  /**
   * Check if any exit conditions are met
   */
  private checkExitTriggers(position: Position): ExitTrigger | null {
    if (!position.exitStrategy.enabled) return null;

    const currentPrice = position.currentPrice;
    const entryPrice = position.entryPrice;
    const priceChangePercent = ((currentPrice - entryPrice) / entryPrice) * 100;

    // Check stop-loss
    if (position.exitStrategy.stopLoss && priceChangePercent <= -position.exitStrategy.stopLoss) {
      return {
        type: 'stop-loss',
        reason: `Stop-loss triggered at ${priceChangePercent.toFixed(2)}% loss`,
        targetPrice: currentPrice,
        sellPercentage: 100 - (position.exitStrategy.moonBag || 0)
      };
    }

    // Check trailing stop
    if (position.exitStrategy.trailingStop && position.highestPrice) {
      const trailingStopPrice = position.highestPrice * (1 - position.exitStrategy.trailingStop / 100);
      if (currentPrice <= trailingStopPrice) {
        return {
          type: 'trailing-stop',
          reason: `Trailing stop triggered at $${currentPrice} (${position.exitStrategy.trailingStop}% from high of $${position.highestPrice})`,
          targetPrice: currentPrice,
          sellPercentage: 100 - (position.exitStrategy.moonBag || 0)
        };
      }
    }

    // Check take-profit levels
    if (position.exitStrategy.takeProfitLevels) {
      for (const profitLevel of position.exitStrategy.takeProfitLevels) {
        if (priceChangePercent >= profitLevel && !this.hasTakenProfitAt(position, profitLevel)) {
          const sellPercentage = this.calculateTakeProfitSellPercentage(position, profitLevel);
          return {
            type: 'take-profit',
            reason: `Take-profit triggered at ${priceChangePercent.toFixed(2)}% gain (target: ${profitLevel}%)`,
            targetPrice: currentPrice,
            sellPercentage
          };
        }
      }
    }

    return null;
  }

  /**
   * Check if profit has already been taken at a specific level
   */
  private hasTakenProfitAt(position: Position, profitLevel: number): boolean {
    // Simple implementation - in production, track individual profit takes
    return (position.profitTaken || 0) >= profitLevel;
  }

  /**
   * Calculate how much to sell for take-profit
   */
  private calculateTakeProfitSellPercentage(position: Position, profitLevel: number): number {
    // Sell 25% at each take-profit level, keeping moon bag
    const moonBagPercentage = position.exitStrategy.moonBag || 0;
    const availableToSell = 100 - moonBagPercentage - (position.profitTaken || 0);
    
    return Math.min(25, availableToSell);
  }

  /**
   * Execute exit trade
   */
  private async executeExit(position: Position, trigger: ExitTrigger): Promise<void> {
    try {
      logger.info(`Executing exit for ${position.symbol}: ${trigger.reason}`);

      const sellAmount = (position.amount * trigger.sellPercentage) / 100;
      const slippage = position.exitStrategy.maxSlippage || 100; // 1% default slippage for exits

      // Get quote for selling
      const quote = await jupiterClient.getQuote(
        position.tokenMint,
        'So11111111111111111111111111111111111111112', // SOL
        sellAmount,
        slippage
      );

      // Add trade job
      await addTradeJob({
        inputMint: position.tokenMint,
        outputMint: 'So11111111111111111111111111111111111111112',
        amount: sellAmount,
        slippageBps: slippage,
        quote,
        applyExitStrategy: false // Don't apply exit strategy to exit trades
      });

      // Update position
      if (trigger.type === 'take-profit') {
        position.profitTaken = (position.profitTaken || 0) + trigger.sellPercentage;
      }

      position.amount -= sellAmount;

      // Remove position if fully sold
      if (position.amount <= 0.001) { // Small threshold for dust
        await this.removePosition(position.tokenMint);
      } else {
        await PortfolioCache.setPosition(position.tokenMint, position);
      }

      logger.info(`Exit executed: Sold ${trigger.sellPercentage}% of ${position.symbol}`);
    } catch (error) {
      logger.error('Failed to execute exit:', error);
    }
  }

  /**
   * Start monitoring positions
   */
  private startMonitoring(): void {
    if (this.monitoringInterval) return;

    logger.info('Starting exit strategy monitoring...');
    
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.monitorPositions();
      } catch (error) {
        logger.error('Error in position monitoring:', error);
      }
    }, this.MONITORING_INTERVAL_MS);
  }

  /**
   * Stop monitoring positions
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      logger.info('Exit strategy monitoring stopped');
    }
  }

  /**
   * Monitor all positions for exit conditions
   */
  private async monitorPositions(): Promise<void> {
    const positions = Array.from(this.positions.values());
    
    if (positions.length === 0) return;

    logger.debug(`Monitoring ${positions.length} positions for exit conditions`);

    for (const position of positions) {
      try {
        // Get current price
        const currentPrice = await jupiterClient.getTokenPrice(position.tokenMint);
        
        if (currentPrice > 0) {
          await this.updatePosition(position.tokenMint, currentPrice);
        }
      } catch (error) {
        logger.error(`Failed to update position ${position.symbol}:`, error);
      }
    }
  }

  /**
   * Get all monitored positions
   */
  getPositions(): Position[] {
    return Array.from(this.positions.values());
  }

  /**
   * Get position by token mint
   */
  getPosition(tokenMint: string): Position | undefined {
    return this.positions.get(tokenMint);
  }

  /**
   * Update exit strategy for a position
   */
  async updateExitStrategy(tokenMint: string, exitStrategy: Partial<ExitStrategy>): Promise<void> {
    const position = this.positions.get(tokenMint);
    if (!position) {
      throw new Error(`Position not found: ${tokenMint}`);
    }

    position.exitStrategy = { ...position.exitStrategy, ...exitStrategy };
    await PortfolioCache.setPosition(tokenMint, position);
    
    logger.info(`Exit strategy updated for ${position.symbol}`);
  }

  /**
   * Load positions from cache on startup
   */
  async loadPositionsFromCache(): Promise<void> {
    try {
      const cachedPositions = await PortfolioCache.getAllPositions();
      
      for (const position of cachedPositions) {
        this.positions.set(position.tokenMint, position);
      }

      logger.info(`Loaded ${cachedPositions.length} positions from cache`);
    } catch (error) {
      logger.error('Failed to load positions from cache:', error);
    }
  }
}

export const exitStrategyEngine = new ExitStrategyEngine();
