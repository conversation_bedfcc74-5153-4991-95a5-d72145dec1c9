/**
 * Helius WebSocket service for real-time account monitoring
 * 
 * Monitors wallet token account changes in real-time using Helius WebSocket API
 * This replaces the Jupiter polling approach to avoid rate limits
 */

import WebSocket from 'ws';
import pino from 'pino';
import { PublicKey } from '@solana/web3.js';

const logger = pino({ name: 'helius-websocket' });

interface HeliusAccountUpdate {
  account: string;
  data: {
    parsed: {
      info: {
        mint: string;
        owner: string;
        tokenAmount: {
          amount: string;
          decimals: number;
          uiAmount: number;
          uiAmountString: string;
        };
      };
    };
  };
  executable: boolean;
  lamports: number;
  owner: string;
  rentEpoch: number;
  space: number;
}

interface HeliusWebSocketMessage {
  jsonrpc: string;
  method: string;
  params: {
    result: {
      context: {
        slot: number;
      };
      value: HeliusAccountUpdate;
    };
    subscription: number;
  };
}

export class HeliusWebSocketService {
  private ws: WebSocket | null = null;
  private subscriptions = new Map<string, number>();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 5000;
  private isConnected = false;
  private walletAddress: string;
  private heliusKey: string;
  private onAccountUpdate?: (mint: string, balance: number) => void;

  constructor(walletAddress: string, heliusKey: string) {
    this.walletAddress = walletAddress;
    this.heliusKey = heliusKey;
  }

  /**
   * Connect to Helius WebSocket
   */
  async connect(): Promise<void> {
    try {
      const wsUrl = `wss://mainnet.helius-rpc.com/?api-key=${this.heliusKey}`;
      
      logger.info('Connecting to Helius WebSocket...');
      this.ws = new WebSocket(wsUrl);

      this.ws.on('open', () => {
        logger.info('Connected to Helius WebSocket');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // Subscribe to wallet token account changes
        this.subscribeToWalletAccounts();
      });

      this.ws.on('message', (data: WebSocket.Data) => {
        try {
          const message: HeliusWebSocketMessage = JSON.parse(data.toString());
          this.handleMessage(message);
        } catch (error) {
          logger.error('Failed to parse WebSocket message:', error);
        }
      });

      this.ws.on('close', (code: number, reason: Buffer) => {
        logger.warn(`WebSocket closed: ${code} - ${reason.toString()}`);
        this.isConnected = false;
        this.handleReconnect();
      });

      this.ws.on('error', (error: Error) => {
        logger.error('WebSocket error:', error);
        this.isConnected = false;
      });

    } catch (error) {
      logger.error('Failed to connect to Helius WebSocket:', error);
      this.handleReconnect();
    }
  }

  /**
   * Subscribe to wallet token account changes
   */
  private subscribeToWalletAccounts(): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      logger.error('WebSocket not connected, cannot subscribe');
      return;
    }

    const subscribeMessage = {
      jsonrpc: '2.0',
      id: 1,
      method: 'programSubscribe',
      params: [
        'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA', // SPL Token Program
        {
          commitment: 'confirmed',
          encoding: 'jsonParsed',
          filters: [
            {
              memcmp: {
                offset: 32, // Owner field offset in token account
                bytes: this.walletAddress
              }
            }
          ]
        }
      ]
    };

    logger.info(`Subscribing to token account changes for wallet: ${this.walletAddress}`);
    this.ws.send(JSON.stringify(subscribeMessage));
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(message: HeliusWebSocketMessage): void {
    try {
      if (message.method === 'programNotification') {
        const accountData = message.params.result.value;
        
        if (accountData.data?.parsed?.info) {
          const { mint, tokenAmount } = accountData.data.parsed.info;
          const balance = tokenAmount.uiAmount;
          
          logger.debug(`Token balance update: ${mint} = ${balance}`);
          
          // Notify listeners of balance change
          if (this.onAccountUpdate) {
            this.onAccountUpdate(mint, balance);
          }
        }
      } else if (message.jsonrpc === '2.0' && 'result' in message) {
        // Subscription confirmation
        logger.info('Subscription confirmed:', message);
      }
    } catch (error) {
      logger.error('Error handling WebSocket message:', error);
    }
  }

  /**
   * Handle reconnection logic
   */
  private handleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('Max reconnection attempts reached, giving up');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
    
    logger.info(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      this.connect();
    }, delay);
  }

  /**
   * Set callback for account updates
   */
  setAccountUpdateCallback(callback: (mint: string, balance: number) => void): void {
    this.onAccountUpdate = callback;
  }

  /**
   * Disconnect from WebSocket
   */
  disconnect(): void {
    if (this.ws) {
      logger.info('Disconnecting from Helius WebSocket');
      this.ws.close();
      this.ws = null;
      this.isConnected = false;
    }
  }

  /**
   * Check if connected
   */
  isWebSocketConnected(): boolean {
    return this.isConnected && this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * Get connection status
   */
  getStatus(): { connected: boolean; subscriptions: number; reconnectAttempts: number } {
    return {
      connected: this.isConnected,
      subscriptions: this.subscriptions.size,
      reconnectAttempts: this.reconnectAttempts
    };
  }
}

// Singleton instance
let heliusWebSocketService: HeliusWebSocketService | null = null;

/**
 * Initialize Helius WebSocket service
 */
export function initializeHeliusWebSocket(walletAddress: string, heliusKey: string): HeliusWebSocketService {
  if (!heliusWebSocketService) {
    heliusWebSocketService = new HeliusWebSocketService(walletAddress, heliusKey);
  }
  return heliusWebSocketService;
}

/**
 * Get the singleton instance
 */
export function getHeliusWebSocketService(): HeliusWebSocketService | null {
  return heliusWebSocketService;
}
