/**
 * Jupiter DEX integration service
 * 
 * Provides trading functionality through Jupiter aggregator:
 * - Price quotes and routing
 * - Swap transaction building
 * - Slippage management
 * - Market data fetching
 */

import axios, { AxiosInstance } from 'axios';
import { PublicKey, VersionedTransaction } from '@solana/web3.js';
import pino from 'pino';
import { getSolanaConnection, getWallet } from './solanaClient';

const logger = pino({ name: 'jupiter-client' });

interface JupiterQuoteResponse {
  inputMint: string;
  inAmount: string;
  outputMint: string;
  outAmount: string;
  otherAmountThreshold: string;
  swapMode: string;
  slippageBps: number;
  platformFee?: any;
  priceImpactPct: string;
  routePlan: any[];
}

interface JupiterSwapResponse {
  swapTransaction: string;
}

class JupiterClient {
  private client: AxiosInstance;
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.JUPITER_BASE_URL || 'https://quote-api.jup.ag/v6';
    
    this.client = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'MemePortfolio/1.0'
      }
    });

    // Add API key if provided
    if (process.env.JUP_API_KEY && process.env.JUP_API_KEY !== 'TODO_OPTIONAL_FOR_PAID_PLAN') {
      this.client.defaults.headers['Authorization'] = `Bearer ${process.env.JUP_API_KEY}`;
    }

    // Request/response logging
    this.client.interceptors.request.use(
      (config) => {
        logger.debug(`Jupiter API request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        logger.error('Jupiter API request error:', error);
        return Promise.reject(error);
      }
    );

    this.client.interceptors.response.use(
      (response) => {
        logger.debug(`Jupiter API response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        logger.error('Jupiter API response error:', {
          status: error.response?.status,
          data: error.response?.data,
          url: error.config?.url
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get a quote for swapping tokens
   */
  async getQuote(
    inputMint: string,
    outputMint: string,
    amount: number,
    slippageBps: number = 50
  ): Promise<JupiterQuoteResponse> {
    try {
      logger.info(`Getting quote: ${amount} ${inputMint} -> ${outputMint} (slippage: ${slippageBps}bps)`);

      const params = {
        inputMint,
        outputMint,
        amount: amount.toString(),
        slippageBps,
        onlyDirectRoutes: false,
        asLegacyTransaction: false
      };

      const response = await this.client.get('/quote', { params });
      
      const quote = response.data as JupiterQuoteResponse;
      
      logger.info(`Quote received: ${quote.inAmount} -> ${quote.outAmount} (impact: ${quote.priceImpactPct}%)`);
      
      return quote;
    } catch (error) {
      logger.error('Failed to get Jupiter quote:', error);
      throw new Error(`Jupiter quote failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get swap transaction for a quote
   */
  async getSwapTransaction(
    quote: JupiterQuoteResponse,
    userPublicKey: string,
    wrapAndUnwrapSol: boolean = true
  ): Promise<VersionedTransaction> {
    try {
      logger.info(`Building swap transaction for user: ${userPublicKey}`);

      const swapRequest = {
        quoteResponse: quote,
        userPublicKey,
        wrapAndUnwrapSol,
        useSharedAccounts: true,
        feeAccount: undefined, // Can be set for referral fees
        trackingAccount: undefined,
        computeUnitPriceMicroLamports: 'auto'
      };

      const response = await this.client.post('/swap', swapRequest);
      const swapResponse = response.data as JupiterSwapResponse;

      // Deserialize the transaction
      const transactionBuf = Buffer.from(swapResponse.swapTransaction, 'base64');
      const transaction = VersionedTransaction.deserialize(transactionBuf);

      logger.info('Swap transaction built successfully');
      return transaction;
    } catch (error) {
      logger.error('Failed to build swap transaction:', error);
      throw new Error(`Jupiter swap transaction failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute a complete swap operation
   */
  async executeSwap(
    inputMint: string,
    outputMint: string,
    amount: number,
    slippageBps: number = 50
  ): Promise<string> {
    const wallet = getWallet();
    if (!wallet) {
      throw new Error('Wallet not initialized - cannot execute swap');
    }

    try {
      // Get quote
      const quote = await this.getQuote(inputMint, outputMint, amount, slippageBps);
      
      // Check price impact
      const priceImpact = parseFloat(quote.priceImpactPct);
      if (priceImpact > 5) {
        logger.warn(`High price impact detected: ${priceImpact}%`);
      }
      
      // Build transaction
      const transaction = await this.getSwapTransaction(quote, wallet.publicKey.toString());
      
      // Sign transaction
      transaction.sign([wallet]);
      
      // Send transaction
      const connection = getSolanaConnection();
      const signature = await connection.sendTransaction(transaction);
      
      logger.info(`Swap executed successfully: ${signature}`);
      return signature;
    } catch (error) {
      logger.error('Swap execution failed:', error);
      throw error;
    }
  }

  /**
   * Get token price in USDC
   */
  async getTokenPrice(tokenMint: string): Promise<number> {
    try {
      // Use SOL as intermediate if not USDC
      const USDC_MINT = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
      const SOL_MINT = 'So11111111111111111111111111111111111111112';
      
      if (tokenMint === USDC_MINT) {
        return 1; // USDC = $1
      }
      
      // Get quote for 1 token to USDC (using 1e6 for proper decimals)
      const quote = await this.getQuote(tokenMint, USDC_MINT, 1000000, 50);
      
      const inputAmount = parseFloat(quote.inAmount);
      const outputAmount = parseFloat(quote.outAmount);
      
      if (inputAmount === 0) {
        return 0;
      }
      
      // Calculate price per token
      const price = outputAmount / inputAmount;
      
      logger.debug(`Token ${tokenMint} price: $${price}`);
      return price;
    } catch (error) {
      logger.error(`Failed to get token price for ${tokenMint}:`, error);
      return 0;
    }
  }

  /**
   * Get supported tokens list
   */
  async getSupportedTokens(): Promise<any[]> {
    try {
      const response = await this.client.get('/tokens');
      return response.data;
    } catch (error) {
      logger.error('Failed to get supported tokens:', error);
      return [];
    }
  }

  /**
   * Validate if a token is supported by Jupiter
   */
  async isTokenSupported(tokenMint: string): Promise<boolean> {
    try {
      const tokens = await this.getSupportedTokens();
      return tokens.some(token => token.address === tokenMint);
    } catch (error) {
      logger.error(`Failed to validate token support for ${tokenMint}:`, error);
      return false;
    }
  }
}

// Export singleton instance
export const jupiterClient = new JupiterClient();

// Export types
export type { JupiterQuoteResponse, JupiterSwapResponse };
