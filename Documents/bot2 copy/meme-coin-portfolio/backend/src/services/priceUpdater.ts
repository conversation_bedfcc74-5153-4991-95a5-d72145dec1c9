/**
 * Real-time Price Update Service
 *
 * Continuously fetches and broadcasts updated token prices
 * to keep portfolio values current
 */

import pino from 'pino';
import { jupiterClient } from './jupiterClient';
import { tokenMetadataService } from './tokenMetadata';
import { getSolanaConnection, getWallet } from './solanaClient';
import { PublicKey } from '@solana/web3.js';
import { broadcastPortfolioUpdate } from './websocket';
import { initializeHeliusWebSocket, getHeliusWebSocketService } from './heliusWebSocket';

const logger = pino({ name: 'price-updater' });

interface TokenPrice {
  mint: string;
  symbol: string;
  price: number;
  lastUpdated: Date;
  change24h?: number;
}

interface PortfolioUpdate {
  totalValue: number;
  totalValueSOL: number;
  positions: Array<{
    tokenMint: string;
    tokenSymbol: string;
    currentPrice: number;
    value: number;
    priceChange24h?: number;
  }>;
  lastUpdated: Date;
}

class PriceUpdateService {
  private updateInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  private readonly UPDATE_INTERVAL = 120000; // 2 minutes
  private tokenPrices = new Map<string, TokenPrice>();
  private lastPortfolioUpdate: PortfolioUpdate | null = null;

  /**
   * Start the real-time price update service
   */
  start(): void {
    if (this.isRunning) {
      logger.warn('Price update service is already running');
      return;
    }

    logger.info('Starting real-time price update service...');
    this.isRunning = true;

    // Initialize Helius WebSocket for real-time account monitoring
    this.initializeHeliusWebSocket();

    // Initial update
    this.updatePrices();

    // Set up recurring updates (less frequent now since we have real-time updates)
    this.updateInterval = setInterval(() => {
      this.updatePrices();
    }, this.UPDATE_INTERVAL);

    logger.info(`Price updates scheduled every ${this.UPDATE_INTERVAL / 1000} seconds`);
  }

  /**
   * Stop the price update service
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    logger.info('Stopping price update service...');

    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }

    // Disconnect Helius WebSocket
    const heliusWS = getHeliusWebSocketService();
    if (heliusWS) {
      heliusWS.disconnect();
      logger.info('Helius WebSocket disconnected');
    }

    this.isRunning = false;
    logger.info('Price update service stopped');
  }

  /**
   * Initialize Helius WebSocket for real-time account monitoring
   */
  private initializeHeliusWebSocket(): void {
    try {
      const walletAddress = process.env.WALLET_ADDRESS;
      const heliusKey = process.env.HELIUS_KEY;

      if (!walletAddress || !heliusKey) {
        logger.warn('WALLET_ADDRESS or HELIUS_KEY not configured, skipping WebSocket initialization');
        return;
      }

      logger.info('Initializing Helius WebSocket for real-time monitoring...');

      const heliusWS = initializeHeliusWebSocket(walletAddress, heliusKey);

      // Set up callback for account updates
      heliusWS.setAccountUpdateCallback((mint: string, balance: number) => {
        logger.debug(`Real-time balance update: ${mint} = ${balance}`);
        // Trigger immediate price update for this token
        this.updateSingleTokenPrice(mint, balance);
      });

      // Connect to WebSocket
      heliusWS.connect();

      logger.info('Helius WebSocket initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Helius WebSocket:', error);
    }
  }

  /**
   * Update price for a single token (triggered by WebSocket)
   */
  private async updateSingleTokenPrice(mint: string, balance: number): Promise<void> {
    try {
      if (balance <= 0) return;

      // Get token metadata
      const metadata = await tokenMetadataService.getTokenMetadata(mint);
      const tokenSymbol = metadata?.symbol || 'Unknown';

      // Get current price
      const currentPrice = await jupiterClient.getTokenPrice(mint);
      const tokenValue = balance * currentPrice;

      logger.debug(`Updated ${tokenSymbol}: ${balance} tokens @ $${currentPrice} = $${tokenValue.toFixed(2)}`);

      // Update token price cache
      this.tokenPrices.set(mint, {
        mint,
        symbol: tokenSymbol,
        price: currentPrice,
        lastUpdated: new Date()
      });

      // Trigger portfolio update
      this.updatePrices();
    } catch (error) {
      logger.error(`Failed to update price for token ${mint}:`, error);
    }
  }

  /**
   * Update prices for all tokens in the wallet
   */
  private async updatePrices(): Promise<void> {
    try {
      const wallet = getWallet();
      if (!wallet) {
        logger.debug('No wallet configured, skipping price update');
        return;
      }

      logger.debug('Fetching current token prices...');

      // Get all token accounts
      const connection = getSolanaConnection();
      const tokenAccounts = await connection.getParsedTokenAccountsByOwner(wallet.publicKey, {
        programId: new PublicKey('TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA')
      });

      const updatedPositions: PortfolioUpdate['positions'] = [];
      let totalTokenValue = 0;

      // Update prices for each token
      for (const tokenAccount of tokenAccounts.value) {
        const accountData = tokenAccount.account.data.parsed.info;
        const tokenAmount = accountData.tokenAmount.uiAmount;

        if (tokenAmount && tokenAmount > 0) {
          const tokenMint = accountData.mint;

          try {
            // Get current price
            const currentPrice = await jupiterClient.getTokenPrice(tokenMint);
            const tokenValue = tokenAmount * currentPrice;

            // Get token metadata for symbol
            const metadata = await tokenMetadataService.getTokenMetadata(tokenMint);
            const tokenSymbol = metadata?.symbol || `${tokenMint.slice(0, 4)}...${tokenMint.slice(-4)}`;

            // Calculate 24h change (simplified - would need historical data for real calculation)
            const previousPrice = this.tokenPrices.get(tokenMint);
            let priceChange24h = 0;
            if (previousPrice && previousPrice.price > 0) {
              priceChange24h = ((currentPrice - previousPrice.price) / previousPrice.price) * 100;
            }

            // Store updated price
            this.tokenPrices.set(tokenMint, {
              mint: tokenMint,
              symbol: tokenSymbol,
              price: currentPrice,
              lastUpdated: new Date(),
              change24h: priceChange24h
            });

            // Only include tokens with significant value
            if (tokenValue > 0.01 || tokenAmount > 1000) {
              updatedPositions.push({
                tokenMint,
                tokenSymbol,
                currentPrice,
                value: tokenValue,
                priceChange24h
              });

              totalTokenValue += tokenValue;
            }

          } catch (error) {
            logger.warn(`Failed to update price for token ${tokenMint}:`, error);
          }
        }
      }

      // Get SOL balance and price
      const solBalance = await connection.getBalance(wallet.publicKey) / 1e9;
      const solPriceUSD = 180; // Could integrate with a SOL price API
      const totalValueUSD = solBalance * solPriceUSD;

      // Create portfolio update
      const portfolioUpdate: PortfolioUpdate = {
        totalValue: totalValueUSD + totalTokenValue,
        totalValueSOL: solBalance,
        positions: updatedPositions,
        lastUpdated: new Date()
      };

      // Check if there are significant changes to broadcast
      if (this.shouldBroadcastUpdate(portfolioUpdate)) {
        logger.info(`Broadcasting portfolio update: $${portfolioUpdate.totalValue.toFixed(2)} total value`);

        // Broadcast to all connected clients
        broadcastPortfolioUpdate(portfolioUpdate);

        this.lastPortfolioUpdate = portfolioUpdate;
      }

      logger.debug(`Updated prices for ${updatedPositions.length} tokens`);

    } catch (error) {
      logger.error('Failed to update prices:', error);
    }
  }

  /**
   * Determine if the update should be broadcast to clients
   */
  private shouldBroadcastUpdate(newUpdate: PortfolioUpdate): boolean {
    if (!this.lastPortfolioUpdate) {
      return true; // First update
    }

    const lastUpdate = this.lastPortfolioUpdate;

    // Broadcast if total value changed by more than $0.10
    const valueChange = Math.abs(newUpdate.totalValue - lastUpdate.totalValue);
    if (valueChange > 0.10) {
      return true;
    }

    // Broadcast if any token price changed by more than 2%
    for (const position of newUpdate.positions) {
      const lastPosition = lastUpdate.positions.find(p => p.tokenMint === position.tokenMint);
      if (lastPosition) {
        const priceChangePercent = Math.abs(
          ((position.currentPrice - lastPosition.currentPrice) / lastPosition.currentPrice) * 100
        );
        if (priceChangePercent > 2) {
          return true;
        }
      }
    }

    // Broadcast every 5 minutes regardless
    const timeDiff = newUpdate.lastUpdated.getTime() - lastUpdate.lastUpdated.getTime();
    if (timeDiff > 5 * 60 * 1000) { // 5 minutes
      return true;
    }

    return false;
  }

  /**
   * Get current token prices
   */
  getTokenPrices(): Map<string, TokenPrice> {
    return new Map(this.tokenPrices);
  }

  /**
   * Get last portfolio update
   */
  getLastPortfolioUpdate(): PortfolioUpdate | null {
    return this.lastPortfolioUpdate;
  }

  /**
   * Force an immediate price update
   */
  async forceUpdate(): Promise<void> {
    logger.info('Forcing immediate price update...');
    await this.updatePrices();
  }
}

// Export singleton instance
export const priceUpdateService = new PriceUpdateService();

// Export types
export type { TokenPrice, PortfolioUpdate };
