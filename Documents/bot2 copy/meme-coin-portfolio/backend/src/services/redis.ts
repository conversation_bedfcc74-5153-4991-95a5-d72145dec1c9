/**
 * Redis connection and management service
 *
 * Provides centralized Redis connection for:
 * - BullMQ job queues
 * - Caching portfolio data
 * - Session management
 * - Real-time data storage
 */

import Redis from 'ioredis';
import pino from 'pino';

const logger = pino({ name: 'redis-service' });

let redisClient: Redis | null = null;

/**
 * Initialize Redis connection with retry logic
 */
export async function initializeRedis(): Promise<Redis> {
  try {
    const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

    redisClient = new Redis(redisUrl, {
      enableReadyCheck: false,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      connectTimeout: 10000,
      commandTimeout: 5000,
    });

    // Event handlers
    redisClient.on('connect', () => {
      logger.info('Redis connection established');
    });

    redisClient.on('ready', () => {
      logger.info('Redis client ready');
    });

    redisClient.on('error', (error) => {
      logger.error('Redis connection error:', error);
    });

    redisClient.on('close', () => {
      logger.warn('Redis connection closed');
    });

    redisClient.on('reconnecting', () => {
      logger.info('Redis reconnecting...');
    });

    // Test connection
    await redisClient.ping();
    logger.info('Redis ping successful');

    return redisClient;
  } catch (error) {
    logger.error('Failed to initialize Redis:', error);
    throw error;
  }
}

/**
 * Get the Redis client instance
 */
export function getRedisClient(): Redis {
  if (!redisClient) {
    throw new Error('Redis client not initialized. Call initializeRedis() first.');
  }
  return redisClient;
}

/**
 * Portfolio data caching utilities
 */
export class PortfolioCache {
  private static readonly PORTFOLIO_KEY = 'portfolio:data';
  private static readonly POSITIONS_KEY = 'portfolio:positions';
  private static readonly SIGNALS_KEY = 'signals:recent';
  private static readonly TTL = 60; // 1 minute TTL for real-time data

  /**
   * Cache portfolio summary data
   */
  static async setPortfolioData(data: any): Promise<void> {
    const client = getRedisClient();
    await client.setex(this.PORTFOLIO_KEY, this.TTL, JSON.stringify(data));
  }

  /**
   * Get cached portfolio summary data
   */
  static async getPortfolioData(): Promise<any | null> {
    const client = getRedisClient();
    const data = await client.get(this.PORTFOLIO_KEY);
    return data ? JSON.parse(data) : null;
  }

  /**
   * Cache active positions
   */
  static async setPositions(positions: any[]): Promise<void> {
    const client = getRedisClient();
    await client.setex(this.POSITIONS_KEY, this.TTL, JSON.stringify(positions));
  }

  /**
   * Get cached positions
   */
  static async getPositions(): Promise<any[] | null> {
    const client = getRedisClient();
    const data = await client.get(this.POSITIONS_KEY);
    return data ? JSON.parse(data) : null;
  }

  /**
   * Add a new signal to the recent signals list
   */
  static async addSignal(signal: any): Promise<void> {
    const client = getRedisClient();
    await client.lpush(this.SIGNALS_KEY, JSON.stringify(signal));
    await client.ltrim(this.SIGNALS_KEY, 0, 99); // Keep last 100 signals
    await client.expire(this.SIGNALS_KEY, 3600); // 1 hour TTL
  }

  /**
   * Get recent signals
   */
  static async getRecentSignals(limit: number = 20): Promise<any[]> {
    const client = getRedisClient();
    const signals = await client.lrange(this.SIGNALS_KEY, 0, limit - 1);
    return signals.map(signal => JSON.parse(signal));
  }

  /**
   * Cache individual position with exit strategy
   */
  static async setPosition(tokenMint: string, position: any): Promise<void> {
    const client = getRedisClient();
    const key = `position:${tokenMint}`;
    await client.setex(key, this.TTL * 10, JSON.stringify(position)); // Longer TTL for positions

    // Add to positions set for tracking
    await client.sadd('positions:active', tokenMint);
  }

  /**
   * Get individual position
   */
  static async getPosition(tokenMint: string): Promise<any | null> {
    const client = getRedisClient();
    const key = `position:${tokenMint}`;
    const data = await client.get(key);
    return data ? JSON.parse(data) : null;
  }

  /**
   * Get all active positions
   */
  static async getAllPositions(): Promise<any[]> {
    const client = getRedisClient();
    const tokenMints = await client.smembers('positions:active');
    const positions = [];

    for (const tokenMint of tokenMints) {
      const position = await this.getPosition(tokenMint);
      if (position) {
        positions.push(position);
      }
    }

    return positions;
  }

  /**
   * Delete individual position
   */
  static async deletePosition(tokenMint: string): Promise<void> {
    const client = getRedisClient();
    const key = `position:${tokenMint}`;
    await client.del(key);
    await client.srem('positions:active', tokenMint);
  }

  /**
   * Invalidate portfolio cache
   */
  static async invalidatePortfolio(): Promise<void> {
    const client = getRedisClient();
    await client.del(this.PORTFOLIO_KEY);
  }

  /**
   * Clear all cached data
   */
  static async clearAll(): Promise<void> {
    const client = getRedisClient();
    await client.del(this.PORTFOLIO_KEY, this.POSITIONS_KEY, this.SIGNALS_KEY);

    // Clear all individual positions
    const tokenMints = await client.smembers('positions:active');
    for (const tokenMint of tokenMints) {
      await client.del(`position:${tokenMint}`);
    }
    await client.del('positions:active');
  }
}

/**
 * Configuration caching utilities
 */
export class ConfigCache {
  private static readonly EXIT_STRATEGY_KEY = 'config:exit_strategy';
  private static readonly TRADING_CONFIG_KEY = 'config:trading';
  private static readonly TTL = 3600; // 1 hour TTL for config data

  /**
   * Cache exit strategy configuration
   */
  static async setExitStrategy(config: any): Promise<void> {
    const client = getRedisClient();
    await client.setex(this.EXIT_STRATEGY_KEY, this.TTL, JSON.stringify(config));
  }

  /**
   * Get cached exit strategy configuration
   */
  static async getExitStrategy(): Promise<any | null> {
    const client = getRedisClient();
    const data = await client.get(this.EXIT_STRATEGY_KEY);
    return data ? JSON.parse(data) : null;
  }

  /**
   * Cache trading configuration
   */
  static async setTradingConfig(config: any): Promise<void> {
    const client = getRedisClient();
    await client.setex(this.TRADING_CONFIG_KEY, this.TTL, JSON.stringify(config));
  }

  /**
   * Get cached trading configuration
   */
  static async getTradingConfig(): Promise<any | null> {
    const client = getRedisClient();
    const data = await client.get(this.TRADING_CONFIG_KEY);
    return data ? JSON.parse(data) : null;
  }
}

/**
 * Close Redis connection
 */
export async function closeRedis(): Promise<void> {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
    logger.info('Redis connection closed');
  }
}
