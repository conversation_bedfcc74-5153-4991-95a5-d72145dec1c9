/**
 * Solana blockchain client service
 *
 * Provides connection to Solana network with:
 * - RPC connection management
 * - Wallet integration
 * - Transaction utilities
 * - Priority fee management
 */

import { Connection, Keypair, PublicKey, Transaction, VersionedTransaction } from '@solana/web3.js';
import bs58 from 'bs58';
import pino from 'pino';

const logger = pino({ name: 'solana-client' });

let connection: Connection | null = null;
let wallet: Keypair | null = null;

/**
 * Initialize Solana connection and wallet
 */
export async function initializeSolana(): Promise<{ connection: Connection; wallet: Keypair | null }> {
  try {
    // Initialize RPC connection
    const rpcUrl = process.env.RPC_URL;
    if (!rpcUrl || rpcUrl === 'TODO_REPLACE_WITH_YOUR_HELIUS_RPC_URL') {
      throw new Error('RPC_URL not configured. Please set your Helius RPC URL in .env file');
    }

    connection = new Connection(rpcUrl, {
      commitment: 'confirmed',
      confirmTransactionInitialTimeout: 60000,
    });

    // Test connection
    const version = await connection.getVersion();
    logger.info(`Connected to Solana cluster: ${version['solana-core']}`);

    // Initialize wallet if private key is provided
    const privateKeyString = process.env.WALLET_PRIVATE_KEY;
    if (privateKeyString && privateKeyString !== 'TODO_REPLACE_WITH_YOUR_PRIVATE_KEY') {
      try {
        // Try to parse as base58 first (common format), then base64
        let privateKeyBytes: Uint8Array;
        try {
          // Try base58 format first
          privateKeyBytes = bs58.decode(privateKeyString);
        } catch {
          // Fallback to base64 format
          privateKeyBytes = Uint8Array.from(Buffer.from(privateKeyString, 'base64'));
        }
        wallet = Keypair.fromSecretKey(privateKeyBytes);

        logger.info(`Wallet initialized: ${wallet.publicKey.toString()}`);

        // Check wallet balance
        const balance = await connection.getBalance(wallet.publicKey);
        logger.info(`Wallet balance: ${balance / 1e9} SOL`);

        if (balance === 0) {
          logger.warn('⚠️  Wallet has zero SOL balance - trading will fail');
        }
      } catch (error) {
        logger.error('Failed to initialize wallet:', error);
        logger.warn('Trading will be disabled due to wallet initialization failure');
        wallet = null;
      }
    } else {
      logger.warn('⚠️  WALLET_PRIVATE_KEY not configured - trading will be disabled');
    }

    return { connection, wallet };
  } catch (error) {
    logger.error('Failed to initialize Solana client:', error);
    throw error;
  }
}

/**
 * Get the Solana connection instance
 */
export function getSolanaConnection(): Connection {
  if (!connection) {
    throw new Error('Solana connection not initialized. Call initializeSolana() first.');
  }
  return connection;
}

/**
 * Get the wallet keypair
 */
export function getWallet(): Keypair | null {
  return wallet;
}

/**
 * Get current priority fees for transactions
 */
export async function getPriorityFees(): Promise<number> {
  try {
    const conn = getSolanaConnection();

    // Get recent priority fees (this is a simplified implementation)
    // In production, you might want to use Helius Priority Fee API
    const recentFees = await conn.getRecentPrioritizationFees();

    if (recentFees.length === 0) {
      return 1000; // Default 1000 microlamports
    }

    // Calculate median priority fee
    const fees = recentFees.map(fee => fee.prioritizationFee).sort((a, b) => a - b);
    const median = fees[Math.floor(fees.length / 2)];

    // Add 20% buffer and ensure minimum
    const priorityFee = Math.max(median * 1.2, 1000);

    logger.debug(`Calculated priority fee: ${priorityFee} microlamports`);
    return priorityFee;
  } catch (error) {
    logger.error('Failed to get priority fees:', error);
    return 5000; // Fallback to higher fee
  }
}

/**
 * Send and confirm a transaction with retry logic
 */
export async function sendAndConfirmTransaction(
  transaction: Transaction | VersionedTransaction,
  maxRetries: number = 3
): Promise<string> {
  const conn = getSolanaConnection();
  const walletKeypair = getWallet();

  if (!walletKeypair) {
    throw new Error('Wallet not initialized - cannot send transactions');
  }

  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      logger.info(`Sending transaction (attempt ${attempt}/${maxRetries})`);

      let signature: string;

      if (transaction instanceof VersionedTransaction) {
        signature = await conn.sendTransaction(transaction);
      } else {
        signature = await conn.sendTransaction(transaction, [walletKeypair]);
      }

      logger.info(`Transaction sent: ${signature}`);

      // Confirm transaction
      const confirmation = await conn.confirmTransaction(signature, 'confirmed');

      if (confirmation.value.err) {
        throw new Error(`Transaction failed: ${confirmation.value.err}`);
      }

      logger.info(`Transaction confirmed: ${signature}`);
      return signature;

    } catch (error) {
      lastError = error as Error;
      logger.warn(`Transaction attempt ${attempt} failed:`, error);

      if (attempt < maxRetries) {
        // Wait before retry with exponential backoff
        const delay = Math.pow(2, attempt) * 1000;
        logger.info(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw new Error(`Transaction failed after ${maxRetries} attempts: ${lastError?.message}`);
}

/**
 * Get token account balance
 */
export async function getTokenBalance(tokenMint: string, walletAddress?: string): Promise<number> {
  try {
    const conn = getSolanaConnection();
    const walletPubkey = walletAddress ? new PublicKey(walletAddress) : getWallet()?.publicKey;

    if (!walletPubkey) {
      throw new Error('No wallet address provided');
    }

    const tokenAccounts = await conn.getParsedTokenAccountsByOwner(walletPubkey, {
      mint: new PublicKey(tokenMint)
    });

    if (tokenAccounts.value.length === 0) {
      return 0;
    }

    const balance = tokenAccounts.value[0].account.data.parsed.info.tokenAmount.uiAmount;
    return balance || 0;
  } catch (error) {
    logger.error(`Failed to get token balance for ${tokenMint}:`, error);
    return 0;
  }
}

/**
 * Get SOL balance
 */
export async function getSolBalance(walletAddress?: string): Promise<number> {
  try {
    const conn = getSolanaConnection();
    const walletPubkey = walletAddress ? new PublicKey(walletAddress) : getWallet()?.publicKey;

    if (!walletPubkey) {
      throw new Error('No wallet address provided');
    }

    const balance = await conn.getBalance(walletPubkey);
    return balance / 1e9; // Convert lamports to SOL
  } catch (error) {
    logger.error('Failed to get SOL balance:', error);
    return 0;
  }
}

/**
 * Validate if an address is a valid Solana public key
 */
export function isValidSolanaAddress(address: string): boolean {
  try {
    new PublicKey(address);
    return true;
  } catch {
    return false;
  }
}
