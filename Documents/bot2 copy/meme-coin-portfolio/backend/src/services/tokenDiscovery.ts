/**
 * Token Discovery Service
 * 
 * Provides comprehensive token search and discovery functionality:
 * - Search tokens by symbol, name, or address
 * - Get trending tokens
 * - Filter by market cap, liquidity, etc.
 * - Integration with Jupiter token list
 */

import axios from 'axios';
import pino from 'pino';
import { tokenMetadataService } from './tokenMetadata';
import { jupiterClient } from './jupiterClient';

const logger = pino({ name: 'token-discovery' });

export interface TokenInfo {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  logoURI?: string;
  tags?: string[];
  verified?: boolean;
  price?: number;
  marketCap?: number;
  volume24h?: number;
  priceChange24h?: number;
  liquidity?: number;
  holders?: number;
  isMemeCoin?: boolean;
}

export interface TokenSearchFilters {
  query?: string;
  minPrice?: number;
  maxPrice?: number;
  minMarketCap?: number;
  maxMarketCap?: number;
  minVolume?: number;
  maxVolume?: number;
  minLiquidity?: number;
  maxLiquidity?: number;
  verified?: boolean;
  memeCoinsOnly?: boolean;
  limit?: number;
  offset?: number;
}

class TokenDiscoveryService {
  private jupiterTokens: Map<string, TokenInfo> = new Map();
  private lastTokenListUpdate = 0;
  private readonly TOKEN_LIST_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.initializeTokenList();
  }

  /**
   * Initialize and cache Jupiter token list
   */
  private async initializeTokenList(): Promise<void> {
    try {
      logger.info('Initializing token list from Jupiter...');
      
      const response = await axios.get('https://token.jup.ag/all');
      const tokens = response.data as any[];

      this.jupiterTokens.clear();
      
      for (const token of tokens) {
        const tokenInfo: TokenInfo = {
          address: token.address,
          symbol: token.symbol,
          name: token.name,
          decimals: token.decimals,
          logoURI: token.logoURI,
          tags: token.tags || [],
          verified: token.tags?.includes('verified') || false,
          isMemeCoin: this.isMemeCoin(token)
        };
        
        this.jupiterTokens.set(token.address, tokenInfo);
      }

      this.lastTokenListUpdate = Date.now();
      logger.info(`Loaded ${this.jupiterTokens.size} tokens from Jupiter`);
    } catch (error) {
      logger.error('Failed to load Jupiter token list:', error);
    }
  }

  /**
   * Check if token list needs refresh
   */
  private async ensureTokenListFresh(): Promise<void> {
    if (Date.now() - this.lastTokenListUpdate > this.TOKEN_LIST_CACHE_DURATION) {
      await this.initializeTokenList();
    }
  }

  /**
   * Determine if a token is likely a meme coin
   */
  private isMemeCoin(token: any): boolean {
    const memeKeywords = ['meme', 'doge', 'shib', 'pepe', 'wojak', 'chad', 'moon', 'rocket', 'ape', 'diamond'];
    const tokenText = `${token.symbol} ${token.name}`.toLowerCase();
    
    return memeKeywords.some(keyword => tokenText.includes(keyword)) ||
           token.tags?.includes('meme') ||
           token.tags?.includes('community');
  }

  /**
   * Search tokens with filters
   */
  async searchTokens(filters: TokenSearchFilters = {}): Promise<TokenInfo[]> {
    await this.ensureTokenListFresh();

    let results = Array.from(this.jupiterTokens.values());

    // Apply text search filter
    if (filters.query) {
      const query = filters.query.toLowerCase();
      results = results.filter(token => 
        token.symbol.toLowerCase().includes(query) ||
        token.name.toLowerCase().includes(query) ||
        token.address.toLowerCase().includes(query)
      );
    }

    // Apply verification filter
    if (filters.verified !== undefined) {
      results = results.filter(token => token.verified === filters.verified);
    }

    // Apply meme coin filter
    if (filters.memeCoinsOnly) {
      results = results.filter(token => token.isMemeCoin);
    }

    // Enrich with market data for top results
    const limit = Math.min(filters.limit || 50, 100);
    const enrichedResults = await this.enrichTokensWithMarketData(
      results.slice(filters.offset || 0, (filters.offset || 0) + limit)
    );

    // Apply market data filters
    let filteredResults = enrichedResults;

    if (filters.minPrice !== undefined) {
      filteredResults = filteredResults.filter(token => (token.price || 0) >= filters.minPrice!);
    }

    if (filters.maxPrice !== undefined) {
      filteredResults = filteredResults.filter(token => (token.price || 0) <= filters.maxPrice!);
    }

    if (filters.minMarketCap !== undefined) {
      filteredResults = filteredResults.filter(token => (token.marketCap || 0) >= filters.minMarketCap!);
    }

    if (filters.maxMarketCap !== undefined) {
      filteredResults = filteredResults.filter(token => (token.marketCap || 0) <= filters.maxMarketCap!);
    }

    if (filters.minVolume !== undefined) {
      filteredResults = filteredResults.filter(token => (token.volume24h || 0) >= filters.minVolume!);
    }

    if (filters.maxVolume !== undefined) {
      filteredResults = filteredResults.filter(token => (token.volume24h || 0) <= filters.maxVolume!);
    }

    if (filters.minLiquidity !== undefined) {
      filteredResults = filteredResults.filter(token => (token.liquidity || 0) >= filters.minLiquidity!);
    }

    if (filters.maxLiquidity !== undefined) {
      filteredResults = filteredResults.filter(token => (token.liquidity || 0) <= filters.maxLiquidity!);
    }

    return filteredResults;
  }

  /**
   * Get token by address
   */
  async getTokenByAddress(address: string): Promise<TokenInfo | null> {
    await this.ensureTokenListFresh();
    
    const token = this.jupiterTokens.get(address);
    if (!token) return null;

    // Enrich with market data
    const enriched = await this.enrichTokensWithMarketData([token]);
    return enriched[0] || null;
  }

  /**
   * Get trending meme coins
   */
  async getTrendingMemeCoins(limit: number = 20): Promise<TokenInfo[]> {
    const memeCoins = await this.searchTokens({
      memeCoinsOnly: true,
      limit: 100
    });

    // Sort by volume and market cap
    return memeCoins
      .filter(token => token.volume24h && token.marketCap)
      .sort((a, b) => {
        const scoreA = (a.volume24h || 0) * Math.log(a.marketCap || 1);
        const scoreB = (b.volume24h || 0) * Math.log(b.marketCap || 1);
        return scoreB - scoreA;
      })
      .slice(0, limit);
  }

  /**
   * Get popular tokens by volume
   */
  async getPopularTokens(limit: number = 20): Promise<TokenInfo[]> {
    const tokens = await this.searchTokens({ limit: 100 });
    
    return tokens
      .filter(token => token.volume24h)
      .sort((a, b) => (b.volume24h || 0) - (a.volume24h || 0))
      .slice(0, limit);
  }

  /**
   * Enrich tokens with market data
   */
  private async enrichTokensWithMarketData(tokens: TokenInfo[]): Promise<TokenInfo[]> {
    const enrichedTokens: TokenInfo[] = [];

    for (const token of tokens) {
      try {
        // Get price from Jupiter
        const price = await jupiterClient.getTokenPrice(token.address);
        
        // Mock market data (in production, use real market data APIs)
        const enrichedToken: TokenInfo = {
          ...token,
          price,
          marketCap: price * 1000000, // Mock market cap
          volume24h: Math.random() * 100000, // Mock volume
          priceChange24h: (Math.random() - 0.5) * 20, // Mock price change
          liquidity: Math.random() * 50000, // Mock liquidity
          holders: Math.floor(Math.random() * 10000) // Mock holders
        };

        enrichedTokens.push(enrichedToken);
      } catch (error) {
        // If enrichment fails, include token without market data
        enrichedTokens.push(token);
      }
    }

    return enrichedTokens;
  }

  /**
   * Get token suggestions for autocomplete
   */
  async getTokenSuggestions(query: string, limit: number = 10): Promise<TokenInfo[]> {
    if (!query || query.length < 2) return [];

    await this.ensureTokenListFresh();

    const queryLower = query.toLowerCase();
    const suggestions: TokenInfo[] = [];

    // Exact symbol matches first
    for (const token of this.jupiterTokens.values()) {
      if (token.symbol.toLowerCase() === queryLower) {
        suggestions.push(token);
      }
    }

    // Symbol starts with query
    for (const token of this.jupiterTokens.values()) {
      if (token.symbol.toLowerCase().startsWith(queryLower) && 
          !suggestions.find(t => t.address === token.address)) {
        suggestions.push(token);
      }
    }

    // Name starts with query
    for (const token of this.jupiterTokens.values()) {
      if (token.name.toLowerCase().startsWith(queryLower) && 
          !suggestions.find(t => t.address === token.address)) {
        suggestions.push(token);
      }
    }

    // Contains query
    for (const token of this.jupiterTokens.values()) {
      if ((token.symbol.toLowerCase().includes(queryLower) || 
           token.name.toLowerCase().includes(queryLower)) &&
          !suggestions.find(t => t.address === token.address)) {
        suggestions.push(token);
      }
    }

    return suggestions.slice(0, limit);
  }
}

export const tokenDiscoveryService = new TokenDiscoveryService();
