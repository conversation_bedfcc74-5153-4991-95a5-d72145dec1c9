/**
 * Token Metadata Service
 *
 * Fetches token metadata including names, symbols, and logos
 * from various sources like Jupiter token list and Solana token registry
 */

import axios from 'axios';
import pino from 'pino';
import { PublicKey } from '@solana/web3.js';
import { getSolanaConnection } from './solanaClient';
import { createUmi } from '@metaplex-foundation/umi-bundle-defaults';
import { publicKey } from '@metaplex-foundation/umi';
import { fetchMetadata, findMetadataPda } from '@metaplex-foundation/mpl-token-metadata';

const logger = pino({ name: 'token-metadata' });

interface TokenMetadata {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  logoURI?: string;
  tags?: string[];
}

interface JupiterTokenInfo {
  address: string;
  chainId: number;
  decimals: number;
  name: string;
  symbol: string;
  logoURI?: string;
  tags?: string[];
}

class TokenMetadataService {
  private tokenCache = new Map<string, TokenMetadata>();
  private jupiterTokens: Map<string, JupiterTokenInfo> = new Map();
  private lastFetch = 0;
  private readonly CACHE_DURATION = 60 * 60 * 1000; // 1 hour

  /**
   * Initialize the service by fetching Jupiter token list
   */
  async initialize(): Promise<void> {
    try {
      await this.fetchJupiterTokenList();
      logger.info('Token metadata service initialized');
    } catch (error) {
      logger.error('Failed to initialize token metadata service:', error);
    }
  }

  /**
   * Fetch Jupiter token list
   */
  private async fetchJupiterTokenList(): Promise<void> {
    try {
      const now = Date.now();
      if (now - this.lastFetch < this.CACHE_DURATION && this.jupiterTokens.size > 0) {
        return; // Use cached data
      }

      logger.info('Fetching Jupiter token list...');
      const response = await axios.get('https://token.jup.ag/all', {
        timeout: 10000
      });

      const tokens: JupiterTokenInfo[] = response.data;
      this.jupiterTokens.clear();

      for (const token of tokens) {
        this.jupiterTokens.set(token.address, token);
      }

      this.lastFetch = now;
      logger.info(`Loaded ${tokens.length} tokens from Jupiter`);
    } catch (error) {
      logger.error('Failed to fetch Jupiter token list:', error);
    }
  }

  /**
   * Get token metadata by mint address
   */
  async getTokenMetadata(mintAddress: string): Promise<TokenMetadata | null> {
    try {
      // Check cache first
      if (this.tokenCache.has(mintAddress)) {
        return this.tokenCache.get(mintAddress)!;
      }

      // Refresh Jupiter token list if needed
      await this.fetchJupiterTokenList();

      // Look up in Jupiter token list
      const jupiterToken = this.jupiterTokens.get(mintAddress);
      if (jupiterToken) {
        const metadata: TokenMetadata = {
          address: jupiterToken.address,
          symbol: jupiterToken.symbol,
          name: jupiterToken.name,
          decimals: jupiterToken.decimals,
          logoURI: jupiterToken.logoURI,
          tags: jupiterToken.tags
        };

        this.tokenCache.set(mintAddress, metadata);
        return metadata;
      }

      // If not found in Jupiter, try to get basic info from on-chain
      const basicMetadata = await this.getBasicTokenInfo(mintAddress);
      if (basicMetadata) {
        this.tokenCache.set(mintAddress, basicMetadata);
        return basicMetadata;
      }

      return null;
    } catch (error) {
      logger.error(`Failed to get metadata for token ${mintAddress}:`, error);
      return null;
    }
  }

  /**
   * Get basic token info for unknown tokens
   */
  private async getBasicTokenInfo(mintAddress: string): Promise<TokenMetadata | null> {
    try {
      // First try to get on-chain metadata
      const onChainMetadata = await this.getOnChainMetadata(mintAddress);
      if (onChainMetadata) {
        return onChainMetadata;
      }

      // Fallback to basic metadata
      const metadata: TokenMetadata = {
        address: mintAddress,
        symbol: this.generateSymbolFromAddress(mintAddress),
        name: `Token ${mintAddress.slice(0, 8)}...`,
        decimals: 9, // Default decimals
      };

      return metadata;
    } catch (error) {
      logger.error(`Failed to get basic info for token ${mintAddress}:`, error);
      return null;
    }
  }

  /**
   * Fetch on-chain metadata using Metaplex Token Metadata program
   */
  private async getOnChainMetadata(mintAddress: string): Promise<TokenMetadata | null> {
    try {
      // For now, return null to use fallback metadata
      // TODO: Implement proper on-chain metadata fetching
      return null;
    } catch (error) {
      logger.debug(`Failed to get on-chain metadata for ${mintAddress}:`, error);
      return null;
    }
  }



  /**
   * Generate a readable symbol from token address
   */
  private generateSymbolFromAddress(address: string): string {
    // Take first 4 and last 4 characters for a readable symbol
    return `${address.slice(0, 4).toUpperCase()}...${address.slice(-4).toUpperCase()}`;
  }

  /**
   * Get multiple token metadata in batch
   */
  async getBatchTokenMetadata(mintAddresses: string[]): Promise<Map<string, TokenMetadata>> {
    const results = new Map<string, TokenMetadata>();

    // Process in parallel but limit concurrency
    const batchSize = 10;
    for (let i = 0; i < mintAddresses.length; i += batchSize) {
      const batch = mintAddresses.slice(i, i + batchSize);
      const promises = batch.map(async (address) => {
        const metadata = await this.getTokenMetadata(address);
        if (metadata) {
          results.set(address, metadata);
        }
      });

      await Promise.all(promises);
    }

    return results;
  }

  /**
   * Check if token is a known meme coin
   */
  isMemeCoin(metadata: TokenMetadata): boolean {
    if (!metadata.tags) return false;

    const memeTags = ['meme', 'community', 'pump', 'fun'];
    return metadata.tags.some(tag =>
      memeTags.some(memeTag => tag.toLowerCase().includes(memeTag))
    );
  }

  /**
   * Get popular meme coins
   */
  getPopularMemeCoins(): TokenMetadata[] {
    const memeCoins: TokenMetadata[] = [];

    for (const [, token] of this.jupiterTokens) {
      const metadata: TokenMetadata = {
        address: token.address,
        symbol: token.symbol,
        name: token.name,
        decimals: token.decimals,
        logoURI: token.logoURI,
        tags: token.tags
      };

      if (this.isMemeCoin(metadata)) {
        memeCoins.push(metadata);
      }
    }

    return memeCoins.slice(0, 50); // Return top 50
  }
}

// Export singleton instance
export const tokenMetadataService = new TokenMetadataService();

// Export types
export type { TokenMetadata };
