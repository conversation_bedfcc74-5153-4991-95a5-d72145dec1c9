/**
 * WebSocket service for real-time updates
 *
 * Provides real-time communication between backend and frontend:
 * - Portfolio updates
 * - Signal notifications
 * - Trade events
 * - System status updates
 */

import { Namespace, Socket } from 'socket.io';
import { PortfolioCache } from './redis';
import { getQueueStats } from '../jobs/queueManager';
import pino from 'pino';

// Import types (will be available after priceUpdater is created)
interface PortfolioUpdate {
  totalValue: number;
  totalValueSOL: number;
  positions: Array<{
    tokenMint: string;
    tokenSymbol: string;
    currentPrice: number;
    value: number;
    priceChange24h?: number;
  }>;
  lastUpdated: Date;
}

const logger = pino({ name: 'websocket-service' });

interface ClientData {
  userId?: string;
  subscriptions: Set<string>;
  lastActivity: Date;
}

// Store connected clients
const connectedClients = new Map<string, ClientData>();

// Global namespace reference for broadcasting
let globalNamespace: Namespace | null = null;

/**
 * Setup WebSocket event handlers
 */
export function setupWebSocketHandlers(namespace: Namespace, logger: pino.Logger): void {
  // Store namespace reference for broadcasting
  globalNamespace = namespace;
  namespace.on('connection', (socket: Socket) => {
    logger.info(`Client connected: ${socket.id}`);

    // Initialize client data
    connectedClients.set(socket.id, {
      subscriptions: new Set(),
      lastActivity: new Date()
    });

    // Handle client authentication (optional)
    socket.on('authenticate', (data) => {
      const clientData = connectedClients.get(socket.id);
      if (clientData) {
        clientData.userId = data.userId;
        logger.info(`Client ${socket.id} authenticated as ${data.userId}`);
      }
    });

    // Handle subscription to portfolio updates
    socket.on('subscribe:portfolio', () => {
      const clientData = connectedClients.get(socket.id);
      if (clientData) {
        clientData.subscriptions.add('portfolio');
        socket.join('portfolio');
        logger.debug(`Client ${socket.id} subscribed to portfolio updates`);

        // Send current portfolio data immediately
        sendPortfolioUpdate(socket);
      }
    });

    // Handle subscription to signal updates
    socket.on('subscribe:signals', () => {
      const clientData = connectedClients.get(socket.id);
      if (clientData) {
        clientData.subscriptions.add('signals');
        socket.join('signals');
        logger.debug(`Client ${socket.id} subscribed to signal updates`);

        // Send recent signals immediately
        sendRecentSignals(socket);
      }
    });

    // Handle subscription to trade events
    socket.on('subscribe:trades', () => {
      const clientData = connectedClients.get(socket.id);
      if (clientData) {
        clientData.subscriptions.add('trades');
        socket.join('trades');
        logger.debug(`Client ${socket.id} subscribed to trade events`);
      }
    });

    // Handle unsubscription
    socket.on('unsubscribe', (channel: string) => {
      const clientData = connectedClients.get(socket.id);
      if (clientData) {
        clientData.subscriptions.delete(channel);
        socket.leave(channel);
        logger.debug(`Client ${socket.id} unsubscribed from ${channel}`);
      }
    });

    // Handle ping for keepalive
    socket.on('ping', () => {
      const clientData = connectedClients.get(socket.id);
      if (clientData) {
        clientData.lastActivity = new Date();
      }
      socket.emit('pong');
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`Client disconnected: ${socket.id}, reason: ${reason}`);
      connectedClients.delete(socket.id);
    });

    // Send welcome message
    socket.emit('connected', {
      message: 'Connected to Meme Coin Portfolio WebSocket',
      timestamp: new Date().toISOString(),
      clientId: socket.id
    });
  });

  // Start periodic updates
  startPeriodicUpdates(namespace);
}

/**
 * Send current portfolio data to a specific client
 */
async function sendPortfolioUpdate(socket: Socket): Promise<void> {
  try {
    const portfolioData = await PortfolioCache.getPortfolioData();
    if (portfolioData) {
      socket.emit('portfolio:update', {
        data: portfolioData,
        timestamp: new Date().toISOString()
      });
    }
  } catch (error) {
    logger.error('Failed to send portfolio update:', error);
  }
}

/**
 * Send recent signals to a specific client
 */
async function sendRecentSignals(socket: Socket): Promise<void> {
  try {
    const signals = await PortfolioCache.getRecentSignals(10);
    socket.emit('signals:recent', {
      signals,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Failed to send recent signals:', error);
  }
}



/**
 * Broadcast new signal to all subscribed clients
 */
export async function broadcastSignal(namespace: Namespace, signal: any): Promise<void> {
  try {
    // Add signal to cache
    await PortfolioCache.addSignal(signal);

    // Broadcast to subscribed clients
    namespace.to('signals').emit('signal:new', {
      signal,
      timestamp: new Date().toISOString()
    });

    logger.info(`Signal broadcasted: ${signal.type} for ${signal.tokenMint}`);
  } catch (error) {
    logger.error('Failed to broadcast signal:', error);
  }
}

/**
 * Broadcast trade event to all subscribed clients
 */
export function broadcastTradeEvent(namespace: Namespace, tradeEvent: any): void {
  try {
    namespace.to('trades').emit('trade:event', {
      event: tradeEvent,
      timestamp: new Date().toISOString()
    });

    logger.info(`Trade event broadcasted: ${tradeEvent.type}`);
  } catch (error) {
    logger.error('Failed to broadcast trade event:', error);
  }
}

/**
 * Start periodic updates for real-time data
 */
function startPeriodicUpdates(namespace: Namespace): void {
  // Portfolio updates disabled for now

  // System status updates every 60 seconds
  setInterval(async () => {
    try {
      const queueStats = await getQueueStats();
      const systemStatus = {
        queues: queueStats,
        connectedClients: connectedClients.size,
        timestamp: new Date().toISOString()
      };

      namespace.emit('system:status', systemStatus);
    } catch (error) {
      logger.error('Failed to send system status:', error);
    }
  }, 60000);

  // Cleanup inactive clients every 5 minutes
  setInterval(() => {
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);

    for (const [clientId, clientData] of connectedClients.entries()) {
      if (clientData.lastActivity < fiveMinutesAgo) {
        logger.info(`Removing inactive client: ${clientId}`);
        connectedClients.delete(clientId);
      }
    }
  }, 5 * 60 * 1000);
}

/**
 * Get connected clients statistics
 */
export function getConnectedClientsStats(): any {
  const stats = {
    total: connectedClients.size,
    subscriptions: {
      portfolio: 0,
      signals: 0,
      trades: 0
    }
  };

  for (const clientData of connectedClients.values()) {
    if (clientData.subscriptions.has('portfolio')) stats.subscriptions.portfolio++;
    if (clientData.subscriptions.has('signals')) stats.subscriptions.signals++;
    if (clientData.subscriptions.has('trades')) stats.subscriptions.trades++;
  }

  return stats;
}

/**
 * Broadcast portfolio update to all connected clients
 */
export function broadcastPortfolioUpdate(portfolioUpdate: PortfolioUpdate): void {
  if (!globalNamespace) {
    logger.warn('Cannot broadcast portfolio update: namespace not initialized');
    return;
  }

  try {
    // Broadcast to all clients subscribed to portfolio updates
    globalNamespace.emit('portfolio:update', {
      type: 'portfolio_update',
      data: portfolioUpdate,
      timestamp: new Date().toISOString()
    });

    logger.debug(`Portfolio update broadcasted to ${connectedClients.size} clients`);
  } catch (error) {
    logger.error('Failed to broadcast portfolio update:', error);
  }
}

/**
 * Broadcast price update for specific token
 */
export function broadcastTokenPriceUpdate(tokenMint: string, price: number, change24h?: number): void {
  if (!globalNamespace) {
    return;
  }

  try {
    globalNamespace.emit('price:update', {
      type: 'price_update',
      data: {
        tokenMint,
        price,
        change24h,
        timestamp: new Date().toISOString()
      }
    });

    logger.debug(`Price update broadcasted for token ${tokenMint}: $${price}`);
  } catch (error) {
    logger.error('Failed to broadcast price update:', error);
  }
}
