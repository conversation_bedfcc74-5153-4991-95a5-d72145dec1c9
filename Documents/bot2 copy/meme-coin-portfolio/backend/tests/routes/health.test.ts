/**
 * Health routes test suite
 * 
 * Tests the health check endpoints for proper functionality
 */

import request from 'supertest'
import { app } from '../../src/index'

describe('Health Routes', () => {
  describe('GET /health', () => {
    it('should return basic health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200)

      expect(response.body).toHaveProperty('status', 'healthy')
      expect(response.body).toHaveProperty('timestamp')
      expect(response.body).toHaveProperty('service', 'meme-coin-portfolio-backend')
      expect(response.body).toHaveProperty('version', '1.0.0')
    })
  })

  describe('GET /health/detailed', () => {
    it('should return detailed health status', async () => {
      const response = await request(app)
        .get('/health/detailed')
        .expect(200)

      expect(response.body).toHaveProperty('status')
      expect(response.body).toHaveProperty('dependencies')
      expect(response.body.dependencies).toHaveProperty('redis')
      expect(response.body.dependencies).toHaveProperty('solana')
      expect(response.body.dependencies).toHaveProperty('jupiter')
      expect(response.body.dependencies).toHaveProperty('wallet')
    })
  })

  describe('GET /health/ready', () => {
    it('should return readiness status', async () => {
      const response = await request(app)
        .get('/health/ready')

      expect(response.body).toHaveProperty('status')
      expect(response.body).toHaveProperty('timestamp')
    })
  })

  describe('GET /health/live', () => {
    it('should return liveness status', async () => {
      const response = await request(app)
        .get('/health/live')
        .expect(200)

      expect(response.body).toHaveProperty('status', 'alive')
      expect(response.body).toHaveProperty('timestamp')
      expect(response.body).toHaveProperty('uptime')
    })
  })

  describe('GET /health/metrics', () => {
    it('should return system metrics', async () => {
      const response = await request(app)
        .get('/health/metrics')
        .expect(200)

      expect(response.body).toHaveProperty('timestamp')
      expect(response.body).toHaveProperty('uptime')
      expect(response.body).toHaveProperty('memory')
      expect(response.body).toHaveProperty('cpu')
      expect(response.body).toHaveProperty('environment')
    })
  })
})
