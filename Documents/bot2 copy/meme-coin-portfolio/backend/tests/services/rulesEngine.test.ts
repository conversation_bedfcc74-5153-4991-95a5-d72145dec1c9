/**
 * Rules Engine test suite
 * 
 * Tests the trading rules evaluation logic
 */

import { RulesEngine, BotRules, MarketData, Position } from '../../src/rules/rulesEngine'

describe('RulesEngine', () => {
  let rulesEngine: RulesEngine
  let mockRules: BotRules

  beforeEach(() => {
    mockRules = {
      buyRules: {
        newPairDetection: {
          enabled: false,
          dexes: ['raydium', 'orca'],
          buyAmountSOL: 0.1
        },
        priceVelocityTrigger: {
          enabled: true,
          priceChangePercent: 10,
          timeframeSeconds: 300,
          minVolumeSOL: 100
        }
      },
      sellRules: {
        takeProfit: {
          enabled: true,
          percentage: 50
        },
        partialTakeProfit: [
          { thresholdPercent: 50, sellPercent: 25 },
          { thresholdPercent: 100, sellPercent: 25 }
        ],
        stopLoss: {
          enabled: true,
          percentage: 15
        },
        trailingStopLoss: {
          enabled: true,
          percentage: 15
        },
        manualOverride: {
          enabled: true
        }
      },
      riskManagement: {
        slippageControl: {
          enabled: true,
          slippageBps: 50
        },
        transactionPriority: {
          enabled: true,
          priorityFee: 1000
        },
        transactionRetry: {
          enabled: true,
          maxRetries: 3
        },
        preTradeSafetyChecks: {
          minLiquidity: 10000,
          honeypotDetection: true,
          revokedAuthoritiesCheck: true
        }
      }
    }

    rulesEngine = new RulesEngine(mockRules)
  })

  describe('constructor', () => {
    it('should create rules engine with valid rules', () => {
      expect(rulesEngine).toBeInstanceOf(RulesEngine)
    })

    it('should throw error with invalid rules', () => {
      const invalidRules = { ...mockRules }
      delete (invalidRules as any).buyRules

      expect(() => new RulesEngine(invalidRules as any)).toThrow()
    })
  })

  describe('evaluateBuyConditions', () => {
    const mockMarketData: MarketData = {
      tokenMint: 'test_mint',
      price: 0.001,
      volume24h: 150,
      priceChange24h: 15,
      liquidity: 50000,
      timestamp: new Date()
    }

    it('should trigger buy on price velocity', async () => {
      const result = await rulesEngine.evaluateBuyConditions(mockMarketData)

      expect(result.shouldBuy).toBe(true)
      expect(result.reason).toContain('Price velocity trigger')
      expect(result.amount).toBe(0.1)
    })

    it('should not trigger buy when price change is below threshold', async () => {
      const lowChangeData = { ...mockMarketData, priceChange24h: 5 }
      const result = await rulesEngine.evaluateBuyConditions(lowChangeData)

      expect(result.shouldBuy).toBe(false)
      expect(result.reason).toContain('below threshold')
    })

    it('should not trigger buy when volume is below minimum', async () => {
      const lowVolumeData = { ...mockMarketData, volume24h: 50 }
      const result = await rulesEngine.evaluateBuyConditions(lowVolumeData)

      expect(result.shouldBuy).toBe(false)
      expect(result.reason).toContain('below minimum')
    })
  })

  describe('evaluateSellConditions', () => {
    const mockPosition: Position = {
      tokenMint: 'test_mint',
      amount: 1000000,
      entryPrice: 0.001,
      currentPrice: 0.0008, // 20% loss
      entryTime: new Date(Date.now() - 3600000), // 1 hour ago
      highestPrice: 0.0012 // 20% gain at peak
    }

    it('should trigger stop loss when price drops below threshold', async () => {
      const result = await rulesEngine.evaluateSellConditions(mockPosition)

      expect(result.shouldSell).toBe(true)
      expect(result.sellType).toBe('stop_loss')
      expect(result.reason).toContain('Stop loss triggered')
      expect(result.sellAmount).toBe(mockPosition.amount)
    })

    it('should trigger trailing stop when price drops from peak', async () => {
      const profitablePosition = {
        ...mockPosition,
        currentPrice: 0.00102, // Just above entry but below trailing stop from peak
        highestPrice: 0.0015
      }

      const result = await rulesEngine.evaluateSellConditions(profitablePosition)

      expect(result.shouldSell).toBe(true)
      expect(result.sellType).toBe('trailing_stop')
    })

    it('should trigger take profit at target percentage', async () => {
      const profitablePosition = {
        ...mockPosition,
        currentPrice: 0.0015 // 50% profit
      }

      const result = await rulesEngine.evaluateSellConditions(profitablePosition)

      expect(result.shouldSell).toBe(true)
      expect(result.sellType).toBe('take_profit')
    })

    it('should not trigger sell when no conditions are met', async () => {
      const stablePosition = {
        ...mockPosition,
        currentPrice: 0.00095, // Small loss within tolerance
        highestPrice: 0.00105
      }

      const result = await rulesEngine.evaluateSellConditions(stablePosition)

      expect(result.shouldSell).toBe(false)
    })
  })

  describe('performSafetyChecks', () => {
    it('should pass safety checks for valid token', async () => {
      const result = await rulesEngine.performSafetyChecks('valid_token_mint')

      expect(result.passed).toBe(true)
      expect(result.issues).toHaveLength(0)
    })
  })

  describe('getRecommendedSlippage', () => {
    it('should return configured slippage when enabled', () => {
      const slippage = rulesEngine.getRecommendedSlippage()
      expect(slippage).toBe(50)
    })
  })

  describe('getMaxRetries', () => {
    it('should return configured max retries when enabled', () => {
      const retries = rulesEngine.getMaxRetries()
      expect(retries).toBe(3)
    })
  })
})
