/**
 * Jest test setup file
 * 
 * Configures the test environment for the backend tests
 */

import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'silent';
process.env.REDIS_URL = 'redis://localhost:6379/1'; // Use different DB for tests

// Mock external services for testing
jest.mock('../src/services/solanaClient', () => ({
  initializeSolana: jest.fn().mockResolvedValue({
    connection: {},
    wallet: { publicKey: { toString: () => 'mock_wallet_address' } }
  }),
  getSolanaConnection: jest.fn().mockReturnValue({}),
  getWallet: jest.fn().mockReturnValue({ publicKey: { toString: () => 'mock_wallet_address' } }),
  getSolBalance: jest.fn().mockResolvedValue(1.5),
  getTokenBalance: jest.fn().mockResolvedValue(1000000),
  sendAndConfirmTransaction: jest.fn().mockResolvedValue('mock_signature')
}));

jest.mock('../src/services/jupiterClient', () => ({
  jupiterClient: {
    getQuote: jest.fn().mockResolvedValue({
      inputMint: 'So11111111111111111111111111111111111111112',
      outputMint: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
      inAmount: '100000000',
      outAmount: '1000000000',
      priceImpactPct: '0.5',
      slippageBps: 50
    }),
    getSwapTransaction: jest.fn().mockResolvedValue({}),
    getTokenPrice: jest.fn().mockResolvedValue(0.000015),
    getSupportedTokens: jest.fn().mockResolvedValue([])
  }
}));

// Global test timeout
jest.setTimeout(30000);
