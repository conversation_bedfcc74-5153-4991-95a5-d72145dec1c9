version: "3.8"

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    command: "npm run dev"
    ports:
      - "4000:4000"
    depends_on:
      - redis
    env_file: .env
    volumes:
      - ./backend:/app
      - /app/node_modules
    restart: unless-stopped
    environment:
      - NODE_ENV=development

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    command: "npm run dev"
    ports:
      - "3000:3000"
    env_file: .env
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    restart: unless-stopped
    environment:
      - NODE_ENV=development

volumes:
  redis_data:
