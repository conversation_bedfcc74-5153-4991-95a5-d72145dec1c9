"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/usePortfolioStore.ts":
/*!************************************!*\
  !*** ./hooks/usePortfolioStore.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePortfolioStore: () => (/* binding */ usePortfolioStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n * Portfolio state management with Zustand\n *\n * Manages portfolio data including:\n * - Portfolio overview and statistics\n * - Active positions\n * - Trade history\n * - Performance analytics\n */ \n\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';\nconst usePortfolioStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // Initial state\n        portfolio: null,\n        isLoading: false,\n        error: null,\n        lastUpdated: null,\n        // Fetch portfolio data from API\n        fetchPortfolio: async ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"\".concat(API_BASE_URL, \"/api/portfolio\"));\n                const portfolioData = response.data;\n                set({\n                    portfolio: portfolioData,\n                    isLoading: false,\n                    lastUpdated: new Date(),\n                    error: null\n                });\n            } catch (error) {\n                console.error('Failed to fetch portfolio:', error);\n                set({\n                    isLoading: false,\n                    error: error instanceof Error ? error.message : 'Failed to fetch portfolio'\n                });\n            }\n        },\n        // Update portfolio data (used by WebSocket updates)\n        updatePortfolio: (data)=>{\n            set({\n                portfolio: data,\n                lastUpdated: new Date(),\n                error: null\n            });\n        },\n        // Force refresh portfolio data\n        refreshPortfolio: async ()=>{\n            try {\n                // Clear cache on backend\n                await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"\".concat(API_BASE_URL, \"/api/portfolio/refresh\"));\n                // Fetch fresh data\n                await get().fetchPortfolio();\n            } catch (error) {\n                console.error('Failed to refresh portfolio:', error);\n                set({\n                    error: error instanceof Error ? error.message : 'Failed to refresh portfolio'\n                });\n            }\n        },\n        // Clear error state\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        // Update a specific position\n        updatePosition: (positionId, updates)=>{\n            const { portfolio } = get();\n            if (!portfolio) return;\n            const updatedPositions = portfolio.positions.map((position)=>position.id === positionId ? {\n                    ...position,\n                    ...updates\n                } : position);\n            set({\n                portfolio: {\n                    ...portfolio,\n                    positions: updatedPositions\n                },\n                lastUpdated: new Date()\n            });\n        },\n        // Remove a position\n        removePosition: (positionId)=>{\n            const { portfolio } = get();\n            if (!portfolio) return;\n            const updatedPositions = portfolio.positions.filter((position)=>position.id !== positionId);\n            set({\n                portfolio: {\n                    ...portfolio,\n                    positions: updatedPositions,\n                    activePositions: updatedPositions.length\n                },\n                lastUpdated: new Date()\n            });\n        },\n        // Add a new trade to history\n        addTrade: (trade)=>{\n            const { portfolio } = get();\n            if (!portfolio) return;\n            const updatedTrades = [\n                trade,\n                ...portfolio.recentTrades\n            ].slice(0, 50) // Keep last 50 trades\n            ;\n            set({\n                portfolio: {\n                    ...portfolio,\n                    recentTrades: updatedTrades\n                },\n                lastUpdated: new Date()\n            });\n        }\n    }), {\n    name: 'portfolio-store',\n    partialize: (state)=>({\n            portfolio: state.portfolio,\n            lastUpdated: state.lastUpdated\n        })\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/usePortfolioStore.ts\n"));

/***/ })

});