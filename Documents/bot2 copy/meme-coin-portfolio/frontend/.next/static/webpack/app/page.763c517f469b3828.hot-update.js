"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/usePortfolioStore.ts":
/*!************************************!*\
  !*** ./hooks/usePortfolioStore.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePortfolioStore: () => (/* binding */ usePortfolioStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/**\n * Portfolio state management with Zustand\n *\n * Manages portfolio data including:\n * - Portfolio overview and statistics\n * - Active positions\n * - Trade history\n * - Performance analytics\n */ \n\n\nconst API_BASE_URL = \"http://localhost:4000\" || 0;\nconsole.log('API_BASE_URL:', API_BASE_URL);\nconst usePortfolioStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // Initial state\n        portfolio: null,\n        isLoading: false,\n        error: null,\n        lastUpdated: null,\n        // Fetch portfolio data from API\n        fetchPortfolio: async ()=>{\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"\".concat(API_BASE_URL, \"/api/portfolio\"));\n                const portfolioData = response.data;\n                set({\n                    portfolio: portfolioData,\n                    isLoading: false,\n                    lastUpdated: new Date(),\n                    error: null\n                });\n            } catch (error) {\n                console.error('Failed to fetch portfolio:', error);\n                set({\n                    isLoading: false,\n                    error: error instanceof Error ? error.message : 'Failed to fetch portfolio'\n                });\n            }\n        },\n        // Update portfolio data (used by WebSocket updates)\n        updatePortfolio: (data)=>{\n            set({\n                portfolio: data,\n                lastUpdated: new Date(),\n                error: null\n            });\n        },\n        // Force refresh portfolio data\n        refreshPortfolio: async ()=>{\n            try {\n                // Clear cache on backend\n                await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"\".concat(API_BASE_URL, \"/api/portfolio/refresh\"));\n                // Fetch fresh data\n                await get().fetchPortfolio();\n            } catch (error) {\n                console.error('Failed to refresh portfolio:', error);\n                set({\n                    error: error instanceof Error ? error.message : 'Failed to refresh portfolio'\n                });\n            }\n        },\n        // Clear error state\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        // Update a specific position\n        updatePosition: (positionId, updates)=>{\n            const { portfolio } = get();\n            if (!portfolio) return;\n            const updatedPositions = portfolio.positions.map((position)=>position.id === positionId ? {\n                    ...position,\n                    ...updates\n                } : position);\n            set({\n                portfolio: {\n                    ...portfolio,\n                    positions: updatedPositions\n                },\n                lastUpdated: new Date()\n            });\n        },\n        // Remove a position\n        removePosition: (positionId)=>{\n            const { portfolio } = get();\n            if (!portfolio) return;\n            const updatedPositions = portfolio.positions.filter((position)=>position.id !== positionId);\n            set({\n                portfolio: {\n                    ...portfolio,\n                    positions: updatedPositions,\n                    activePositions: updatedPositions.length\n                },\n                lastUpdated: new Date()\n            });\n        },\n        // Add a new trade to history\n        addTrade: (trade)=>{\n            const { portfolio } = get();\n            if (!portfolio) return;\n            const updatedTrades = [\n                trade,\n                ...portfolio.recentTrades\n            ].slice(0, 50) // Keep last 50 trades\n            ;\n            set({\n                portfolio: {\n                    ...portfolio,\n                    recentTrades: updatedTrades\n                },\n                lastUpdated: new Date()\n            });\n        }\n    }), {\n    name: 'portfolio-store',\n    partialize: (state)=>({\n            portfolio: state.portfolio,\n            lastUpdated: state.lastUpdated\n        }),\n    // Handle Date serialization/deserialization\n    onRehydrateStorage: ()=>(state)=>{\n            if ((state === null || state === void 0 ? void 0 : state.lastUpdated) && typeof state.lastUpdated === 'string') {\n                state.lastUpdated = new Date(state.lastUpdated);\n            }\n        }\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/usePortfolioStore.ts\n"));

/***/ })

});