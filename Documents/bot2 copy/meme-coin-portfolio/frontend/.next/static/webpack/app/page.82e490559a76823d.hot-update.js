"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/dashboard/ActivePositions.tsx":
/*!**************************************************!*\
  !*** ./components/dashboard/ActivePositions.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActivePositions: () => (/* binding */ ActivePositions)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_MoreVertical_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,MoreVertical,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_MoreVertical_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,MoreVertical,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_MoreVertical_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,MoreVertical,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_MoreVertical_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,MoreVertical,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_MoreVertical_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,MoreVertical,Target,TrendingDown,TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _hooks_usePortfolioStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/usePortfolioStore */ \"(app-pages-browser)/./hooks/usePortfolioStore.ts\");\n/**\n * Active Positions Component\n *\n * Displays current trading positions with:\n * - Position details and P&L\n * - Exit strategy status\n * - Quick action buttons\n * - Real-time price updates\n */ /* __next_internal_client_entry_do_not_use__ ActivePositions auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ActivePositions() {\n    _s();\n    const { portfolio, isLoading, error } = (0,_hooks_usePortfolioStore__WEBPACK_IMPORTED_MODULE_2__.usePortfolioStore)();\n    const [selectedPosition, setSelectedPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-card rounded-lg border border-border p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-6 bg-muted rounded w-1/4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            ...Array(3)\n                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-16 bg-muted rounded\"\n                            }, i, false, {\n                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    if (!portfolio || portfolio.positions.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-card rounded-lg border border-border p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-bold text-foreground mb-4\",\n                    children: \"Active Positions\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-8 text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_MoreVertical_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            size: 48,\n                            className: \"mx-auto mb-4 opacity-50\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"No active positions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: \"Start trading to see your positions here\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this);\n    }\n    const formatCurrency = (amount)=>{\n        return new Intl.NumberFormat('en-US', {\n            style: 'currency',\n            currency: 'USD',\n            minimumFractionDigits: 2,\n            maximumFractionDigits: 6\n        }).format(amount);\n    };\n    const formatNumber = (num)=>{\n        if (num >= 1000000) {\n            return (num / 1000000).toFixed(2) + 'M';\n        }\n        if (num >= 1000) {\n            return (num / 1000).toFixed(2) + 'K';\n        }\n        return num.toLocaleString();\n    };\n    const getTimeAgo = (timestamp)=>{\n        const now = new Date();\n        const entryTime = new Date(timestamp);\n        const diffMs = now.getTime() - entryTime.getTime();\n        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));\n        const diffDays = Math.floor(diffHours / 24);\n        if (diffDays > 0) {\n            return \"\".concat(diffDays, \"d ago\");\n        }\n        if (diffHours > 0) {\n            return \"\".concat(diffHours, \"h ago\");\n        }\n        return 'Just now';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-card rounded-lg border border-border p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-foreground\",\n                        children: \"Active Positions\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: [\n                            portfolio.positions.length,\n                            \" position\",\n                            portfolio.positions.length !== 1 ? 's' : ''\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"border-b border-border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-left py-3 px-2 text-sm font-medium text-muted-foreground\",\n                                        children: \"Token\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-muted-foreground\",\n                                        children: \"Amount\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-muted-foreground\",\n                                        children: \"Entry Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-muted-foreground\",\n                                        children: \"Current Price\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-muted-foreground\",\n                                        children: \"Value\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-muted-foreground\",\n                                        children: \"P&L\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-right py-3 px-2 text-sm font-medium text-muted-foreground\",\n                                        children: \"Entry Time\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"text-center py-3 px-2 text-sm font-medium text-muted-foreground\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            children: portfolio.positions.map((position)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    className: \"border-b border-border/50 table-row-hover\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    position.logoURI ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: position.logoURI,\n                                                        alt: position.tokenSymbol,\n                                                        className: \"w-8 h-8 rounded-full\",\n                                                        onError: (e)=>{\n                                                            // Fallback to gradient avatar if image fails to load\n                                                            const target = e.target;\n                                                            target.style.display = 'none';\n                                                            const fallback = target.nextElementSibling;\n                                                            if (fallback) fallback.style.display = 'flex';\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 23\n                                                    }, this) : null,\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center \".concat(position.logoURI ? 'hidden' : ''),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-primary-foreground\",\n                                                            children: position.tokenSymbol.slice(0, 2)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-foreground\",\n                                                                children: position.tokenSymbol\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                                lineNumber: 140,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-muted-foreground\",\n                                                                children: position.tokenName\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                                lineNumber: 141,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-foreground\",\n                                                children: formatNumber(position.amount)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: formatCurrency(position.entryPrice)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                lineNumber: 155,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-foreground\",\n                                                children: formatCurrency(position.currentPrice)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-foreground\",\n                                                children: formatCurrency(position.value)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium \".concat(position.pnl.amount >= 0 ? 'text-profit' : 'text-loss'),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-end space-x-1\",\n                                                        children: [\n                                                            position.pnl.amount >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_MoreVertical_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                                lineNumber: 181,\n                                                                columnNumber: 25\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_MoreVertical_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                size: 14\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                                lineNumber: 183,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    position.pnl.amount >= 0 ? '+' : '',\n                                                                    formatCurrency(position.pnl.amount)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                                lineNumber: 185,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs\",\n                                                        children: [\n                                                            \"(\",\n                                                            position.pnl.percentage >= 0 ? '+' : '',\n                                                            position.pnl.percentage.toFixed(2),\n                                                            \"%)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: getTimeAgo(position.entryTime)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"py-4 px-2 text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-1 rounded hover:bg-accent transition-colors\",\n                                                        title: \"View on Solscan\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_MoreVertical_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            size: 14,\n                                                            className: \"text-muted-foreground hover:text-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"p-1 rounded hover:bg-accent transition-colors\",\n                                                        onClick: ()=>setSelectedPosition(selectedPosition === position.id ? null : position.id),\n                                                        title: \"More actions\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_MoreVertical_Target_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                            size: 14,\n                                                            className: \"text-muted-foreground hover:text-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, position.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 pt-6 border-t border-border\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-sm font-medium text-foreground mb-3\",\n                        children: \"Exit Strategy Status\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-accent/50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Stop Loss\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-foreground\",\n                                        children: \"15% Active\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-accent/50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Take Profit\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-foreground\",\n                                        children: \"4 Levels\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-3 bg-accent/50 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Trailing Stop\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium text-foreground\",\n                                        children: \"15% Active\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n                lineNumber: 231,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/dashboard/ActivePositions.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n_s(ActivePositions, \"AkadSpixWR5gJmsGpxyIVkEiAig=\", false, function() {\n    return [\n        _hooks_usePortfolioStore__WEBPACK_IMPORTED_MODULE_2__.usePortfolioStore\n    ];\n});\n_c = ActivePositions;\nvar _c;\n$RefreshReg$(_c, \"ActivePositions\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/ActivePositions.tsx\n"));

/***/ })

});