"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Sidebar */ \"(app-pages-browser)/./components/layout/Sidebar.tsx\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Header */ \"(app-pages-browser)/./components/layout/Header.tsx\");\n/* harmony import */ var _components_dashboard_PortfolioOverview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/PortfolioOverview */ \"(app-pages-browser)/./components/dashboard/PortfolioOverview.tsx\");\n/* harmony import */ var _components_dashboard_ActivePositions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/ActivePositions */ \"(app-pages-browser)/./components/dashboard/ActivePositions.tsx\");\n/* harmony import */ var _components_trading_TradingPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/trading/TradingPanel */ \"(app-pages-browser)/./components/trading/TradingPanel.tsx\");\n/* harmony import */ var _components_signals_SignalPanel__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/signals/SignalPanel */ \"(app-pages-browser)/./components/signals/SignalPanel.tsx\");\n/* harmony import */ var _hooks_usePortfolioStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/usePortfolioStore */ \"(app-pages-browser)/./hooks/usePortfolioStore.ts\");\n/* harmony import */ var _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useWebSocket */ \"(app-pages-browser)/./hooks/useWebSocket.ts\");\n/**\n * Main dashboard page\n *\n * The primary interface for the meme coin portfolio application featuring:\n * - Portfolio overview and statistics\n * - Real-time position tracking\n * - Trading panel\n * - Signal monitoring\n */ /* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    _s();\n    const { fetchPortfolio } = (0,_hooks_usePortfolioStore__WEBPACK_IMPORTED_MODULE_8__.usePortfolioStore)();\n    const { connect, isConnected } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_9__.useWebSocket)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            // Initialize data and connections\n            fetchPortfolio();\n            connect();\n        }\n    }[\"DashboardPage.useEffect\"], [\n        fetchPortfolio,\n        connect\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Sidebar__WEBPACK_IMPORTED_MODULE_2__.Sidebar, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_3__.Header, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"container mx-auto px-6 py-6\",\n                            children: [\n                                !isConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4 p-3 bg-yellow-500/20 border border-yellow-500/50 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-yellow-400 text-sm\",\n                                        children: \"⚠️ Disconnected from real-time updates. Attempting to reconnect...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 xl:grid-cols-4 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"xl:col-span-3 space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_PortfolioOverview__WEBPACK_IMPORTED_MODULE_4__.PortfolioOverview, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_ActivePositions__WEBPACK_IMPORTED_MODULE_5__.ActivePositions, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                                                    lineNumber: 64,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"xl:hidden\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trading_TradingPanel__WEBPACK_IMPORTED_MODULE_6__.TradingPanel, {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"xl:col-span-1 space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"hidden xl:block\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_trading_TradingPanel__WEBPACK_IMPORTED_MODULE_6__.TradingPanel, {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                                                        lineNumber: 76,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_signals_SignalPanel__WEBPACK_IMPORTED_MODULE_7__.SignalPanel, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                                                    lineNumber: 80,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/app/page.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"guw01wdJPYxFgT3BmFUvBz6NLlM=\", false, function() {\n    return [\n        _hooks_usePortfolioStore__WEBPACK_IMPORTED_MODULE_8__.usePortfolioStore,\n        _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_9__.useWebSocket\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});