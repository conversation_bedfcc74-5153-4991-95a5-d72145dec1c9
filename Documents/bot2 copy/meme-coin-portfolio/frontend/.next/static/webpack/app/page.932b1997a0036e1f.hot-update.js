"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/usePortfolioStore.ts":
/*!************************************!*\
  !*** ./hooks/usePortfolioStore.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePortfolioStore: () => (/* binding */ usePortfolioStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/**\n * Portfolio state management with Zustand\n *\n * Manages portfolio data including:\n * - Portfolio overview and statistics\n * - Active positions\n * - Trade history\n * - Performance analytics\n */ \n\n\nconst API_BASE_URL = \"http://localhost:4000\" || 0;\nconst usePortfolioStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // Initial state\n        portfolio: null,\n        isLoading: false,\n        error: null,\n        lastUpdated: null,\n        // Fetch portfolio data from API\n        fetchPortfolio: async ()=>{\n            console.log('Fetching portfolio from:', \"\".concat(API_BASE_URL, \"/api/portfolio\"));\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"\".concat(API_BASE_URL, \"/api/portfolio\"));\n                const portfolioData = response.data;\n                console.log('Portfolio data received:', portfolioData);\n                set({\n                    portfolio: portfolioData,\n                    isLoading: false,\n                    lastUpdated: new Date(),\n                    error: null\n                });\n            } catch (error) {\n                console.error('Failed to fetch portfolio:', error);\n                set({\n                    isLoading: false,\n                    error: error instanceof Error ? error.message : 'Failed to fetch portfolio'\n                });\n            }\n        },\n        // Update portfolio data (used by WebSocket updates)\n        updatePortfolio: (data)=>{\n            set({\n                portfolio: data,\n                lastUpdated: new Date(),\n                error: null\n            });\n        },\n        // Force refresh portfolio data\n        refreshPortfolio: async ()=>{\n            try {\n                // Clear cache on backend\n                await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"\".concat(API_BASE_URL, \"/api/portfolio/refresh\"));\n                // Fetch fresh data\n                await get().fetchPortfolio();\n            } catch (error) {\n                console.error('Failed to refresh portfolio:', error);\n                set({\n                    error: error instanceof Error ? error.message : 'Failed to refresh portfolio'\n                });\n            }\n        },\n        // Clear error state\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        // Update a specific position\n        updatePosition: (positionId, updates)=>{\n            const { portfolio } = get();\n            if (!portfolio) return;\n            const updatedPositions = portfolio.positions.map((position)=>position.id === positionId ? {\n                    ...position,\n                    ...updates\n                } : position);\n            set({\n                portfolio: {\n                    ...portfolio,\n                    positions: updatedPositions\n                },\n                lastUpdated: new Date()\n            });\n        },\n        // Remove a position\n        removePosition: (positionId)=>{\n            const { portfolio } = get();\n            if (!portfolio) return;\n            const updatedPositions = portfolio.positions.filter((position)=>position.id !== positionId);\n            set({\n                portfolio: {\n                    ...portfolio,\n                    positions: updatedPositions,\n                    activePositions: updatedPositions.length\n                },\n                lastUpdated: new Date()\n            });\n        },\n        // Add a new trade to history\n        addTrade: (trade)=>{\n            const { portfolio } = get();\n            if (!portfolio) return;\n            const updatedTrades = [\n                trade,\n                ...portfolio.recentTrades\n            ].slice(0, 50) // Keep last 50 trades\n            ;\n            set({\n                portfolio: {\n                    ...portfolio,\n                    recentTrades: updatedTrades\n                },\n                lastUpdated: new Date()\n            });\n        }\n    }), {\n    name: 'portfolio-store',\n    partialize: (state)=>({\n            portfolio: state.portfolio,\n            lastUpdated: state.lastUpdated\n        }),\n    // Handle Date serialization/deserialization\n    onRehydrateStorage: ()=>(state)=>{\n            if ((state === null || state === void 0 ? void 0 : state.lastUpdated) && typeof state.lastUpdated === 'string') {\n                state.lastUpdated = new Date(state.lastUpdated);\n            }\n        }\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/usePortfolioStore.ts\n"));

/***/ })

});