"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/layout/Header.tsx":
/*!**************************************!*\
  !*** ./components/layout/Header.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bell_RefreshCw_Settings_Wallet_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,RefreshCw,Settings,Wallet,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_RefreshCw_Settings_Wallet_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,RefreshCw,Settings,Wallet,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_RefreshCw_Settings_Wallet_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,RefreshCw,Settings,Wallet,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_RefreshCw_Settings_Wallet_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,RefreshCw,Settings,Wallet,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_RefreshCw_Settings_Wallet_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,RefreshCw,Settings,Wallet,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_RefreshCw_Settings_Wallet_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,RefreshCw,Settings,Wallet,Wifi,WifiOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _hooks_usePortfolioStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/usePortfolioStore */ \"(app-pages-browser)/./hooks/usePortfolioStore.ts\");\n/* harmony import */ var _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useWebSocket */ \"(app-pages-browser)/./hooks/useWebSocket.ts\");\n/**\n * Header Component\n *\n * Top navigation bar featuring:\n * - Connection status\n * - Wallet information\n * - Quick actions\n * - Real-time indicators\n */ /* __next_internal_client_entry_do_not_use__ Header auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Header() {\n    _s();\n    const { portfolio, isLoading, refreshPortfolio, lastUpdated } = (0,_hooks_usePortfolioStore__WEBPACK_IMPORTED_MODULE_2__.usePortfolioStore)();\n    const { isConnected } = (0,_hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_3__.useWebSocket)();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    // Update current time every second\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const timer = setInterval({\n                \"Header.useEffect.timer\": ()=>{\n                    setCurrentTime(new Date());\n                }\n            }[\"Header.useEffect.timer\"], 1000);\n            return ({\n                \"Header.useEffect\": ()=>clearInterval(timer)\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    const formatTime = (date)=>{\n        return date.toLocaleTimeString('en-US', {\n            hour12: false,\n            hour: '2-digit',\n            minute: '2-digit',\n            second: '2-digit'\n        });\n    };\n    const formatLastUpdated = (date)=>{\n        if (!date) return 'Never';\n        // Convert string to Date if needed (happens when rehydrating from localStorage)\n        const dateObj = typeof date === 'string' ? new Date(date) : date;\n        // Check if the date is valid\n        if (isNaN(dateObj.getTime())) return 'Invalid date';\n        const now = new Date();\n        const diff = now.getTime() - dateObj.getTime();\n        const seconds = Math.floor(diff / 1000);\n        if (seconds < 60) return \"\".concat(seconds, \"s ago\");\n        if (seconds < 3600) return \"\".concat(Math.floor(seconds / 60), \"m ago\");\n        return dateObj.toLocaleTimeString('en-US', {\n            hour12: false\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-card border-b border-border px-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_Settings_Wallet_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                        lineNumber: 67,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_Settings_Wallet_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium \".concat(isConnected ? 'text-green-500' : 'text-red-500'),\n                                        children: isConnected ? 'Connected' : 'Disconnected'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-sm text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Last updated:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: formatLastUpdated(lastUpdated)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: refreshPortfolio,\n                                disabled: isLoading,\n                                className: \"\\n              p-2 rounded-lg border border-border hover:bg-accent transition-colors\\n              \".concat(isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary/50', \"\\n            \"),\n                                title: \"Refresh portfolio data\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_Settings_Wallet_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 16,\n                                    className: \"\".concat(isLoading ? 'animate-spin' : '', \" text-muted-foreground hover:text-foreground\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-8\",\n                        children: portfolio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Total Value\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                            lineNumber: 109,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-bold text-foreground\",\n                                            children: [\n                                                \"$\",\n                                                portfolio.totalValue.toLocaleString()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"24h P&L\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-bold \".concat(portfolio.pnl24h.amount >= 0 ? 'text-profit' : 'text-loss'),\n                                            children: [\n                                                portfolio.pnl24h.amount >= 0 ? '+' : '',\n                                                \"$\",\n                                                portfolio.pnl24h.amount.toFixed(2),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm ml-1\",\n                                                    children: [\n                                                        \"(\",\n                                                        portfolio.pnl24h.percentage >= 0 ? '+' : '',\n                                                        portfolio.pnl24h.percentage.toFixed(2),\n                                                        \"%)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"Positions\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-lg font-bold text-foreground\",\n                                            children: portfolio.activePositions\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden sm:block text-sm text-muted-foreground\",\n                                children: formatTime(currentTime)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this),\n                            portfolio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 px-3 py-2 bg-accent rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_Settings_Wallet_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium text-foreground\",\n                                                children: [\n                                                    portfolio.availableBalance.toFixed(3),\n                                                    \" SOL\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: \"Available\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-2 rounded-lg border border-border hover:bg-accent transition-colors relative\",\n                                title: \"Notifications\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_Settings_Wallet_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 16,\n                                        className: \"text-muted-foreground hover:text-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"p-2 rounded-lg border border-border hover:bg-accent transition-colors\",\n                                title: \"Settings\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_RefreshCw_Settings_Wallet_Wifi_WifiOff_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    size: 16,\n                                    className: \"text-muted-foreground hover:text-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden mt-4 pt-4 border-t border-border\",\n                children: portfolio && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-3 gap-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: \"Total Value\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-bold text-foreground\",\n                                    children: [\n                                        \"$\",\n                                        portfolio.totalValue.toLocaleString()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: \"24h P&L\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-bold \".concat(portfolio.pnl24h.amount >= 0 ? 'text-profit' : 'text-loss'),\n                                    children: [\n                                        portfolio.pnl24h.amount >= 0 ? '+' : '',\n                                        \"$\",\n                                        portfolio.pnl24h.amount.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: \"Positions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm font-bold text-foreground\",\n                                    children: portfolio.activePositions\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/bot2 copy/meme-coin-portfolio/frontend/components/layout/Header.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n_s(Header, \"50/b8uLyqkx/2s5ouW9jpCGs3F0=\", false, function() {\n    return [\n        _hooks_usePortfolioStore__WEBPACK_IMPORTED_MODULE_2__.usePortfolioStore,\n        _hooks_useWebSocket__WEBPACK_IMPORTED_MODULE_3__.useWebSocket\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/layout/Header.tsx\n"));

/***/ })

});