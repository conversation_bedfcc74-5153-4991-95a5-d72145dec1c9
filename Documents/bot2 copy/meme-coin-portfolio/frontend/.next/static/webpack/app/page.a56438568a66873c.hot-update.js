"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/useWebSocket.ts":
/*!*******************************!*\
  !*** ./hooks/useWebSocket.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var _usePortfolioStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./usePortfolioStore */ \"(app-pages-browser)/./hooks/usePortfolioStore.ts\");\n/* harmony import */ var _useSignalStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useSignalStore */ \"(app-pages-browser)/./hooks/useSignalStore.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n * WebSocket connection hook\n *\n * Manages real-time communication with the backend:\n * - Portfolio updates\n * - Signal notifications\n * - Trade events\n * - Connection management\n */ \n\n\n\nconst WS_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:4000';\nconst useWebSocket = ()=>{\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const socketRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectAttempts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const maxReconnectAttempts = 5;\n    // Store actions\n    const { updatePortfolio, addTrade } = (0,_usePortfolioStore__WEBPACK_IMPORTED_MODULE_2__.usePortfolioStore)();\n    const { addSignal, updateSignalStatus } = (0,_useSignalStore__WEBPACK_IMPORTED_MODULE_3__.useSignalStore)();\n    // Connect to WebSocket\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocket.useCallback[connect]\": ()=>{\n            var _socketRef_current;\n            if ((_socketRef_current = socketRef.current) === null || _socketRef_current === void 0 ? void 0 : _socketRef_current.connected) {\n                return;\n            }\n            console.log('Connecting to WebSocket:', WS_URL);\n            const socket = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_1__.io)(\"\".concat(WS_URL, \"/ws\"), {\n                transports: [\n                    'websocket',\n                    'polling'\n                ],\n                timeout: 20000,\n                reconnection: false\n            });\n            socketRef.current = socket;\n            // Connection events\n            socket.on('connect', {\n                \"useWebSocket.useCallback[connect]\": ()=>{\n                    console.log('WebSocket connected');\n                    setIsConnected(true);\n                    reconnectAttempts.current = 0;\n                    // Subscribe to default channels\n                    socket.emit('subscribe:portfolio');\n                    socket.emit('subscribe:signals');\n                    socket.emit('subscribe:trades');\n                }\n            }[\"useWebSocket.useCallback[connect]\"]);\n            socket.on('disconnect', {\n                \"useWebSocket.useCallback[connect]\": (reason)=>{\n                    console.log('WebSocket disconnected:', reason);\n                    setIsConnected(false);\n                    // Attempt to reconnect unless it was a manual disconnect\n                    if (reason !== 'io client disconnect') {\n                        scheduleReconnect();\n                    }\n                }\n            }[\"useWebSocket.useCallback[connect]\"]);\n            socket.on('connect_error', {\n                \"useWebSocket.useCallback[connect]\": (error)=>{\n                    console.error('WebSocket connection error:', error);\n                    setIsConnected(false);\n                    scheduleReconnect();\n                }\n            }[\"useWebSocket.useCallback[connect]\"]);\n            // Portfolio updates\n            socket.on('portfolio:update', {\n                \"useWebSocket.useCallback[connect]\": (data)=>{\n                    console.log('📊 Real-time portfolio update received:', data);\n                    if (data.data) {\n                        // Get current portfolio to merge with real-time updates\n                        const currentPortfolio = _usePortfolioStore__WEBPACK_IMPORTED_MODULE_2__.usePortfolioStore.getState().portfolio;\n                        if (currentPortfolio) {\n                            // Update positions with new prices\n                            const updatedPositions = currentPortfolio.positions.map({\n                                \"useWebSocket.useCallback[connect].updatedPositions\": (position)=>{\n                                    const updatedPosition = data.data.positions.find({\n                                        \"useWebSocket.useCallback[connect].updatedPositions.updatedPosition\": (p)=>p.tokenMint === position.tokenMint\n                                    }[\"useWebSocket.useCallback[connect].updatedPositions.updatedPosition\"]);\n                                    if (updatedPosition) {\n                                        return {\n                                            ...position,\n                                            currentPrice: updatedPosition.currentPrice,\n                                            value: updatedPosition.value,\n                                            priceChange24h: updatedPosition.priceChange24h || 0\n                                        };\n                                    }\n                                    return position;\n                                }\n                            }[\"useWebSocket.useCallback[connect].updatedPositions\"]);\n                            // Recalculate asset allocation with new values\n                            const totalValue = data.data.totalValue;\n                            const updatedAssetAllocation = currentPortfolio.assetAllocation.map({\n                                \"useWebSocket.useCallback[connect].updatedAssetAllocation\": (asset)=>{\n                                    if (asset.name === 'SOL') {\n                                        const solPriceUSD = 180; // Should match backend\n                                        const solValue = data.data.totalValueSOL * solPriceUSD;\n                                        return {\n                                            ...asset,\n                                            value: solValue,\n                                            percentage: Math.round(solValue / totalValue * 100)\n                                        };\n                                    } else {\n                                        // Find corresponding position\n                                        const position = updatedPositions.find({\n                                            \"useWebSocket.useCallback[connect].updatedAssetAllocation.position\": (p)=>p.tokenSymbol === asset.name\n                                        }[\"useWebSocket.useCallback[connect].updatedAssetAllocation.position\"]);\n                                        if (position) {\n                                            return {\n                                                ...asset,\n                                                value: position.value,\n                                                percentage: Math.round(position.value / totalValue * 100)\n                                            };\n                                        }\n                                    }\n                                    return asset;\n                                }\n                            }[\"useWebSocket.useCallback[connect].updatedAssetAllocation\"]);\n                            // Update portfolio with real-time data\n                            const updatedPortfolio = {\n                                ...currentPortfolio,\n                                totalValue: data.data.totalValue,\n                                totalValueSOL: data.data.totalValueSOL,\n                                positions: updatedPositions,\n                                assetAllocation: updatedAssetAllocation\n                            };\n                            updatePortfolio(updatedPortfolio);\n                        }\n                    }\n                }\n            }[\"useWebSocket.useCallback[connect]\"]);\n            // Signal updates\n            socket.on('signal:new', {\n                \"useWebSocket.useCallback[connect]\": (data)=>{\n                    console.log('New signal received:', data);\n                    if (data.signal) {\n                        addSignal(data.signal);\n                        // Show notification for important signals\n                        if (data.signal.type === 'volume_spike' && data.signal.value > 10) {\n                            showNotification('High Volume Signal', \"\".concat(data.signal.tokenSymbol, \" volume spike detected!\"));\n                        }\n                    }\n                }\n            }[\"useWebSocket.useCallback[connect]\"]);\n            socket.on('signals:recent', {\n                \"useWebSocket.useCallback[connect]\": (data)=>{\n                    console.log('Recent signals received:', data);\n                    if (data.signals) {\n                        data.signals.forEach({\n                            \"useWebSocket.useCallback[connect]\": (signal)=>addSignal(signal)\n                        }[\"useWebSocket.useCallback[connect]\"]);\n                    }\n                }\n            }[\"useWebSocket.useCallback[connect]\"]);\n            // Trade events\n            socket.on('trade:event', {\n                \"useWebSocket.useCallback[connect]\": (data)=>{\n                    console.log('Trade event received:', data);\n                    if (data.event) {\n                        const { event } = data;\n                        switch(event.type){\n                            case 'trade_completed':\n                                addTrade(event.trade);\n                                showNotification('Trade Completed', \"\".concat(event.trade.type, \" order executed successfully\"));\n                                break;\n                            case 'trade_failed':\n                                showNotification('Trade Failed', event.message, 'error');\n                                break;\n                            case 'stop_loss_triggered':\n                                showNotification('Stop Loss Triggered', \"Position closed: \".concat(event.tokenSymbol), 'warning');\n                                break;\n                            case 'take_profit_hit':\n                                showNotification('Take Profit Hit', \"Profit locked: \".concat(event.tokenSymbol), 'success');\n                                break;\n                        }\n                    }\n                }\n            }[\"useWebSocket.useCallback[connect]\"]);\n            // System status updates\n            socket.on('system:status', {\n                \"useWebSocket.useCallback[connect]\": (data)=>{\n                    console.log('System status:', data);\n                // Could be used to show system health indicators\n                }\n            }[\"useWebSocket.useCallback[connect]\"]);\n            // Handle ping/pong for keepalive\n            socket.on('pong', {\n                \"useWebSocket.useCallback[connect]\": ()=>{\n                // Connection is alive\n                }\n            }[\"useWebSocket.useCallback[connect]\"]);\n            // Send periodic ping\n            const pingInterval = setInterval({\n                \"useWebSocket.useCallback[connect].pingInterval\": ()=>{\n                    if (socket.connected) {\n                        socket.emit('ping');\n                    }\n                }\n            }[\"useWebSocket.useCallback[connect].pingInterval\"], 30000) // Every 30 seconds\n            ;\n            // Cleanup interval on disconnect\n            socket.on('disconnect', {\n                \"useWebSocket.useCallback[connect]\": ()=>{\n                    clearInterval(pingInterval);\n                }\n            }[\"useWebSocket.useCallback[connect]\"]);\n        }\n    }[\"useWebSocket.useCallback[connect]\"], [\n        updatePortfolio,\n        addSignal,\n        addTrade\n    ]);\n    // Disconnect from WebSocket\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocket.useCallback[disconnect]\": ()=>{\n            if (reconnectTimeoutRef.current) {\n                clearTimeout(reconnectTimeoutRef.current);\n                reconnectTimeoutRef.current = null;\n            }\n            if (socketRef.current) {\n                socketRef.current.disconnect();\n                socketRef.current = null;\n            }\n            setIsConnected(false);\n        }\n    }[\"useWebSocket.useCallback[disconnect]\"], []);\n    // Schedule reconnection with exponential backoff\n    const scheduleReconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocket.useCallback[scheduleReconnect]\": ()=>{\n            if (reconnectAttempts.current >= maxReconnectAttempts) {\n                console.log('Max reconnection attempts reached');\n                return;\n            }\n            if (reconnectTimeoutRef.current) {\n                clearTimeout(reconnectTimeoutRef.current);\n            }\n            const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000) // Max 30 seconds\n            ;\n            console.log(\"Scheduling reconnect in \".concat(delay, \"ms (attempt \").concat(reconnectAttempts.current + 1, \")\"));\n            reconnectTimeoutRef.current = setTimeout({\n                \"useWebSocket.useCallback[scheduleReconnect]\": ()=>{\n                    reconnectAttempts.current++;\n                    connect();\n                }\n            }[\"useWebSocket.useCallback[scheduleReconnect]\"], delay);\n        }\n    }[\"useWebSocket.useCallback[scheduleReconnect]\"], [\n        connect\n    ]);\n    // Subscribe to a channel\n    const subscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocket.useCallback[subscribe]\": (channel)=>{\n            var _socketRef_current;\n            if ((_socketRef_current = socketRef.current) === null || _socketRef_current === void 0 ? void 0 : _socketRef_current.connected) {\n                socketRef.current.emit(\"subscribe:\".concat(channel));\n            }\n        }\n    }[\"useWebSocket.useCallback[subscribe]\"], []);\n    // Unsubscribe from a channel\n    const unsubscribe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWebSocket.useCallback[unsubscribe]\": (channel)=>{\n            var _socketRef_current;\n            if ((_socketRef_current = socketRef.current) === null || _socketRef_current === void 0 ? void 0 : _socketRef_current.connected) {\n                socketRef.current.emit('unsubscribe', channel);\n            }\n        }\n    }[\"useWebSocket.useCallback[unsubscribe]\"], []);\n    // Show browser notification\n    const showNotification = function(title, body) {\n        let type = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'info';\n        // Check if notifications are supported and permitted\n        if ('Notification' in window && Notification.permission === 'granted') {\n            new Notification(title, {\n                body,\n                icon: '/favicon.ico',\n                tag: type // Prevents duplicate notifications\n            });\n        }\n    };\n    // Request notification permission on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWebSocket.useEffect\": ()=>{\n            if ('Notification' in window && Notification.permission === 'default') {\n                Notification.requestPermission();\n            }\n        }\n    }[\"useWebSocket.useEffect\"], []);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWebSocket.useEffect\": ()=>{\n            return ({\n                \"useWebSocket.useEffect\": ()=>{\n                    disconnect();\n                }\n            })[\"useWebSocket.useEffect\"];\n        }\n    }[\"useWebSocket.useEffect\"], [\n        disconnect\n    ]);\n    return {\n        isConnected,\n        connect,\n        disconnect,\n        subscribe,\n        unsubscribe\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useWebSocket.ts\n"));

/***/ })

});