"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./hooks/usePortfolioStore.ts":
/*!************************************!*\
  !*** ./hooks/usePortfolioStore.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   usePortfolioStore: () => (/* binding */ usePortfolioStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(app-pages-browser)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(app-pages-browser)/./node_modules/zustand/esm/middleware.mjs\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/**\n * Portfolio state management with Zustand\n *\n * Manages portfolio data including:\n * - Portfolio overview and statistics\n * - Active positions\n * - Trade history\n * - Performance analytics\n */ \n\n\nconst API_BASE_URL = \"http://localhost:4000\" || 0;\nconsole.log('API_BASE_URL:', API_BASE_URL);\nconst usePortfolioStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        // Initial state\n        portfolio: null,\n        isLoading: false,\n        error: null,\n        lastUpdated: null,\n        // Fetch portfolio data from API\n        fetchPortfolio: async ()=>{\n            console.log('Fetching portfolio from:', \"\".concat(API_BASE_URL, \"/api/portfolio\"));\n            set({\n                isLoading: true,\n                error: null\n            });\n            try {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].get(\"\".concat(API_BASE_URL, \"/api/portfolio\"));\n                const portfolioData = response.data;\n                console.log('Portfolio data received:', portfolioData);\n                set({\n                    portfolio: portfolioData,\n                    isLoading: false,\n                    lastUpdated: new Date(),\n                    error: null\n                });\n            } catch (error) {\n                console.error('Failed to fetch portfolio:', error);\n                set({\n                    isLoading: false,\n                    error: error instanceof Error ? error.message : 'Failed to fetch portfolio'\n                });\n            }\n        },\n        // Update portfolio data (used by WebSocket updates)\n        updatePortfolio: (data)=>{\n            set({\n                portfolio: data,\n                lastUpdated: new Date(),\n                error: null\n            });\n        },\n        // Force refresh portfolio data\n        refreshPortfolio: async ()=>{\n            try {\n                // Clear cache on backend\n                await axios__WEBPACK_IMPORTED_MODULE_2__[\"default\"].post(\"\".concat(API_BASE_URL, \"/api/portfolio/refresh\"));\n                // Fetch fresh data\n                await get().fetchPortfolio();\n            } catch (error) {\n                console.error('Failed to refresh portfolio:', error);\n                set({\n                    error: error instanceof Error ? error.message : 'Failed to refresh portfolio'\n                });\n            }\n        },\n        // Clear error state\n        clearError: ()=>{\n            set({\n                error: null\n            });\n        },\n        // Update a specific position\n        updatePosition: (positionId, updates)=>{\n            const { portfolio } = get();\n            if (!portfolio) return;\n            const updatedPositions = portfolio.positions.map((position)=>position.id === positionId ? {\n                    ...position,\n                    ...updates\n                } : position);\n            set({\n                portfolio: {\n                    ...portfolio,\n                    positions: updatedPositions\n                },\n                lastUpdated: new Date()\n            });\n        },\n        // Remove a position\n        removePosition: (positionId)=>{\n            const { portfolio } = get();\n            if (!portfolio) return;\n            const updatedPositions = portfolio.positions.filter((position)=>position.id !== positionId);\n            set({\n                portfolio: {\n                    ...portfolio,\n                    positions: updatedPositions,\n                    activePositions: updatedPositions.length\n                },\n                lastUpdated: new Date()\n            });\n        },\n        // Add a new trade to history\n        addTrade: (trade)=>{\n            const { portfolio } = get();\n            if (!portfolio) return;\n            const updatedTrades = [\n                trade,\n                ...portfolio.recentTrades\n            ].slice(0, 50) // Keep last 50 trades\n            ;\n            set({\n                portfolio: {\n                    ...portfolio,\n                    recentTrades: updatedTrades\n                },\n                lastUpdated: new Date()\n            });\n        }\n    }), {\n    name: 'portfolio-store',\n    partialize: (state)=>({\n            portfolio: state.portfolio,\n            lastUpdated: state.lastUpdated\n        }),\n    // Handle Date serialization/deserialization\n    onRehydrateStorage: ()=>(state)=>{\n            if ((state === null || state === void 0 ? void 0 : state.lastUpdated) && typeof state.lastUpdated === 'string') {\n                state.lastUpdated = new Date(state.lastUpdated);\n            }\n        }\n}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/usePortfolioStore.ts\n"));

/***/ })

});