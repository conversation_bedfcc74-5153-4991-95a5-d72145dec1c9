@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
    min-height: 100vh;
  }
}

@layer components {
  /* Custom scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #374151 #1f2937;
  }
  
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #1f2937;
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #374151;
    border-radius: 3px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #4b5563;
  }

  /* Profit/Loss colors */
  .profit-text {
    @apply text-profit;
  }
  
  .loss-text {
    @apply text-loss;
  }
  
  .profit-bg {
    @apply bg-profit;
  }
  
  .loss-bg {
    @apply bg-loss;
  }

  /* Animated gradient backgrounds */
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .profit-gradient {
    background: linear-gradient(135deg, #00FF88 0%, #00CC6A 100%);
  }
  
  .loss-gradient {
    background: linear-gradient(135deg, #FF4444 0%, #CC3333 100%);
  }
  
  .action-gradient {
    background: linear-gradient(135deg, #FF8C00 0%, #FF6B00 100%);
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:shadow-primary/10 hover:scale-[1.02];
  }

  /* Pulse animations for real-time data */
  .pulse-profit {
    animation: pulse-profit 2s ease-in-out infinite;
  }
  
  .pulse-loss {
    animation: pulse-loss 2s ease-in-out infinite;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary;
  }

  /* Glassmorphism effect */
  .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Status indicators */
  .status-online {
    @apply bg-green-500 animate-pulse;
  }
  
  .status-offline {
    @apply bg-red-500;
  }
  
  .status-warning {
    @apply bg-yellow-500;
  }

  /* Typography */
  .text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Button variants */
  .btn-profit {
    @apply bg-profit hover:bg-profit/90 text-black font-semibold;
  }
  
  .btn-loss {
    @apply bg-loss hover:bg-loss/90 text-white font-semibold;
  }
  
  .btn-action {
    @apply bg-action hover:bg-action/90 text-white font-semibold;
  }

  /* Progress bars */
  .progress-profit {
    @apply bg-profit;
  }
  
  .progress-loss {
    @apply bg-loss;
  }
  
  .progress-neutral {
    @apply bg-gray-400;
  }

  /* Table styles */
  .table-row-hover {
    @apply hover:bg-white/5 transition-colors duration-200;
  }

  /* Mobile responsive utilities */
  .mobile-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }
  
  .mobile-text {
    @apply text-sm sm:text-base;
  }

  /* Chart container */
  .chart-container {
    @apply bg-card/50 backdrop-blur-sm border border-border rounded-lg p-4;
  }

  /* Signal indicators */
  .signal-new {
    @apply bg-green-500/20 border-green-500/50 text-green-400;
  }
  
  .signal-old {
    @apply bg-gray-500/20 border-gray-500/50 text-gray-400;
  }

  /* Exposure meter */
  .exposure-safe {
    @apply bg-green-500;
  }
  
  .exposure-warning {
    @apply bg-yellow-500;
  }
  
  .exposure-danger {
    @apply bg-red-500;
  }
}
