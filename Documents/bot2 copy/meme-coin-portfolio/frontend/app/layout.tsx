import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Meme Coin Portfolio - Automated Trading Dashboard',
  description: 'Intelligent meme coin trading assistant with automated exit strategies, real-time portfolio tracking, and signal detection.',
  keywords: ['meme coin', 'trading', 'solana', 'defi', 'portfolio', 'automation'],
  authors: [{ name: 'Meme Coin Portfolio Team' }],
  viewport: 'width=device-width, initial-scale=1',
  themeColor: '#0f0f23',
  icons: {
    icon: '/favicon.ico',
    apple: '/apple-touch-icon.png',
  },
  openGraph: {
    title: 'Meme Coin Portfolio - Automated Trading Dashboard',
    description: 'Intelligent meme coin trading assistant with automated exit strategies',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Meme Coin Portfolio',
    description: 'Automated meme coin trading dashboard',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark">
      <body className={`${inter.className} antialiased`}>
        <div className="min-h-screen bg-background">
          {children}
        </div>
      </body>
    </html>
  )
}
