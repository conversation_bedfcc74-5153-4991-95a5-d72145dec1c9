/**
 * Main dashboard page
 * 
 * The primary interface for the meme coin portfolio application featuring:
 * - Portfolio overview and statistics
 * - Real-time position tracking
 * - Trading panel
 * - Signal monitoring
 */

'use client'

import { useEffect } from 'react'
import { Sidebar } from '@/components/layout/Sidebar'
import { Header } from '@/components/layout/Header'
import { PortfolioOverview } from '@/components/dashboard/PortfolioOverview'
import { ActivePositions } from '@/components/dashboard/ActivePositions'
import { TradingPanel } from '@/components/trading/TradingPanel'
import { SignalPanel } from '@/components/signals/SignalPanel'
import { usePortfolioStore } from '@/hooks/usePortfolioStore'
import { useWebSocket } from '@/hooks/useWebSocket'

export default function DashboardPage() {
  const { fetchPortfolio } = usePortfolioStore()
  const { connect, isConnected } = useWebSocket()

  useEffect(() => {
    // Initialize data and connections
    fetchPortfolio()
    connect()
  }, [fetchPortfolio, connect])

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar Navigation */}
      <Sidebar />
      
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header />
        
        {/* Dashboard Content */}
        <main className="flex-1 overflow-auto">
          <div className="container mx-auto px-6 py-6">
            {/* Connection Status */}
            {!isConnected && (
              <div className="mb-4 p-3 bg-yellow-500/20 border border-yellow-500/50 rounded-lg">
                <p className="text-yellow-400 text-sm">
                  ⚠️ Disconnected from real-time updates. Attempting to reconnect...
                </p>
              </div>
            )}
            
            {/* Main Dashboard Grid */}
            <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
              {/* Left Column - Portfolio & Positions */}
              <div className="xl:col-span-3 space-y-6">
                {/* Portfolio Overview */}
                <PortfolioOverview />
                
                {/* Active Positions */}
                <ActivePositions />
                
                {/* Trading Panel - Mobile/Tablet */}
                <div className="xl:hidden">
                  <TradingPanel />
                </div>
              </div>
              
              {/* Right Column - Trading & Signals */}
              <div className="xl:col-span-1 space-y-6">
                {/* Trading Panel - Desktop */}
                <div className="hidden xl:block">
                  <TradingPanel />
                </div>
                
                {/* Signal Panel */}
                <SignalPanel />
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
