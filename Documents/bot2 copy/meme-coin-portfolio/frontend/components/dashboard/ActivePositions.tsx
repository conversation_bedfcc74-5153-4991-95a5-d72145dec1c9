/**
 * Active Positions Component
 * 
 * Displays current trading positions with:
 * - Position details and P&L
 * - Exit strategy status
 * - Quick action buttons
 * - Real-time price updates
 */

'use client'

import { useState } from 'react'
import { TrendingUp, TrendingDown, Target, MoreVertical, ExternalLink } from 'lucide-react'
import { usePortfolioStore, Position } from '@/hooks/usePortfolioStore'

export function ActivePositions() {
  const { portfolio, isLoading } = usePortfolioStore()
  const [selectedPosition, setSelectedPosition] = useState<string | null>(null)

  if (isLoading) {
    return (
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-muted rounded w-1/4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!portfolio || portfolio.positions.length === 0) {
    return (
      <div className="bg-card rounded-lg border border-border p-6">
        <h2 className="text-xl font-bold text-foreground mb-4">Active Positions</h2>
        <div className="text-center py-8 text-muted-foreground">
          <Target size={48} className="mx-auto mb-4 opacity-50" />
          <p>No active positions</p>
          <p className="text-sm">Start trading to see your positions here</p>
        </div>
      </div>
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 6
    }).format(amount)
  }

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(2) + 'M'
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(2) + 'K'
    }
    return num.toLocaleString()
  }

  const getTimeAgo = (timestamp: string) => {
    const now = new Date()
    const entryTime = new Date(timestamp)
    const diffMs = now.getTime() - entryTime.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)

    if (diffDays > 0) {
      return `${diffDays}d ago`
    }
    if (diffHours > 0) {
      return `${diffHours}h ago`
    }
    return 'Just now'
  }

  return (
    <div className="bg-card rounded-lg border border-border p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-bold text-foreground">Active Positions</h2>
        <div className="text-sm text-muted-foreground">
          {portfolio.positions.length} position{portfolio.positions.length !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Positions Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-border">
              <th className="text-left py-3 px-2 text-sm font-medium text-muted-foreground">Token</th>
              <th className="text-right py-3 px-2 text-sm font-medium text-muted-foreground">Amount</th>
              <th className="text-right py-3 px-2 text-sm font-medium text-muted-foreground">Entry Price</th>
              <th className="text-right py-3 px-2 text-sm font-medium text-muted-foreground">Current Price</th>
              <th className="text-right py-3 px-2 text-sm font-medium text-muted-foreground">Value</th>
              <th className="text-right py-3 px-2 text-sm font-medium text-muted-foreground">P&L</th>
              <th className="text-right py-3 px-2 text-sm font-medium text-muted-foreground">Entry Time</th>
              <th className="text-center py-3 px-2 text-sm font-medium text-muted-foreground">Actions</th>
            </tr>
          </thead>
          <tbody>
            {portfolio.positions.map((position) => (
              <tr 
                key={position.id} 
                className="border-b border-border/50 table-row-hover"
              >
                {/* Token */}
                <td className="py-4 px-2">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-primary to-secondary rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-primary-foreground">
                        {position.tokenSymbol.slice(0, 2)}
                      </span>
                    </div>
                    <div>
                      <div className="font-medium text-foreground">{position.tokenSymbol}</div>
                      <div className="text-xs text-muted-foreground">{position.tokenName}</div>
                    </div>
                  </div>
                </td>

                {/* Amount */}
                <td className="py-4 px-2 text-right">
                  <div className="font-medium text-foreground">
                    {formatNumber(position.amount)}
                  </div>
                </td>

                {/* Entry Price */}
                <td className="py-4 px-2 text-right">
                  <div className="text-sm text-muted-foreground">
                    {formatCurrency(position.entryPrice)}
                  </div>
                </td>

                {/* Current Price */}
                <td className="py-4 px-2 text-right">
                  <div className="font-medium text-foreground">
                    {formatCurrency(position.currentPrice)}
                  </div>
                </td>

                {/* Value */}
                <td className="py-4 px-2 text-right">
                  <div className="font-medium text-foreground">
                    {formatCurrency(position.value)}
                  </div>
                </td>

                {/* P&L */}
                <td className="py-4 px-2 text-right">
                  <div className={`font-medium ${
                    position.pnl.amount >= 0 ? 'text-profit' : 'text-loss'
                  }`}>
                    <div className="flex items-center justify-end space-x-1">
                      {position.pnl.amount >= 0 ? (
                        <TrendingUp size={14} />
                      ) : (
                        <TrendingDown size={14} />
                      )}
                      <span>
                        {position.pnl.amount >= 0 ? '+' : ''}
                        {formatCurrency(position.pnl.amount)}
                      </span>
                    </div>
                    <div className="text-xs">
                      ({position.pnl.percentage >= 0 ? '+' : ''}
                      {position.pnl.percentage.toFixed(2)}%)
                    </div>
                  </div>
                </td>

                {/* Entry Time */}
                <td className="py-4 px-2 text-right">
                  <div className="text-sm text-muted-foreground">
                    {getTimeAgo(position.entryTime)}
                  </div>
                </td>

                {/* Actions */}
                <td className="py-4 px-2 text-center">
                  <div className="flex items-center justify-center space-x-2">
                    <button
                      className="p-1 rounded hover:bg-accent transition-colors"
                      title="View on Solscan"
                    >
                      <ExternalLink size={14} className="text-muted-foreground hover:text-foreground" />
                    </button>
                    <button
                      className="p-1 rounded hover:bg-accent transition-colors"
                      onClick={() => setSelectedPosition(
                        selectedPosition === position.id ? null : position.id
                      )}
                      title="More actions"
                    >
                      <MoreVertical size={14} className="text-muted-foreground hover:text-foreground" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Exit Strategy Summary */}
      <div className="mt-6 pt-6 border-t border-border">
        <h3 className="text-sm font-medium text-foreground mb-3">Exit Strategy Status</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="flex items-center justify-between p-3 bg-accent/50 rounded-lg">
            <span className="text-muted-foreground">Stop Loss</span>
            <span className="font-medium text-foreground">15% Active</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-accent/50 rounded-lg">
            <span className="text-muted-foreground">Take Profit</span>
            <span className="font-medium text-foreground">4 Levels</span>
          </div>
          <div className="flex items-center justify-between p-3 bg-accent/50 rounded-lg">
            <span className="text-muted-foreground">Trailing Stop</span>
            <span className="font-medium text-foreground">15% Active</span>
          </div>
        </div>
      </div>
    </div>
  )
}
