/**
 * Portfolio Overview Component
 * 
 * Displays key portfolio metrics including:
 * - Total value and P&L
 * - Exposure meter
 * - Asset allocation chart
 * - Performance indicators
 */

'use client'

import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts'
import { TrendingUp, TrendingDown, DollarSign, Target, AlertTriangle } from 'lucide-react'
import { usePortfolioStore } from '@/hooks/usePortfolioStore'

export function PortfolioOverview() {
  const { portfolio, isLoading } = usePortfolioStore()

  if (isLoading) {
    return (
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-20 bg-muted rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!portfolio) {
    return (
      <div className="bg-card rounded-lg border border-border p-6">
        <div className="text-center text-muted-foreground">
          <AlertTriangle size={48} className="mx-auto mb-4 opacity-50" />
          <p>Unable to load portfolio data</p>
        </div>
      </div>
    )
  }

  const exposurePercentage = (portfolio.exposure.current / portfolio.exposure.limit) * 100
  const exposureColor = exposurePercentage > 80 ? 'text-loss' : 
                       exposurePercentage > 60 ? 'text-yellow-500' : 'text-profit'

  return (
    <div className="bg-card rounded-lg border border-border p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold text-foreground">Portfolio Overview</h2>
        <div className="text-sm text-muted-foreground">
          Last updated: {new Date().toLocaleTimeString()}
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Total Value */}
        <div className="bg-accent/50 rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total Value</p>
              <p className="text-2xl font-bold text-foreground">
                ${portfolio.totalValue.toLocaleString()}
              </p>
              <p className="text-xs text-muted-foreground">
                {portfolio.totalValueSOL.toFixed(3)} SOL
              </p>
            </div>
            <DollarSign className="text-info" size={24} />
          </div>
        </div>

        {/* 24h P&L */}
        <div className="bg-accent/50 rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">24h P&L</p>
              <p className={`text-2xl font-bold ${
                portfolio.pnl24h.amount >= 0 ? 'text-profit' : 'text-loss'
              }`}>
                {portfolio.pnl24h.amount >= 0 ? '+' : ''}
                ${portfolio.pnl24h.amount.toFixed(2)}
              </p>
              <p className={`text-xs ${
                portfolio.pnl24h.percentage >= 0 ? 'text-profit' : 'text-loss'
              }`}>
                {portfolio.pnl24h.percentage >= 0 ? '+' : ''}
                {portfolio.pnl24h.percentage.toFixed(2)}%
              </p>
            </div>
            {portfolio.pnl24h.amount >= 0 ? (
              <TrendingUp className="text-profit" size={24} />
            ) : (
              <TrendingDown className="text-loss" size={24} />
            )}
          </div>
        </div>

        {/* Total P&L */}
        <div className="bg-accent/50 rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total P&L</p>
              <p className={`text-2xl font-bold ${
                portfolio.pnlTotal.amount >= 0 ? 'text-profit' : 'text-loss'
              }`}>
                {portfolio.pnlTotal.amount >= 0 ? '+' : ''}
                ${portfolio.pnlTotal.amount.toFixed(2)}
              </p>
              <p className={`text-xs ${
                portfolio.pnlTotal.percentage >= 0 ? 'text-profit' : 'text-loss'
              }`}>
                {portfolio.pnlTotal.percentage >= 0 ? '+' : ''}
                {portfolio.pnlTotal.percentage.toFixed(2)}%
              </p>
            </div>
            {portfolio.pnlTotal.amount >= 0 ? (
              <TrendingUp className="text-profit" size={24} />
            ) : (
              <TrendingDown className="text-loss" size={24} />
            )}
          </div>
        </div>

        {/* Active Positions */}
        <div className="bg-accent/50 rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Active Positions</p>
              <p className="text-2xl font-bold text-foreground">
                {portfolio.activePositions}
              </p>
              <p className="text-xs text-muted-foreground">
                ${portfolio.totalInvested.toLocaleString()} invested
              </p>
            </div>
            <Target className="text-info" size={24} />
          </div>
        </div>
      </div>

      {/* Exposure Meter and Asset Allocation */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Exposure Meter */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">Capital Exposure</h3>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Used</span>
              <span className={`font-medium ${exposureColor}`}>
                ${portfolio.exposure.current.toLocaleString()} / ${portfolio.exposure.limit.toLocaleString()}
              </span>
            </div>
            
            <div className="w-full bg-muted rounded-full h-3">
              <div 
                className={`h-3 rounded-full transition-all duration-500 ${
                  exposurePercentage > 80 ? 'bg-loss' : 
                  exposurePercentage > 60 ? 'bg-yellow-500' : 'bg-profit'
                }`}
                style={{ width: `${Math.min(exposurePercentage, 100)}%` }}
              ></div>
            </div>
            
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>0%</span>
              <span className={exposureColor}>
                {exposurePercentage.toFixed(1)}%
              </span>
              <span>100%</span>
            </div>
          </div>

          <div className="text-sm text-muted-foreground">
            Available: <span className="font-medium text-foreground">
              ${(portfolio.exposure.limit - portfolio.exposure.current).toLocaleString()}
            </span>
          </div>
        </div>

        {/* Asset Allocation Chart */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">Asset Allocation</h3>
          
          <div className="h-48">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={portfolio.assetAllocation}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={2}
                  dataKey="value"
                >
                  {portfolio.assetAllocation.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value: number) => [`$${value.toFixed(2)}`, 'Value']}
                  labelFormatter={(label) => `${label}`}
                  contentStyle={{
                    backgroundColor: 'hsl(var(--card))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                    color: 'hsl(var(--foreground))'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>

          {/* Legend */}
          <div className="grid grid-cols-2 gap-2 text-sm">
            {portfolio.assetAllocation.map((asset, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: asset.color }}
                ></div>
                <span className="text-muted-foreground">{asset.name}</span>
                <span className="font-medium text-foreground">
                  {asset.percentage}%
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
