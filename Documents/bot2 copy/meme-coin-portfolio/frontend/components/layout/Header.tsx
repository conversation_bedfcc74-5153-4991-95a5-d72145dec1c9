/**
 * Header Component
 *
 * Top navigation bar featuring:
 * - Connection status
 * - Wallet information
 * - Quick actions
 * - Real-time indicators
 */

'use client'

import { useState, useEffect } from 'react'
import { Wallet, Wifi, WifiOff, RefreshCw, Bell, Settings } from 'lucide-react'
import { usePortfolioStore } from '@/hooks/usePortfolioStore'
import { useWebSocket } from '@/hooks/useWebSocket'

export function Header() {
  const { portfolio, isLoading, refreshPortfolio, lastUpdated } = usePortfolioStore()
  const { isConnected } = useWebSocket()
  const [currentTime, setCurrentTime] = useState(new Date())

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const formatLastUpdated = (date: Date | string | null) => {
    if (!date) return 'Never'

    // Convert string to Date if needed (happens when rehydrating from localStorage)
    const dateObj = typeof date === 'string' ? new Date(date) : date

    // Check if the date is valid
    if (isNaN(dateObj.getTime())) return 'Invalid date'

    const now = new Date()
    const diff = now.getTime() - dateObj.getTime()
    const seconds = Math.floor(diff / 1000)

    if (seconds < 60) return `${seconds}s ago`
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ago`
    return dateObj.toLocaleTimeString('en-US', { hour12: false })
  }

  return (
    <header className="bg-card border-b border-border px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Status indicators */}
        <div className="flex items-center space-x-6">
          {/* Connection status */}
          <div className="flex items-center space-x-2">
            {isConnected ? (
              <Wifi size={16} className="text-green-500" />
            ) : (
              <WifiOff size={16} className="text-red-500" />
            )}
            <span className={`text-sm font-medium ${
              isConnected ? 'text-green-500' : 'text-red-500'
            }`}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>

          {/* Last updated */}
          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
            <span>Last updated:</span>
            <span className="font-medium">
              {formatLastUpdated(lastUpdated)}
            </span>
          </div>

          {/* Refresh button */}
          <button
            onClick={refreshPortfolio}
            disabled={isLoading}
            className={`
              p-2 rounded-lg border border-border hover:bg-accent transition-colors
              ${isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary/50'}
            `}
            title="Refresh portfolio data"
          >
            <RefreshCw
              size={16}
              className={`${isLoading ? 'animate-spin' : ''} text-muted-foreground hover:text-foreground`}
            />
          </button>
        </div>

        {/* Center - Portfolio summary */}
        <div className="hidden md:flex items-center space-x-8">
          {portfolio && (
            <>
              {/* Total value */}
              <div className="text-center">
                <div className="text-sm text-muted-foreground">Total Value</div>
                <div className="text-lg font-bold text-foreground">
                  ${portfolio.totalValue.toLocaleString()}
                </div>
              </div>

              {/* 24h P&L */}
              <div className="text-center">
                <div className="text-sm text-muted-foreground">24h P&L</div>
                <div className={`text-lg font-bold ${
                  portfolio.pnl24h.amount >= 0 ? 'text-profit' : 'text-loss'
                }`}>
                  {portfolio.pnl24h.amount >= 0 ? '+' : ''}
                  ${portfolio.pnl24h.amount.toFixed(2)}
                  <span className="text-sm ml-1">
                    ({portfolio.pnl24h.percentage >= 0 ? '+' : ''}
                    {portfolio.pnl24h.percentage.toFixed(2)}%)
                  </span>
                </div>
              </div>

              {/* Active positions */}
              <div className="text-center">
                <div className="text-sm text-muted-foreground">Positions</div>
                <div className="text-lg font-bold text-foreground">
                  {portfolio.activePositions}
                </div>
              </div>
            </>
          )}
        </div>

        {/* Right side - Actions and info */}
        <div className="flex items-center space-x-4">
          {/* Current time */}
          <div className="hidden sm:block text-sm text-muted-foreground">
            {formatTime(currentTime)}
          </div>

          {/* Wallet info */}
          {portfolio && (
            <div className="flex items-center space-x-2 px-3 py-2 bg-accent rounded-lg">
              <Wallet size={16} className="text-muted-foreground" />
              <div className="text-sm">
                <div className="font-medium text-foreground">
                  {portfolio.availableBalance.toFixed(3)} SOL
                </div>
                <div className="text-xs text-muted-foreground">Available</div>
              </div>
            </div>
          )}

          {/* Notifications */}
          <button
            className="p-2 rounded-lg border border-border hover:bg-accent transition-colors relative"
            title="Notifications"
          >
            <Bell size={16} className="text-muted-foreground hover:text-foreground" />
            {/* Notification badge */}
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></div>
          </button>

          {/* Settings */}
          <button
            className="p-2 rounded-lg border border-border hover:bg-accent transition-colors"
            title="Settings"
          >
            <Settings size={16} className="text-muted-foreground hover:text-foreground" />
          </button>
        </div>
      </div>

      {/* Mobile portfolio summary */}
      <div className="md:hidden mt-4 pt-4 border-t border-border">
        {portfolio && (
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-xs text-muted-foreground">Total Value</div>
              <div className="text-sm font-bold text-foreground">
                ${portfolio.totalValue.toLocaleString()}
              </div>
            </div>
            <div>
              <div className="text-xs text-muted-foreground">24h P&L</div>
              <div className={`text-sm font-bold ${
                portfolio.pnl24h.amount >= 0 ? 'text-profit' : 'text-loss'
              }`}>
                {portfolio.pnl24h.amount >= 0 ? '+' : ''}
                ${portfolio.pnl24h.amount.toFixed(2)}
              </div>
            </div>
            <div>
              <div className="text-xs text-muted-foreground">Positions</div>
              <div className="text-sm font-bold text-foreground">
                {portfolio.activePositions}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  )
}
