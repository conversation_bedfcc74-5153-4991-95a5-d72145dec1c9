/**
 * Signal Panel Component
 * 
 * Displays Dead Hunter signals with:
 * - Real-time signal feed
 * - Signal filtering and sorting
 * - Signal details and actions
 * - Watched tokens management
 */

'use client'

import { useState, useEffect } from 'react'
import { 
  TrendingUp, 
  Volume2, 
  Activity, 
  Plus, 
  Filter, 
  Clock,
  AlertCircle,
  Eye,
  EyeOff
} from 'lucide-react'
import { useSignalStore, Signal } from '@/hooks/useSignalStore'

const signalTypeIcons = {
  volume_spike: Volume2,
  price_movement: TrendingUp,
  tx_rate: Activity,
  new_pair: Plus
}

const signalTypeColors = {
  volume_spike: 'text-blue-400',
  price_movement: 'text-green-400',
  tx_rate: 'text-purple-400',
  new_pair: 'text-orange-400'
}

export function SignalPanel() {
  const { 
    signals, 
    filters, 
    getFilteredSignals, 
    setFilters, 
    markAllAsRead,
    updateSignalStatus,
    clearOldSignals
  } = useSignalStore()

  const [showFilters, setShowFilters] = useState(false)
  const [filteredSignals, setFilteredSignals] = useState<Signal[]>([])

  // Update filtered signals when signals or filters change
  useEffect(() => {
    setFilteredSignals(getFilteredSignals())
  }, [signals, filters, getFilteredSignals])

  // Clean old signals periodically
  useEffect(() => {
    const interval = setInterval(() => {
      clearOldSignals()
    }, 5 * 60 * 1000) // Every 5 minutes

    return () => clearInterval(interval)
  }, [clearOldSignals])

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const signalTime = new Date(timestamp)
    const diffMs = now.getTime() - signalTime.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMinutes / 60)

    if (diffMinutes < 1) return 'Just now'
    if (diffMinutes < 60) return `${diffMinutes}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    return signalTime.toLocaleDateString()
  }

  const getSignalIntensity = (signal: Signal) => {
    const ratio = signal.value / signal.threshold
    if (ratio >= 3) return 'high'
    if (ratio >= 2) return 'medium'
    return 'low'
  }

  const getIntensityColor = (intensity: string) => {
    switch (intensity) {
      case 'high': return 'border-red-500 bg-red-500/10'
      case 'medium': return 'border-yellow-500 bg-yellow-500/10'
      default: return 'border-green-500 bg-green-500/10'
    }
  }

  const newSignalsCount = filteredSignals.filter(s => s.isNew).length

  return (
    <div className="bg-card rounded-lg border border-border p-6 space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <h2 className="text-xl font-bold text-foreground">Dead Hunter</h2>
          {newSignalsCount > 0 && (
            <div className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
              {newSignalsCount}
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="p-2 rounded-lg hover:bg-accent transition-colors"
            title="Filter signals"
          >
            <Filter size={16} className="text-muted-foreground" />
          </button>
          {newSignalsCount > 0 && (
            <button
              onClick={markAllAsRead}
              className="p-2 rounded-lg hover:bg-accent transition-colors"
              title="Mark all as read"
            >
              <EyeOff size={16} className="text-muted-foreground" />
            </button>
          )}
        </div>
      </div>

      {/* Filters */}
      {showFilters && (
        <div className="bg-accent/50 rounded-lg p-4 space-y-3">
          {/* Signal Types */}
          <div>
            <label className="text-sm font-medium text-muted-foreground mb-2 block">
              Signal Types
            </label>
            <div className="flex flex-wrap gap-2">
              {Object.entries(signalTypeIcons).map(([type, Icon]) => (
                <button
                  key={type}
                  onClick={() => {
                    const newTypes = filters.signalTypes.includes(type)
                      ? filters.signalTypes.filter(t => t !== type)
                      : [...filters.signalTypes, type]
                    setFilters({ signalTypes: newTypes })
                  }}
                  className={`flex items-center space-x-1 px-3 py-1 rounded-lg text-sm transition-colors ${
                    filters.signalTypes.includes(type)
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-accent text-muted-foreground hover:text-foreground'
                  }`}
                >
                  <Icon size={14} />
                  <span className="capitalize">{type.replace('_', ' ')}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Time Range */}
          <div>
            <label className="text-sm font-medium text-muted-foreground mb-2 block">
              Time Range
            </label>
            <select
              value={filters.timeRange}
              onChange={(e) => setFilters({ timeRange: e.target.value as any })}
              className="bg-accent border border-border rounded-lg px-3 py-2 text-foreground"
            >
              <option value="1h">Last Hour</option>
              <option value="6h">Last 6 Hours</option>
              <option value="24h">Last 24 Hours</option>
              <option value="all">All Time</option>
            </select>
          </div>

          {/* Minimum Value */}
          <div>
            <label className="text-sm font-medium text-muted-foreground mb-2 block">
              Minimum Signal Value
            </label>
            <input
              type="number"
              value={filters.minValue}
              onChange={(e) => setFilters({ minValue: parseFloat(e.target.value) || 0 })}
              min="0"
              step="0.1"
              className="w-full bg-accent border border-border rounded-lg px-3 py-2 text-foreground"
            />
          </div>
        </div>
      )}

      {/* Signals List */}
      <div className="space-y-3 max-h-96 overflow-y-auto custom-scrollbar">
        {filteredSignals.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <AlertCircle size={48} className="mx-auto mb-4 opacity-50" />
            <p>No signals found</p>
            <p className="text-sm">Adjust your filters or wait for new signals</p>
          </div>
        ) : (
          filteredSignals.map((signal) => {
            const Icon = signalTypeIcons[signal.type]
            const intensity = getSignalIntensity(signal)
            const intensityColor = getIntensityColor(intensity)

            return (
              <div
                key={signal.id}
                className={`border rounded-lg p-3 transition-all hover:shadow-md cursor-pointer ${
                  signal.isNew ? 'border-primary bg-primary/5' : intensityColor
                }`}
                onClick={() => updateSignalStatus(signal.id, false)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${
                      signal.isNew ? 'bg-primary/20' : 'bg-accent'
                    }`}>
                      <Icon 
                        size={16} 
                        className={signal.isNew ? 'text-primary' : signalTypeColors[signal.type]} 
                      />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium text-foreground">
                          {signal.tokenSymbol || 'Unknown'}
                        </span>
                        {signal.isNew && (
                          <div className="bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">
                            NEW
                          </div>
                        )}
                      </div>
                      
                      <div className="text-sm text-muted-foreground">
                        {signal.tokenName || signal.tokenMint.slice(0, 8) + '...'}
                      </div>
                      
                      <div className="text-sm text-foreground mt-1">
                        <span className="capitalize">{signal.type.replace('_', ' ')}</span>
                        <span className="text-muted-foreground mx-2">•</span>
                        <span className={`font-medium ${
                          intensity === 'high' ? 'text-red-400' :
                          intensity === 'medium' ? 'text-yellow-400' : 'text-green-400'
                        }`}>
                          {signal.value.toFixed(2)}x
                        </span>
                      </div>
                      
                      {signal.metadata && (
                        <div className="text-xs text-muted-foreground mt-1 space-y-1">
                          {signal.metadata.volume24h && (
                            <div>Volume: ${signal.metadata.volume24h.toLocaleString()}</div>
                          )}
                          {signal.metadata.priceChange && (
                            <div className={signal.metadata.priceChange >= 0 ? 'text-profit' : 'text-loss'}>
                              Price: {signal.metadata.priceChange >= 0 ? '+' : ''}
                              {signal.metadata.priceChange.toFixed(2)}%
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center space-x-1 text-xs text-muted-foreground">
                      <Clock size={12} />
                      <span>{formatTimeAgo(signal.timestamp)}</span>
                    </div>
                    
                    <div className="mt-1">
                      <button
                        className="text-xs text-primary hover:text-primary/80 transition-colors"
                        onClick={(e) => {
                          e.stopPropagation()
                          // TODO: Open token details or trading panel
                        }}
                      >
                        Trade
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )
          })
        )}
      </div>

      {/* Summary Stats */}
      <div className="pt-4 border-t border-border">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="text-center">
            <div className="font-medium text-foreground">{filteredSignals.length}</div>
            <div className="text-muted-foreground">Total Signals</div>
          </div>
          <div className="text-center">
            <div className="font-medium text-foreground">{newSignalsCount}</div>
            <div className="text-muted-foreground">New Signals</div>
          </div>
        </div>
      </div>
    </div>
  )
}
