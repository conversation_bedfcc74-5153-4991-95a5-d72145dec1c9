/**
 * Trading Panel Component
 * 
 * Provides trading interface with:
 * - Buy/Sell order placement
 * - Dynamic slippage management
 * - Position validation
 * - Quick trade execution
 */

'use client'

import { useState, useEffect } from 'react'
import { ArrowUpDown, Settings, AlertTriangle, CheckCircle, Loader2 } from 'lucide-react'
import axios from 'axios'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'

interface Token {
  address: string
  symbol: string
  name: string
  decimals: number
}

interface Quote {
  inputMint: string
  outputMint: string
  inAmount: string
  outAmount: string
  priceImpactPct: string
  slippageBps: number
  metrics: {
    rate: number
    priceImpact: number
    minimumReceived: number
    estimatedFee: number
  }
  warnings: string[]
}

export function TradingPanel() {
  const [tradeType, setTradeType] = useState<'buy' | 'sell'>('buy')
  const [inputToken, setInputToken] = useState<Token>({
    address: 'So11111111111111111111111111111111111111112',
    symbol: 'S<PERSON>',
    name: '<PERSON><PERSON>',
    decimals: 9
  })
  const [outputToken, setOutputToken] = useState<Token>({
    address: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
    symbol: 'BONK',
    name: 'Bonk',
    decimals: 5
  })
  const [amount, setAmount] = useState('')
  const [slippage, setSlippage] = useState(50) // basis points
  const [slippageMode, setSlippageMode] = useState<'dynamic' | 'static'>('dynamic')
  const [quote, setQuote] = useState<Quote | null>(null)
  const [isLoadingQuote, setIsLoadingQuote] = useState(false)
  const [isExecuting, setIsExecuting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [availableTokens, setAvailableTokens] = useState<Token[]>([])

  // Load available tokens
  useEffect(() => {
    const loadTokens = async () => {
      try {
        const response = await axios.get(`${API_BASE_URL}/api/trade/tokens`)
        setAvailableTokens(response.data.tokens || [])
      } catch (error) {
        console.error('Failed to load tokens:', error)
      }
    }
    loadTokens()
  }, [])

  // Get quote when inputs change
  useEffect(() => {
    if (amount && parseFloat(amount) > 0) {
      const timer = setTimeout(() => {
        getQuote()
      }, 500) // Debounce
      return () => clearTimeout(timer)
    } else {
      setQuote(null)
    }
  }, [amount, inputToken, outputToken, slippage])

  const getQuote = async () => {
    if (!amount || parseFloat(amount) <= 0) return

    setIsLoadingQuote(true)
    setError(null)

    try {
      const response = await axios.post(`${API_BASE_URL}/api/trade/quote`, {
        inputMint: inputToken.address,
        outputMint: outputToken.address,
        amount: parseFloat(amount) * Math.pow(10, inputToken.decimals),
        slippageBps: slippage
      })

      setQuote(response.data)
    } catch (error) {
      console.error('Failed to get quote:', error)
      setError('Failed to get quote. Please try again.')
    } finally {
      setIsLoadingQuote(false)
    }
  }

  const executeTrade = async () => {
    if (!quote || !amount) return

    setIsExecuting(true)
    setError(null)

    try {
      const response = await axios.post(`${API_BASE_URL}/api/trade/execute`, {
        inputMint: inputToken.address,
        outputMint: outputToken.address,
        amount: parseFloat(amount) * Math.pow(10, inputToken.decimals),
        slippageBps: slippage,
        applyExitStrategy: true
      })

      // Reset form on success
      setAmount('')
      setQuote(null)
      
      // Show success message
      alert(`Trade submitted successfully! Job ID: ${response.data.jobId}`)
    } catch (error: any) {
      console.error('Failed to execute trade:', error)
      setError(error.response?.data?.message || 'Failed to execute trade')
    } finally {
      setIsExecuting(false)
    }
  }

  const swapTokens = () => {
    const temp = inputToken
    setInputToken(outputToken)
    setOutputToken(temp)
    setAmount('')
    setQuote(null)
  }

  const formatNumber = (num: number, decimals: number = 6) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: decimals
    }).format(num)
  }

  return (
    <div className="bg-card rounded-lg border border-border p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-bold text-foreground">Quick Trade</h2>
        <button className="p-2 rounded-lg hover:bg-accent transition-colors">
          <Settings size={16} className="text-muted-foreground" />
        </button>
      </div>

      {/* Trade Type Toggle */}
      <div className="flex bg-accent rounded-lg p-1">
        <button
          onClick={() => setTradeType('buy')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            tradeType === 'buy'
              ? 'bg-profit text-black'
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          Buy
        </button>
        <button
          onClick={() => setTradeType('sell')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${
            tradeType === 'sell'
              ? 'bg-loss text-white'
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          Sell
        </button>
      </div>

      {/* Token Selection */}
      <div className="space-y-4">
        {/* From Token */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">From</label>
          <div className="flex items-center space-x-2">
            <select
              value={inputToken.address}
              onChange={(e) => {
                const token = availableTokens.find(t => t.address === e.target.value)
                if (token) setInputToken(token)
              }}
              className="flex-1 bg-accent border border-border rounded-lg px-3 py-2 text-foreground"
            >
              <option value={inputToken.address}>{inputToken.symbol}</option>
              {availableTokens.map(token => (
                <option key={token.address} value={token.address}>
                  {token.symbol}
                </option>
              ))}
            </select>
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="0.0"
              className="flex-1 bg-accent border border-border rounded-lg px-3 py-2 text-foreground text-right"
            />
          </div>
        </div>

        {/* Swap Button */}
        <div className="flex justify-center">
          <button
            onClick={swapTokens}
            className="p-2 rounded-lg border border-border hover:bg-accent transition-colors"
          >
            <ArrowUpDown size={16} className="text-muted-foreground" />
          </button>
        </div>

        {/* To Token */}
        <div className="space-y-2">
          <label className="text-sm font-medium text-muted-foreground">To</label>
          <div className="flex items-center space-x-2">
            <select
              value={outputToken.address}
              onChange={(e) => {
                const token = availableTokens.find(t => t.address === e.target.value)
                if (token) setOutputToken(token)
              }}
              className="flex-1 bg-accent border border-border rounded-lg px-3 py-2 text-foreground"
            >
              <option value={outputToken.address}>{outputToken.symbol}</option>
              {availableTokens.map(token => (
                <option key={token.address} value={token.address}>
                  {token.symbol}
                </option>
              ))}
            </select>
            <div className="flex-1 bg-accent border border-border rounded-lg px-3 py-2 text-right text-muted-foreground">
              {quote ? formatNumber(parseFloat(quote.outAmount) / Math.pow(10, outputToken.decimals)) : '0.0'}
            </div>
          </div>
        </div>
      </div>

      {/* Slippage Settings */}
      <div className="space-y-2">
        <label className="text-sm font-medium text-muted-foreground">Slippage Tolerance</label>
        <div className="flex items-center space-x-2">
          <select
            value={slippageMode}
            onChange={(e) => setSlippageMode(e.target.value as 'dynamic' | 'static')}
            className="bg-accent border border-border rounded-lg px-3 py-2 text-foreground"
          >
            <option value="dynamic">Dynamic</option>
            <option value="static">Static</option>
          </select>
          <input
            type="number"
            value={slippage}
            onChange={(e) => setSlippage(parseInt(e.target.value) || 50)}
            min="1"
            max="5000"
            className="flex-1 bg-accent border border-border rounded-lg px-3 py-2 text-foreground text-right"
          />
          <span className="text-sm text-muted-foreground">bps</span>
        </div>
        <div className="text-xs text-muted-foreground">
          {(slippage / 100).toFixed(2)}% slippage tolerance
        </div>
      </div>

      {/* Quote Information */}
      {quote && (
        <div className="bg-accent/50 rounded-lg p-4 space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Rate</span>
            <span className="text-foreground">
              1 {inputToken.symbol} = {formatNumber(quote.metrics.rate)} {outputToken.symbol}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Price Impact</span>
            <span className={`${
              quote.metrics.priceImpact > 1 ? 'text-loss' : 'text-foreground'
            }`}>
              {quote.metrics.priceImpact.toFixed(2)}%
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Minimum Received</span>
            <span className="text-foreground">
              {formatNumber(quote.metrics.minimumReceived / Math.pow(10, outputToken.decimals))} {outputToken.symbol}
            </span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Network Fee</span>
            <span className="text-foreground">~{quote.metrics.estimatedFee} SOL</span>
          </div>
        </div>
      )}

      {/* Warnings */}
      {quote && quote.warnings.length > 0 && (
        <div className="bg-yellow-500/20 border border-yellow-500/50 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <AlertTriangle size={16} className="text-yellow-500 mt-0.5" />
            <div className="space-y-1">
              {quote.warnings.map((warning, index) => (
                <div key={index} className="text-sm text-yellow-400">
                  {warning}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-loss/20 border border-loss/50 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <AlertTriangle size={16} className="text-loss" />
            <span className="text-sm text-loss">{error}</span>
          </div>
        </div>
      )}

      {/* Execute Button */}
      <button
        onClick={executeTrade}
        disabled={!quote || isExecuting || isLoadingQuote}
        className={`w-full py-3 px-4 rounded-lg font-medium transition-all ${
          tradeType === 'buy'
            ? 'bg-profit hover:bg-profit/90 text-black'
            : 'bg-loss hover:bg-loss/90 text-white'
        } disabled:opacity-50 disabled:cursor-not-allowed`}
      >
        {isExecuting ? (
          <div className="flex items-center justify-center space-x-2">
            <Loader2 size={16} className="animate-spin" />
            <span>Executing...</span>
          </div>
        ) : isLoadingQuote ? (
          <div className="flex items-center justify-center space-x-2">
            <Loader2 size={16} className="animate-spin" />
            <span>Getting Quote...</span>
          </div>
        ) : (
          `${tradeType === 'buy' ? 'Buy' : 'Sell'} ${outputToken.symbol}`
        )}
      </button>

      {/* Exit Strategy Notice */}
      <div className="bg-info/20 border border-info/50 rounded-lg p-3">
        <div className="flex items-start space-x-2">
          <CheckCircle size={16} className="text-info mt-0.5" />
          <div className="text-sm text-info">
            Exit strategy will be automatically applied to this position
          </div>
        </div>
      </div>
    </div>
  )
}
