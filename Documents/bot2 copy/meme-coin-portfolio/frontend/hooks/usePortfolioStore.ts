/**
 * Portfolio state management with Zustand
 * 
 * Manages portfolio data including:
 * - Portfolio overview and statistics
 * - Active positions
 * - Trade history
 * - Performance analytics
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import axios from 'axios'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'

export interface Position {
  id: string
  tokenMint: string
  tokenSymbol: string
  tokenName: string
  amount: number
  entryPrice: number
  currentPrice: number
  value: number
  pnl: {
    amount: number
    percentage: number
  }
  entryTime: string
  exitStrategy: {
    stopLoss: number
    trailingStop: number
    takeProfitLevels: number[]
  }
}

export interface Trade {
  id: string
  type: 'buy' | 'sell'
  tokenSymbol: string
  amount: number
  price: number
  value: number
  timestamp: string
  signature: string
}

export interface AssetAllocation {
  name: string
  value: number
  percentage: number
  color: string
}

export interface PortfolioData {
  totalValue: number
  totalValueSOL: number
  pnl24h: {
    amount: number
    percentage: number
  }
  pnl7d: {
    amount: number
    percentage: number
  }
  pnlTotal: {
    amount: number
    percentage: number
  }
  activePositions: number
  totalInvested: number
  availableBalance: number
  exposure: {
    current: number
    limit: number
    percentage: number
  }
  positions: Position[]
  assetAllocation: AssetAllocation[]
  recentTrades: Trade[]
}

interface PortfolioStore {
  // State
  portfolio: PortfolioData | null
  isLoading: boolean
  error: string | null
  lastUpdated: Date | null

  // Actions
  fetchPortfolio: () => Promise<void>
  updatePortfolio: (data: PortfolioData) => void
  refreshPortfolio: () => Promise<void>
  clearError: () => void
  
  // Position actions
  updatePosition: (positionId: string, updates: Partial<Position>) => void
  removePosition: (positionId: string) => void
  
  // Trade actions
  addTrade: (trade: Trade) => void
}

export const usePortfolioStore = create<PortfolioStore>()(
  persist(
    (set, get) => ({
      // Initial state
      portfolio: null,
      isLoading: false,
      error: null,
      lastUpdated: null,

      // Fetch portfolio data from API
      fetchPortfolio: async () => {
        set({ isLoading: true, error: null })
        
        try {
          const response = await axios.get(`${API_BASE_URL}/api/portfolio`)
          const portfolioData = response.data
          
          set({
            portfolio: portfolioData,
            isLoading: false,
            lastUpdated: new Date(),
            error: null
          })
        } catch (error) {
          console.error('Failed to fetch portfolio:', error)
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to fetch portfolio'
          })
        }
      },

      // Update portfolio data (used by WebSocket updates)
      updatePortfolio: (data: PortfolioData) => {
        set({
          portfolio: data,
          lastUpdated: new Date(),
          error: null
        })
      },

      // Force refresh portfolio data
      refreshPortfolio: async () => {
        try {
          // Clear cache on backend
          await axios.post(`${API_BASE_URL}/api/portfolio/refresh`)
          
          // Fetch fresh data
          await get().fetchPortfolio()
        } catch (error) {
          console.error('Failed to refresh portfolio:', error)
          set({
            error: error instanceof Error ? error.message : 'Failed to refresh portfolio'
          })
        }
      },

      // Clear error state
      clearError: () => {
        set({ error: null })
      },

      // Update a specific position
      updatePosition: (positionId: string, updates: Partial<Position>) => {
        const { portfolio } = get()
        if (!portfolio) return

        const updatedPositions = portfolio.positions.map(position =>
          position.id === positionId
            ? { ...position, ...updates }
            : position
        )

        set({
          portfolio: {
            ...portfolio,
            positions: updatedPositions
          },
          lastUpdated: new Date()
        })
      },

      // Remove a position
      removePosition: (positionId: string) => {
        const { portfolio } = get()
        if (!portfolio) return

        const updatedPositions = portfolio.positions.filter(
          position => position.id !== positionId
        )

        set({
          portfolio: {
            ...portfolio,
            positions: updatedPositions,
            activePositions: updatedPositions.length
          },
          lastUpdated: new Date()
        })
      },

      // Add a new trade to history
      addTrade: (trade: Trade) => {
        const { portfolio } = get()
        if (!portfolio) return

        const updatedTrades = [trade, ...portfolio.recentTrades].slice(0, 50) // Keep last 50 trades

        set({
          portfolio: {
            ...portfolio,
            recentTrades: updatedTrades
          },
          lastUpdated: new Date()
        })
      }
    }),
    {
      name: 'portfolio-store',
      partialize: (state) => ({
        portfolio: state.portfolio,
        lastUpdated: state.lastUpdated
      })
    }
  )
)
