/**
 * Signal detection state management
 * 
 * Manages Dead Hunter signals including:
 * - Signal detection and storage
 * - Signal filtering and sorting
 * - Watched tokens management
 * - Signal history tracking
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface Signal {
  id: string
  tokenMint: string
  tokenSymbol?: string
  tokenName?: string
  type: 'volume_spike' | 'price_movement' | 'tx_rate' | 'new_pair'
  value: number
  threshold: number
  timestamp: string
  isNew: boolean
  metadata?: {
    volume24h?: number
    priceChange?: number
    txCount?: number
    liquidity?: number
  }
}

export interface WatchedToken {
  tokenMint: string
  tokenSymbol: string
  tokenName: string
  addedAt: string
  thresholds: {
    volumeMultiplier: number
    priceChangePercent: number
    txRateMultiplier: number
  }
}

interface SignalStore {
  // State
  signals: Signal[]
  watchedTokens: WatchedToken[]
  isLoading: boolean
  error: string | null
  
  // Filters
  filters: {
    signalTypes: string[]
    timeRange: '1h' | '6h' | '24h' | 'all'
    minValue: number
  }

  // Actions
  addSignal: (signal: Omit<Signal, 'id' | 'isNew'>) => void
  updateSignalStatus: (signalId: string, isNew: boolean) => void
  clearOldSignals: () => void
  
  // Watched tokens
  addWatchedToken: (token: Omit<WatchedToken, 'addedAt'>) => void
  removeWatchedToken: (tokenMint: string) => void
  updateTokenThresholds: (tokenMint: string, thresholds: WatchedToken['thresholds']) => void
  
  // Filters
  setFilters: (filters: Partial<SignalStore['filters']>) => void
  getFilteredSignals: () => Signal[]
  
  // Utility
  clearError: () => void
  markAllAsRead: () => void
}

export const useSignalStore = create<SignalStore>()(
  persist(
    (set, get) => ({
      // Initial state
      signals: [],
      watchedTokens: [
        // Default watched tokens (popular meme coins)
        {
          tokenMint: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
          tokenSymbol: 'BONK',
          tokenName: 'Bonk',
          addedAt: new Date().toISOString(),
          thresholds: {
            volumeMultiplier: 5,
            priceChangePercent: 10,
            txRateMultiplier: 10
          }
        },
        {
          tokenMint: 'WENWENvqqNya429ubCdR81ZmD69brwQaaBYY6p3LCpk',
          tokenSymbol: 'WEN',
          tokenName: 'Wen',
          addedAt: new Date().toISOString(),
          thresholds: {
            volumeMultiplier: 5,
            priceChangePercent: 10,
            txRateMultiplier: 10
          }
        }
      ],
      isLoading: false,
      error: null,
      
      filters: {
        signalTypes: ['volume_spike', 'price_movement', 'tx_rate', 'new_pair'],
        timeRange: '24h',
        minValue: 0
      },

      // Add a new signal
      addSignal: (signalData) => {
        const signal: Signal = {
          ...signalData,
          id: `signal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          isNew: true
        }

        set((state) => ({
          signals: [signal, ...state.signals].slice(0, 1000) // Keep last 1000 signals
        }))
      },

      // Update signal read status
      updateSignalStatus: (signalId, isNew) => {
        set((state) => ({
          signals: state.signals.map(signal =>
            signal.id === signalId ? { ...signal, isNew } : signal
          )
        }))
      },

      // Clear signals older than 24 hours
      clearOldSignals: () => {
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
        
        set((state) => ({
          signals: state.signals.filter(signal => 
            new Date(signal.timestamp) > oneDayAgo
          )
        }))
      },

      // Add a token to watch list
      addWatchedToken: (tokenData) => {
        const token: WatchedToken = {
          ...tokenData,
          addedAt: new Date().toISOString()
        }

        set((state) => {
          // Check if token already exists
          const exists = state.watchedTokens.some(t => t.tokenMint === token.tokenMint)
          if (exists) return state

          return {
            watchedTokens: [...state.watchedTokens, token]
          }
        })
      },

      // Remove a token from watch list
      removeWatchedToken: (tokenMint) => {
        set((state) => ({
          watchedTokens: state.watchedTokens.filter(token => 
            token.tokenMint !== tokenMint
          )
        }))
      },

      // Update thresholds for a watched token
      updateTokenThresholds: (tokenMint, thresholds) => {
        set((state) => ({
          watchedTokens: state.watchedTokens.map(token =>
            token.tokenMint === tokenMint 
              ? { ...token, thresholds }
              : token
          )
        }))
      },

      // Set filters
      setFilters: (newFilters) => {
        set((state) => ({
          filters: { ...state.filters, ...newFilters }
        }))
      },

      // Get filtered signals
      getFilteredSignals: () => {
        const { signals, filters } = get()
        
        let filtered = signals

        // Filter by signal types
        if (filters.signalTypes.length > 0) {
          filtered = filtered.filter(signal => 
            filters.signalTypes.includes(signal.type)
          )
        }

        // Filter by time range
        if (filters.timeRange !== 'all') {
          const timeRangeMs = {
            '1h': 60 * 60 * 1000,
            '6h': 6 * 60 * 60 * 1000,
            '24h': 24 * 60 * 60 * 1000
          }[filters.timeRange]

          const cutoff = new Date(Date.now() - timeRangeMs)
          filtered = filtered.filter(signal => 
            new Date(signal.timestamp) > cutoff
          )
        }

        // Filter by minimum value
        if (filters.minValue > 0) {
          filtered = filtered.filter(signal => 
            signal.value >= filters.minValue
          )
        }

        // Sort by timestamp (newest first)
        return filtered.sort((a, b) => 
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
        )
      },

      // Clear error state
      clearError: () => {
        set({ error: null })
      },

      // Mark all signals as read
      markAllAsRead: () => {
        set((state) => ({
          signals: state.signals.map(signal => ({ ...signal, isNew: false }))
        }))
      }
    }),
    {
      name: 'signal-store',
      partialize: (state) => ({
        signals: state.signals.slice(0, 100), // Persist only last 100 signals
        watchedTokens: state.watchedTokens,
        filters: state.filters
      })
    }
  )
)
