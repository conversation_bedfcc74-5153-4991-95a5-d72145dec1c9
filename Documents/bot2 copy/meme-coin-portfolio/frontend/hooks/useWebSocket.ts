/**
 * WebSocket connection hook
 *
 * Manages real-time communication with the backend:
 * - Portfolio updates
 * - Signal notifications
 * - Trade events
 * - Connection management
 */

import { useEffect, useRef, useState, useCallback } from 'react'
import { io, Socket } from 'socket.io-client'
import { usePortfolioStore } from './usePortfolioStore'
import { useSignalStore } from './useSignalStore'

const WS_URL = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:4000'

interface UseWebSocketReturn {
  isConnected: boolean
  connect: () => void
  disconnect: () => void
  subscribe: (channel: string) => void
  unsubscribe: (channel: string) => void
}

export const useWebSocket = (): UseWebSocketReturn => {
  const [isConnected, setIsConnected] = useState(false)
  const socketRef = useRef<Socket | null>(null)
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const reconnectAttempts = useRef(0)
  const maxReconnectAttempts = 5

  // Store actions
  const { updatePortfolio, addTrade } = usePortfolioStore()
  const { addSignal, updateSignalStatus } = useSignalStore()

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (socketRef.current?.connected) {
      return
    }

    console.log('Connecting to WebSocket:', WS_URL)

    const socket = io(`${WS_URL}/ws`, {
      transports: ['websocket', 'polling'],
      timeout: 20000,
      reconnection: false, // We'll handle reconnection manually
    })

    socketRef.current = socket

    // Connection events
    socket.on('connect', () => {
      console.log('WebSocket connected')
      setIsConnected(true)
      reconnectAttempts.current = 0

      // Subscribe to default channels
      socket.emit('subscribe:portfolio')
      socket.emit('subscribe:signals')
      socket.emit('subscribe:trades')
    })

    socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason)
      setIsConnected(false)

      // Attempt to reconnect unless it was a manual disconnect
      if (reason !== 'io client disconnect') {
        scheduleReconnect()
      }
    })

    socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error)
      setIsConnected(false)
      scheduleReconnect()
    })

    // Portfolio updates
    socket.on('portfolio:update', (data) => {
      console.log('📊 Real-time portfolio update received:', data)
      if (data.data) {
        // Get current portfolio to merge with real-time updates
        const currentPortfolio = usePortfolioStore.getState().portfolio;

        if (currentPortfolio) {
          // Update positions with new prices
          const updatedPositions = currentPortfolio.positions.map(position => {
            const updatedPosition = data.data.positions.find(
              (p: any) => p.tokenMint === position.tokenMint
            );

            if (updatedPosition) {
              return {
                ...position,
                currentPrice: updatedPosition.currentPrice,
                value: updatedPosition.value,
                priceChange24h: updatedPosition.priceChange24h || 0
              };
            }
            return position;
          });

          // Recalculate asset allocation with new values
          const totalValue = data.data.totalValue;
          const updatedAssetAllocation = currentPortfolio.assetAllocation.map(asset => {
            if (asset.name === 'SOL') {
              const solPriceUSD = 180; // Should match backend
              const solValue = data.data.totalValueSOL * solPriceUSD;
              return {
                ...asset,
                value: solValue,
                percentage: Math.round((solValue / totalValue) * 100)
              };
            } else {
              // Find corresponding position
              const position = updatedPositions.find(p => p.tokenSymbol === asset.name);
              if (position) {
                return {
                  ...asset,
                  value: position.value,
                  percentage: Math.round((position.value / totalValue) * 100)
                };
              }
            }
            return asset;
          });

          // Update portfolio with real-time data
          const updatedPortfolio = {
            ...currentPortfolio,
            totalValue: data.data.totalValue,
            totalValueSOL: data.data.totalValueSOL,
            positions: updatedPositions,
            assetAllocation: updatedAssetAllocation
          };

          updatePortfolio(updatedPortfolio);
        }
      }
    })

    // Individual token price updates
    socket.on('price:update', (data) => {
      console.log('💰 Token price update received:', data)
      if (data.data) {
        const currentPortfolio = usePortfolioStore.getState().portfolio;

        if (currentPortfolio) {
          const updatedPositions = currentPortfolio.positions.map(position => {
            if (position.tokenMint === data.data.tokenMint) {
              const newValue = position.amount * data.data.price;
              return {
                ...position,
                currentPrice: data.data.price,
                value: newValue,
                priceChange24h: data.data.change24h || 0
              };
            }
            return position;
          });

          // Recalculate total value
          const totalTokenValue = updatedPositions.reduce((sum, pos) => sum + pos.value, 0);
          const solPriceUSD = 180; // Should match backend
          const totalValueUSD = currentPortfolio.totalValueSOL * solPriceUSD;

          const updatedPortfolio = {
            ...currentPortfolio,
            totalValue: totalValueUSD + totalTokenValue,
            positions: updatedPositions
          };

          updatePortfolio(updatedPortfolio);
        }
      }
    })

    // Signal updates
    socket.on('signal:new', (data) => {
      console.log('New signal received:', data)
      if (data.signal) {
        addSignal(data.signal)

        // Show notification for important signals
        if (data.signal.type === 'volume_spike' && data.signal.value > 10) {
          showNotification('High Volume Signal', `${data.signal.tokenSymbol} volume spike detected!`)
        }
      }
    })

    socket.on('signals:recent', (data) => {
      console.log('Recent signals received:', data)
      if (data.signals) {
        data.signals.forEach((signal: any) => addSignal(signal))
      }
    })

    // Trade events
    socket.on('trade:event', (data) => {
      console.log('Trade event received:', data)
      if (data.event) {
        const { event } = data

        switch (event.type) {
          case 'trade_completed':
            addTrade(event.trade)
            showNotification('Trade Completed', `${event.trade.type} order executed successfully`)
            break
          case 'trade_failed':
            showNotification('Trade Failed', event.message, 'error')
            break
          case 'stop_loss_triggered':
            showNotification('Stop Loss Triggered', `Position closed: ${event.tokenSymbol}`, 'warning')
            break
          case 'take_profit_hit':
            showNotification('Take Profit Hit', `Profit locked: ${event.tokenSymbol}`, 'success')
            break
        }
      }
    })

    // System status updates
    socket.on('system:status', (data) => {
      console.log('System status:', data)
      // Could be used to show system health indicators
    })

    // Handle ping/pong for keepalive
    socket.on('pong', () => {
      // Connection is alive
    })

    // Send periodic ping
    const pingInterval = setInterval(() => {
      if (socket.connected) {
        socket.emit('ping')
      }
    }, 30000) // Every 30 seconds

    // Cleanup interval on disconnect
    socket.on('disconnect', () => {
      clearInterval(pingInterval)
    })

  }, [updatePortfolio, addSignal, addTrade])

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }

    if (socketRef.current) {
      socketRef.current.disconnect()
      socketRef.current = null
    }

    setIsConnected(false)
  }, [])

  // Schedule reconnection with exponential backoff
  const scheduleReconnect = useCallback(() => {
    if (reconnectAttempts.current >= maxReconnectAttempts) {
      console.log('Max reconnection attempts reached')
      return
    }

    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }

    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000) // Max 30 seconds
    console.log(`Scheduling reconnect in ${delay}ms (attempt ${reconnectAttempts.current + 1})`)

    reconnectTimeoutRef.current = setTimeout(() => {
      reconnectAttempts.current++
      connect()
    }, delay)
  }, [connect])

  // Subscribe to a channel
  const subscribe = useCallback((channel: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit(`subscribe:${channel}`)
    }
  }, [])

  // Unsubscribe from a channel
  const unsubscribe = useCallback((channel: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('unsubscribe', channel)
    }
  }, [])

  // Show browser notification
  const showNotification = (title: string, body: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
    // Check if notifications are supported and permitted
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification(title, {
        body,
        icon: '/favicon.ico',
        tag: type // Prevents duplicate notifications
      })
    }
  }

  // Request notification permission on mount
  useEffect(() => {
    if ('Notification' in window && Notification.permission === 'default') {
      Notification.requestPermission()
    }
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    isConnected,
    connect,
    disconnect,
    subscribe,
    unsubscribe
  }
}
