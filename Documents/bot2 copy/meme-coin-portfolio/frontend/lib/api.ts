/**
 * API client utilities
 * 
 * Centralized API communication with:
 * - Request/response interceptors
 * - Error handling
 * - Type safety
 * - Retry logic
 */

import axios, { AxiosInstance, AxiosError } from 'axios'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'

// Create axios instance with default config
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add timestamp to prevent caching
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`)
    return response
  },
  (error: AxiosError) => {
    console.error('API Response Error:', {
      status: error.response?.status,
      message: error.message,
      url: error.config?.url
    })

    // Handle specific error cases
    if (error.response?.status === 401) {
      // Handle unauthorized
      console.warn('Unauthorized access')
    } else if (error.response?.status >= 500) {
      // Handle server errors
      console.error('Server error occurred')
    }

    return Promise.reject(error)
  }
)

// API endpoints
export const api = {
  // Portfolio endpoints
  portfolio: {
    get: () => apiClient.get('/api/portfolio'),
    refresh: () => apiClient.post('/api/portfolio/refresh'),
    getPosition: (tokenMint: string) => apiClient.get(`/api/portfolio/positions/${tokenMint}`),
    getAnalytics: () => apiClient.get('/api/portfolio/analytics'),
  },

  // Trading endpoints
  trade: {
    getQuote: (data: {
      inputMint: string
      outputMint: string
      amount: number
      slippageBps?: number
    }) => apiClient.post('/api/trade/quote', data),
    
    execute: (data: {
      inputMint: string
      outputMint: string
      amount: number
      slippageBps?: number
      applyExitStrategy?: boolean
      maxPriceImpact?: number
    }) => apiClient.post('/api/trade/execute', data),
    
    getStatus: (jobId: string) => apiClient.get(`/api/trade/status/${jobId}`),
    getHistory: (params?: { limit?: number; offset?: number }) => 
      apiClient.get('/api/trade/history', { params }),
    cancel: (jobId: string) => apiClient.delete(`/api/trade/${jobId}`),
    getTokens: () => apiClient.get('/api/trade/tokens'),
  },

  // Configuration endpoints
  config: {
    getExitStrategy: () => apiClient.get('/api/config/exit-strategy'),
    updateExitStrategy: (config: any) => apiClient.post('/api/config/exit-strategy', config),
    getTradingConfig: () => apiClient.get('/api/config/trading'),
    updateTradingConfig: (config: any) => apiClient.post('/api/config/trading', config),
    getBotRules: () => apiClient.get('/api/config/bot-rules'),
    updateBotRules: (rules: any) => apiClient.post('/api/config/bot-rules', rules),
    reset: (type: string) => apiClient.post('/api/config/reset', { type }),
  },

  // Health endpoints
  health: {
    basic: () => apiClient.get('/health'),
    detailed: () => apiClient.get('/health/detailed'),
    ready: () => apiClient.get('/health/ready'),
    live: () => apiClient.get('/health/live'),
    metrics: () => apiClient.get('/health/metrics'),
  },
}

// Utility functions
export const handleApiError = (error: AxiosError): string => {
  if (error.response?.data && typeof error.response.data === 'object') {
    const data = error.response.data as any
    return data.message || data.error || 'An error occurred'
  }
  
  if (error.message) {
    return error.message
  }
  
  return 'Network error occurred'
}

export const isApiError = (error: any): error is AxiosError => {
  return error.isAxiosError === true
}

// Retry utility for failed requests
export const retryRequest = async <T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await requestFn()
    } catch (error) {
      lastError = error as Error
      
      if (attempt === maxRetries) {
        throw lastError
      }
      
      // Wait before retry with exponential backoff
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt - 1)))
    }
  }
  
  throw lastError!
}

export default apiClient
