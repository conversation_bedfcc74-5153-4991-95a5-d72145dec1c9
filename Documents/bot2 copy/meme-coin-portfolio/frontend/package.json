{"name": "meme-coin-portfolio-frontend", "version": "1.0.0", "description": "Frontend dashboard for meme coin portfolio management", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^15.0.3", "react": "^18.3.1", "react-dom": "^18.3.1", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-button": "^1.1.0", "@radix-ui/react-card": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.441.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zustand": "^4.5.5", "socket.io-client": "^4.7.5", "axios": "^1.7.7", "recharts": "^2.12.7", "framer-motion": "^11.5.4", "date-fns": "^3.6.0"}, "devDependencies": {"@types/node": "^22.5.4", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "^15.0.3", "postcss": "^8.4.47", "tailwindcss": "^3.4.10", "typescript": "^5.6.2"}, "engines": {"node": ">=18.0.0"}}