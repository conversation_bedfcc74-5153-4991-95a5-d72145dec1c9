/**
 * Trading Interface Component
 * 
 * Comprehensive trading interface for buying and selling tokens:
 * - Token search and selection
 * - Buy/sell forms with real-time quotes
 * - Slippage and advanced settings
 * - Transaction confirmation and status
 * - Integration with exit strategies
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { AlertCircle, TrendingUp, TrendingDown, Search, Settings, Zap } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';

interface TokenInfo {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  logoURI?: string;
  price?: number;
  marketCap?: number;
  volume24h?: number;
  priceChange24h?: number;
  verified?: boolean;
  isMemeCoin?: boolean;
}

interface Quote {
  inAmount: string;
  outAmount: string;
  priceImpactPct: string;
  marketInfos: any[];
}

interface TradeSettings {
  slippage: number;
  priorityFee: number;
  enableExitStrategy: boolean;
  stopLoss: number;
  takeProfit: number[];
  moonBag: number;
}

export default function TradingInterface() {
  const { toast } = useToast();
  
  // State management
  const [activeTab, setActiveTab] = useState<'buy' | 'sell'>('buy');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<TokenInfo[]>([]);
  const [selectedToken, setSelectedToken] = useState<TokenInfo | null>(null);
  const [amount, setAmount] = useState('');
  const [quote, setQuote] = useState<Quote | null>(null);
  const [isLoadingQuote, setIsLoadingQuote] = useState(false);
  const [isExecutingTrade, setIsExecutingTrade] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  // Trading settings
  const [settings, setSettings] = useState<TradeSettings>({
    slippage: 0.5,
    priorityFee: 0.0001,
    enableExitStrategy: true,
    stopLoss: 15,
    takeProfit: [50, 100, 200],
    moonBag: 25
  });

  /**
   * Search for tokens
   */
  const searchTokens = useCallback(async (query: string) => {
    if (!query || query.length < 2) {
      setSearchResults([]);
      return;
    }

    try {
      const response = await fetch(`/api/trade/tokens/suggestions?query=${encodeURIComponent(query)}&limit=10`);
      const tokens = await response.json();
      setSearchResults(tokens);
    } catch (error) {
      console.error('Failed to search tokens:', error);
      toast({
        title: 'Search Error',
        description: 'Failed to search for tokens',
        variant: 'destructive'
      });
    }
  }, [toast]);

  /**
   * Get quote for trade
   */
  const getQuote = useCallback(async () => {
    if (!selectedToken || !amount || parseFloat(amount) <= 0) return;

    setIsLoadingQuote(true);
    try {
      const inputMint = activeTab === 'buy' ? 'So11111111111111111111111111111111111111112' : selectedToken.address;
      const outputMint = activeTab === 'buy' ? selectedToken.address : 'So11111111111111111111111111111111111111112';
      const amountLamports = parseFloat(amount) * Math.pow(10, activeTab === 'buy' ? 9 : selectedToken.decimals);

      const response = await fetch('/api/trade/quote', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          inputMint,
          outputMint,
          amount: amountLamports,
          slippageBps: settings.slippage * 100
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get quote');
      }

      const quoteData = await response.json();
      setQuote(quoteData);
    } catch (error) {
      console.error('Failed to get quote:', error);
      toast({
        title: 'Quote Error',
        description: 'Failed to get price quote',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingQuote(false);
    }
  }, [selectedToken, amount, activeTab, settings.slippage, toast]);

  /**
   * Execute trade
   */
  const executeTrade = async () => {
    if (!selectedToken || !quote || !amount) return;

    setIsExecutingTrade(true);
    try {
      const inputMint = activeTab === 'buy' ? 'So11111111111111111111111111111111111111112' : selectedToken.address;
      const outputMint = activeTab === 'buy' ? selectedToken.address : 'So11111111111111111111111111111111111111112';
      const amountLamports = parseFloat(amount) * Math.pow(10, activeTab === 'buy' ? 9 : selectedToken.decimals);

      const response = await fetch('/api/trade/execute', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          inputMint,
          outputMint,
          amount: amountLamports,
          slippageBps: settings.slippage * 100,
          quote,
          applyExitStrategy: settings.enableExitStrategy && activeTab === 'buy'
        })
      });

      if (!response.ok) {
        throw new Error('Failed to execute trade');
      }

      const result = await response.json();
      
      toast({
        title: 'Trade Submitted',
        description: `${activeTab === 'buy' ? 'Buy' : 'Sell'} order submitted successfully`,
      });

      // Reset form
      setAmount('');
      setQuote(null);
      setSelectedToken(null);
      setSearchQuery('');
      
    } catch (error) {
      console.error('Failed to execute trade:', error);
      toast({
        title: 'Trade Error',
        description: 'Failed to execute trade',
        variant: 'destructive'
      });
    } finally {
      setIsExecutingTrade(false);
    }
  };

  // Auto-search when query changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchTokens(searchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, searchTokens]);

  // Auto-quote when amount changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      getQuote();
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [amount, getQuote]);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Trading Interface
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Buy/Sell Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'buy' | 'sell')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="buy" className="flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Buy
            </TabsTrigger>
            <TabsTrigger value="sell" className="flex items-center gap-2">
              <TrendingDown className="h-4 w-4" />
              Sell
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-4">
            {/* Token Search */}
            <div className="space-y-2">
              <Label>Search Token</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by symbol, name, or address..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              {/* Search Results */}
              {searchResults.length > 0 && (
                <div className="border rounded-md max-h-48 overflow-y-auto">
                  {searchResults.map((token) => (
                    <div
                      key={token.address}
                      className="p-3 hover:bg-muted cursor-pointer border-b last:border-b-0"
                      onClick={() => {
                        setSelectedToken(token);
                        setSearchQuery(token.symbol);
                        setSearchResults([]);
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          {token.logoURI && (
                            <img src={token.logoURI} alt={token.symbol} className="w-8 h-8 rounded-full" />
                          )}
                          <div>
                            <div className="flex items-center gap-2">
                              <span className="font-medium">{token.symbol}</span>
                              {token.verified && <Badge variant="secondary">Verified</Badge>}
                              {token.isMemeCoin && <Badge variant="outline">Meme</Badge>}
                            </div>
                            <p className="text-sm text-muted-foreground">{token.name}</p>
                          </div>
                        </div>
                        {token.price && (
                          <div className="text-right">
                            <p className="font-medium">${token.price.toFixed(8)}</p>
                            {token.priceChange24h && (
                              <p className={`text-sm ${token.priceChange24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {token.priceChange24h >= 0 ? '+' : ''}{token.priceChange24h.toFixed(2)}%
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Selected Token */}
            {selectedToken && (
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    {selectedToken.logoURI && (
                      <img src={selectedToken.logoURI} alt={selectedToken.symbol} className="w-10 h-10 rounded-full" />
                    )}
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{selectedToken.symbol}</h3>
                        {selectedToken.verified && <Badge variant="secondary">Verified</Badge>}
                      </div>
                      <p className="text-sm text-muted-foreground">{selectedToken.name}</p>
                    </div>
                    {selectedToken.price && (
                      <div className="text-right">
                        <p className="font-medium">${selectedToken.price.toFixed(8)}</p>
                        {selectedToken.marketCap && (
                          <p className="text-sm text-muted-foreground">
                            MC: ${(selectedToken.marketCap / 1000000).toFixed(2)}M
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Amount Input */}
            <div className="space-y-2">
              <Label>
                Amount ({activeTab === 'buy' ? 'SOL' : selectedToken?.symbol || 'Token'})
              </Label>
              <Input
                type="number"
                placeholder="0.0"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                step="0.001"
                min="0"
              />
            </div>

            {/* Quote Display */}
            {quote && selectedToken && (
              <Card>
                <CardContent className="p-4 space-y-2">
                  <div className="flex justify-between">
                    <span>You {activeTab === 'buy' ? 'pay' : 'receive'}:</span>
                    <span className="font-medium">
                      {activeTab === 'buy' 
                        ? `${(parseFloat(quote.inAmount) / 1e9).toFixed(4)} SOL`
                        : `${(parseFloat(quote.outAmount) / 1e9).toFixed(4)} SOL`
                      }
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>You {activeTab === 'buy' ? 'receive' : 'pay'}:</span>
                    <span className="font-medium">
                      {activeTab === 'buy'
                        ? `${(parseFloat(quote.outAmount) / Math.pow(10, selectedToken.decimals)).toFixed(2)} ${selectedToken.symbol}`
                        : `${(parseFloat(quote.inAmount) / Math.pow(10, selectedToken.decimals)).toFixed(2)} ${selectedToken.symbol}`
                      }
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Price Impact:</span>
                    <span className={`font-medium ${parseFloat(quote.priceImpactPct) > 5 ? 'text-red-600' : 'text-green-600'}`}>
                      {parseFloat(quote.priceImpactPct).toFixed(2)}%
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Advanced Settings */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Advanced Settings</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowAdvanced(!showAdvanced)}
                >
                  <Settings className="h-4 w-4" />
                </Button>
              </div>

              {showAdvanced && (
                <Card>
                  <CardContent className="p-4 space-y-4">
                    {/* Slippage */}
                    <div className="space-y-2">
                      <Label>Slippage Tolerance: {settings.slippage}%</Label>
                      <Slider
                        value={[settings.slippage]}
                        onValueChange={(value) => setSettings(prev => ({ ...prev, slippage: value[0] }))}
                        max={5}
                        min={0.1}
                        step={0.1}
                      />
                    </div>

                    {/* Exit Strategy (Buy only) */}
                    {activeTab === 'buy' && (
                      <>
                        <div className="flex items-center justify-between">
                          <Label>Enable Exit Strategy</Label>
                          <Switch
                            checked={settings.enableExitStrategy}
                            onCheckedChange={(checked) => setSettings(prev => ({ ...prev, enableExitStrategy: checked }))}
                          />
                        </div>

                        {settings.enableExitStrategy && (
                          <div className="space-y-3 pl-4 border-l-2 border-muted">
                            <div className="space-y-2">
                              <Label>Stop Loss: {settings.stopLoss}%</Label>
                              <Slider
                                value={[settings.stopLoss]}
                                onValueChange={(value) => setSettings(prev => ({ ...prev, stopLoss: value[0] }))}
                                max={50}
                                min={5}
                                step={5}
                              />
                            </div>
                            
                            <div className="space-y-2">
                              <Label>Moon Bag: {settings.moonBag}%</Label>
                              <Slider
                                value={[settings.moonBag]}
                                onValueChange={(value) => setSettings(prev => ({ ...prev, moonBag: value[0] }))}
                                max={50}
                                min={0}
                                step={5}
                              />
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Price Impact Warning */}
            {quote && parseFloat(quote.priceImpactPct) > 5 && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  High price impact ({parseFloat(quote.priceImpactPct).toFixed(2)}%). Consider reducing trade size.
                </AlertDescription>
              </Alert>
            )}

            {/* Execute Button */}
            <Button
              onClick={executeTrade}
              disabled={!selectedToken || !amount || !quote || isExecutingTrade || isLoadingQuote}
              className="w-full"
              size="lg"
            >
              {isExecutingTrade ? 'Executing...' : isLoadingQuote ? 'Getting Quote...' : `${activeTab === 'buy' ? 'Buy' : 'Sell'} ${selectedToken?.symbol || 'Token'}`}
            </Button>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
