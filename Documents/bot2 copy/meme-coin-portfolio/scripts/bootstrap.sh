#!/bin/bash

# Meme Coin Portfolio Bootstrap Script
# One-command setup for development environment

set -e

echo "🚀 Meme Coin Portfolio Bootstrap Script"
echo "======================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    print_success "Docker and Docker Compose are installed"
}

# Check if Node.js is installed (for manual setup)
check_node() {
    if ! command -v node &> /dev/null; then
        print_warning "Node.js is not installed. Docker setup will be used."
        return 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_warning "Node.js version 18+ is recommended. Current version: $(node --version)"
        return 1
    fi
    
    print_success "Node.js $(node --version) is installed"
    return 0
}

# Setup environment file
setup_env() {
    print_status "Setting up environment configuration..."
    
    if [ ! -f .env ]; then
        if [ -f .env.example ]; then
            cp .env.example .env
            print_success "Created .env from .env.example"
        else
            print_error ".env.example not found. Please create environment configuration manually."
            exit 1
        fi
    else
        print_warning ".env already exists. Skipping environment setup."
    fi
    
    print_warning "⚠️  Please edit .env file with your actual configuration:"
    echo "   - WALLET_ADDRESS: Your Solana wallet address"
    echo "   - WALLET_PRIVATE_KEY: Your wallet private key (base64 encoded)"
    echo "   - RPC_URL: Your Helius RPC endpoint"
    echo "   - HELIUS_KEY: Your Helius API key"
    echo ""
}

# Docker setup
setup_docker() {
    print_status "Starting Docker setup..."
    
    # Build and start services
    print_status "Building and starting services with Docker Compose..."
    docker compose up --build -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    
    # Check health
    check_health
}

# Manual setup
setup_manual() {
    print_status "Starting manual setup..."
    
    # Install backend dependencies
    print_status "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    
    # Install frontend dependencies
    print_status "Installing frontend dependencies..."
    cd frontend
    npm install
    cd ..
    
    # Start Redis
    print_status "Starting Redis with Docker..."
    docker run -d --name meme-portfolio-redis -p 6379:6379 redis:7-alpine || print_warning "Redis container may already be running"
    
    print_success "Manual setup completed"
    print_status "To start the application:"
    echo "   1. Start backend: cd backend && npm run dev"
    echo "   2. Start frontend: cd frontend && npm run dev"
}

# Check application health
check_health() {
    print_status "Checking application health..."
    
    # Wait for backend to be ready
    for i in {1..30}; do
        if curl -f http://localhost:4000/health &> /dev/null; then
            print_success "Backend is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "Backend health check failed"
            return 1
        fi
        sleep 2
    done
    
    # Wait for frontend to be ready
    for i in {1..30}; do
        if curl -f http://localhost:3000 &> /dev/null; then
            print_success "Frontend is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            print_error "Frontend health check failed"
            return 1
        fi
        sleep 2
    done
}

# Show final instructions
show_instructions() {
    print_success "🎉 Meme Coin Portfolio setup completed!"
    echo ""
    echo "📊 Access your application:"
    echo "   Frontend Dashboard: http://localhost:3000"
    echo "   Backend API:        http://localhost:4000"
    echo "   Health Check:       http://localhost:4000/health"
    echo ""
    echo "📖 Next steps:"
    echo "   1. Edit .env file with your wallet and API credentials"
    echo "   2. Restart the application: docker compose restart"
    echo "   3. Configure your exit strategy in the dashboard"
    echo "   4. Add tokens to watch in the Dead Hunter panel"
    echo ""
    echo "🔧 Useful commands:"
    echo "   View logs:     docker compose logs -f"
    echo "   Stop services: docker compose down"
    echo "   Restart:       docker compose restart"
    echo "   Update:        docker compose pull && docker compose up --build -d"
    echo ""
    print_warning "⚠️  Remember to configure your wallet credentials before trading!"
}

# Main execution
main() {
    # Check prerequisites
    check_docker
    
    # Setup environment
    setup_env
    
    # Choose setup method
    if [ "$1" = "--manual" ] && check_node; then
        setup_manual
    else
        setup_docker
    fi
    
    # Show final instructions
    show_instructions
}

# Run main function
main "$@"
